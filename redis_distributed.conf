# Redis配置文件 - 针对LstmSync分布式架构优化

# 基本配置
port 6380
bind 127.0.0.1
timeout 300
tcp-keepalive 60

# 内存配置
maxmemory 2gb
maxmemory-policy allkeys-lru

# 持久化配置
save 900 1
save 300 10
save 60 10000
stop-writes-on-bgsave-error yes
rdbcompression yes
rdbchecksum yes
dbfilename dump.rdb

# AOF配置 - 暂时禁用以避免权限问题
appendonly no
appendfilename "appendonly.aof"
appendfsync everysec
no-appendfsync-on-rewrite no
auto-aof-rewrite-percentage 100
auto-aof-rewrite-min-size 64mb

# 网络配置
tcp-backlog 511
maxclients 10000

# 慢查询日志
slowlog-log-slower-than 10000
slowlog-max-len 128

# 客户端输出缓冲区限制
client-output-buffer-limit normal 0 0 0
client-output-buffer-limit replica 256mb 64mb 60
client-output-buffer-limit pubsub 32mb 8mb 60

# 数据库数量
databases 16

# 日志配置
loglevel notice
logfile "/var/log/redis/redis-server.log"

# 工作目录
dir /var/lib/redis

# 安全配置
protected-mode yes

# 性能优化
hash-max-ziplist-entries 512
hash-max-ziplist-value 64
list-max-ziplist-size -2
list-compress-depth 0
set-max-intset-entries 512
zset-max-ziplist-entries 128
zset-max-ziplist-value 64
hll-sparse-max-bytes 3000

# 延迟监控
latency-monitor-threshold 100

# 内存使用优化
activerehashing yes
hz 10

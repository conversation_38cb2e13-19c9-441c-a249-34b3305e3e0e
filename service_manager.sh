#!/bin/bash

# LstmSync服务管理脚本
# 用于启动和关闭API服务和Celery worker

# 配置项 - 分布式版本
CONDA_ENV="lstmsync"
LOG_DIR="./logs"
PID_DIR="./pids"
API_LOG="$LOG_DIR/api_server.log"
WORKER_GPU0_LOG="$LOG_DIR/celery_worker_gpu0.log"
WORKER_GPU1_LOG="$LOG_DIR/celery_worker_gpu1.log"
API_PID="$PID_DIR/api_server.pid"
WORKER_GPU0_PID="$PID_DIR/celery_worker_gpu0.pid"
WORKER_GPU1_PID="$PID_DIR/celery_worker_gpu1.pid"

# 创建日志和PID目录
mkdir -p $LOG_DIR
mkdir -p $PID_DIR

# 激活Conda环境的函数
activate_conda() {
    # 获取conda的shell钩子
    CONDA_BASE=$(conda info --base)
    source "$CONDA_BASE/etc/profile.d/conda.sh"
    # 激活指定环境
    conda activate $CONDA_ENV
    if [ $? -ne 0 ]; then
        echo "无法激活Conda环境: $CONDA_ENV"
        exit 1
    fi
    echo "已激活Conda环境: $CONDA_ENV"
}

# 检查Redis服务状态
check_redis() {
    echo "检查Redis服务状态..."
    if systemctl is-active --quiet redis-server; then
        echo "Redis服务正在运行"
        return 0
    else
        echo "Redis服务未运行"
        return 1
    fi
}

# 启动Redis服务
start_redis() {
    echo "启动Redis服务..."
    if check_redis; then
        echo "Redis服务已经在运行"
    else
        sudo systemctl start redis-server
        if [ $? -eq 0 ]; then
            echo "Redis服务启动成功"
        else
            echo "Redis服务启动失败"
            return 1
        fi
    fi
    return 0
}

# 重启Redis服务
restart_redis() {
    echo "重启Redis服务..."
    sudo systemctl restart redis-server
    if [ $? -eq 0 ]; then
        echo "Redis服务重启成功"
    else
        echo "Redis服务重启失败"
        return 1
    fi
    return 0
}

# 启动服务
start_services() {
    echo "正在启动LstmSync服务..."
    
    # 确保Redis服务正在运行
    if ! start_redis; then
        echo "无法启动Redis服务，服务启动失败"
        return 1
    fi
    
    # 检查服务是否已在运行
    if [ -f "$API_PID" ] && ps -p $(cat "$API_PID") > /dev/null; then
        echo "API服务已经在运行中，PID: $(cat "$API_PID")"
    else
        # 启动API服务
        echo "启动API服务..."
        nohup python api_server.py > "$API_LOG" 2>&1 &
        API_PID_VALUE=$!
        echo $API_PID_VALUE > "$API_PID"
        echo "API服务已启动，PID: $API_PID_VALUE"
    fi

    # 等待Redis完全启动
    echo "等待Redis服务完全启动..."
    for i in {1..10}; do
        if redis-cli -p 6380 ping > /dev/null 2>&1; then
            echo "Redis服务已就绪"
            break
        fi
        echo "等待Redis启动... ($i/10)"
        sleep 1
    done

    # 启动GPU 0 Worker
    if [ -f "$WORKER_GPU0_PID" ] && ps -p $(cat "$WORKER_GPU0_PID") > /dev/null; then
        echo "GPU 0 Celery worker已经在运行中，PID: $(cat "$WORKER_GPU0_PID")"
    else
        echo "启动GPU 0 Celery worker..."
        nohup python worker_gpu0.py > "$WORKER_GPU0_LOG" 2>&1 &
        WORKER_GPU0_PID_VALUE=$!
        echo $WORKER_GPU0_PID_VALUE > "$WORKER_GPU0_PID"
        echo "GPU 0 Celery worker已启动，PID: $WORKER_GPU0_PID_VALUE"
    fi

    # 等待GPU 0 Worker启动
    sleep 2

    # 启动GPU 1 Worker
    if [ -f "$WORKER_GPU1_PID" ] && ps -p $(cat "$WORKER_GPU1_PID") > /dev/null; then
        echo "GPU 1 Celery worker已经在运行中，PID: $(cat "$WORKER_GPU1_PID")"
    else
        echo "启动GPU 1 Celery worker..."
        nohup python worker_gpu1.py > "$WORKER_GPU1_LOG" 2>&1 &
        WORKER_GPU1_PID_VALUE=$!
        echo $WORKER_GPU1_PID_VALUE > "$WORKER_GPU1_PID"
        echo "GPU 1 Celery worker已启动，PID: $WORKER_GPU1_PID_VALUE"
    fi
    
    echo "所有服务已启动。日志文件保存在: $LOG_DIR"
}

# 停止服务
stop_services() {
    echo "正在停止LstmSync服务..."
    
    # 停止API服务
    if [ -f "$API_PID" ]; then
        PID=$(cat "$API_PID")
        if ps -p $PID > /dev/null; then
            echo "停止API服务 (PID: $PID)..."
            kill $PID
            # 等待进程终止
            for i in {1..10}; do
                if ! ps -p $PID > /dev/null; then
                    break
                fi
                sleep 1
            done
            # 如果进程仍在运行，强制终止
            if ps -p $PID > /dev/null; then
                echo "强制终止API服务..."
                kill -9 $PID
            fi
        else
            echo "API服务未运行"
        fi
        rm -f "$API_PID"
    else
        echo "未找到API服务的PID文件"
    fi
    
    # 停止GPU 0 Celery worker
    if [ -f "$WORKER_GPU0_PID" ]; then
        PID=$(cat "$WORKER_GPU0_PID")
        if ps -p $PID > /dev/null; then
            echo "停止GPU 0 Celery worker (PID: $PID)..."
            kill $PID
            # 等待进程终止
            for i in {1..10}; do
                if ! ps -p $PID > /dev/null; then
                    break
                fi
                sleep 1
            done
            # 如果进程仍在运行，强制终止
            if ps -p $PID > /dev/null; then
                echo "强制终止GPU 0 Celery worker..."
                kill -9 $PID
            fi
        else
            echo "GPU 0 Celery worker未运行"
        fi
        rm -f "$WORKER_GPU0_PID"
    else
        echo "未找到GPU 0 Celery worker的PID文件"
    fi

    # 停止GPU 1 Celery worker
    if [ -f "$WORKER_GPU1_PID" ]; then
        PID=$(cat "$WORKER_GPU1_PID")
        if ps -p $PID > /dev/null; then
            echo "停止GPU 1 Celery worker (PID: $PID)..."
            kill $PID
            # 等待进程终止
            for i in {1..10}; do
                if ! ps -p $PID > /dev/null; then
                    break
                fi
                sleep 1
            done
            # 如果进程仍在运行，强制终止
            if ps -p $PID > /dev/null; then
                echo "强制终止GPU 1 Celery worker..."
                kill -9 $PID
            fi
        else
            echo "GPU 1 Celery worker未运行"
        fi
        rm -f "$WORKER_GPU1_PID"
    else
        echo "未找到GPU 1 Celery worker的PID文件"
    fi
    
    echo "所有服务已停止"
}

# 检查服务状态
check_status() {
    echo "检查LstmSync服务状态..."
    
    # 检查Redis服务
    check_redis
    
    # 检查API服务
    if [ -f "$API_PID" ]; then
        PID=$(cat "$API_PID")
        if ps -p $PID > /dev/null; then
            echo "API服务正在运行，PID: $PID"
        else
            echo "API服务未运行（PID文件存在但进程不存在）"
        fi
    else
        echo "API服务未运行（PID文件不存在）"
    fi
    
    # 检查GPU 0 Celery worker
    if [ -f "$WORKER_GPU0_PID" ]; then
        PID=$(cat "$WORKER_GPU0_PID")
        if ps -p $PID > /dev/null; then
            echo "GPU 0 Celery worker正在运行，PID: $PID"
        else
            echo "GPU 0 Celery worker未运行（PID文件存在但进程不存在）"
        fi
    else
        echo "GPU 0 Celery worker未运行（PID文件不存在）"
    fi

    # 检查GPU 1 Celery worker
    if [ -f "$WORKER_GPU1_PID" ]; then
        PID=$(cat "$WORKER_GPU1_PID")
        if ps -p $PID > /dev/null; then
            echo "GPU 1 Celery worker正在运行，PID: $PID"
        else
            echo "GPU 1 Celery worker未运行（PID文件存在但进程不存在）"
        fi
    else
        echo "GPU 1 Celery worker未运行（PID文件不存在）"
    fi
    
    # 检查API服务健康状态
    if [ -f "$API_PID" ] && ps -p $(cat "$API_PID") > /dev/null; then
        echo "检查API服务健康状态..."
        health_check=$(curl -s http://localhost:5028/api/health)
        if [ $? -eq 0 ]; then
            echo "API健康检查结果: $health_check"
        else
            echo "API健康检查失败，无法连接到服务"
        fi
    fi
}

# 显示日志
show_logs() {
    if [ "$1" == "api" ]; then
        tail -f "$API_LOG"
    elif [ "$1" == "worker" ]; then
        tail -f "$WORKER_LOG"
    else
        echo "请指定要查看的日志类型: api 或 worker"
        exit 1
    fi
}

# 主函数
main() {
    case "$1" in
        start)
            activate_conda
            start_services
            ;;
        stop)
            stop_services
            ;;
        restart)
            stop_services
            activate_conda
            start_services
            ;;
        restart-redis)
            restart_redis
            ;;
        status)
            check_status
            ;;
        logs)
            if [ -z "$2" ]; then
                echo "请指定要查看的日志类型: api 或 worker"
                exit 1
            fi
            show_logs "$2"
            ;;
        *)
            echo "用法: $0 {start|stop|restart|restart-redis|status|logs [api|worker]}"
            exit 1
            ;;
    esac
}

# 执行主函数
main "$@" 
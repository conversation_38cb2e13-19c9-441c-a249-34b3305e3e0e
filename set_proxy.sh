#!/bin/bash

# Linux代理配置脚本
# 用于快速配置系统代理设置

echo "=== Linux代理配置脚本 ==="

# 默认代理配置
DEFAULT_PROXY_HOST="127.0.0.1"
DEFAULT_PROXY_PORT="7890"
DEFAULT_NO_PROXY="localhost,127.0.0.1,::1"

# 函数：显示使用帮助
show_help() {
    echo "使用方法:"
    echo "  $0 [选项]"
    echo ""
    echo "选项:"
    echo "  -h, --help              显示此帮助信息"
    echo "  -i, --interactive       交互式配置"
    echo "  -H, --host HOST         代理服务器地址 (默认: $DEFAULT_PROXY_HOST)"
    echo "  -p, --port PORT         代理服务器端口 (默认: $DEFAULT_PROXY_PORT)"
    echo "  -n, --no-proxy HOSTS    不使用代理的地址列表 (默认: $DEFAULT_NO_PROXY)"
    echo "  -c, --config-only       仅配置文件，不设置当前会话"
    echo "  -s, --session-only      仅设置当前会话，不修改配置文件"
    echo ""
    echo "示例:"
    echo "  $0                      # 使用默认配置"
    echo "  $0 -H ************* -p 8080"
    echo "  $0 -i                   # 交互式配置"
}

# 函数：交互式配置
interactive_config() {
    echo "=== 交互式代理配置 ==="
    
    read -p "代理服务器地址 [$DEFAULT_PROXY_HOST]: " PROXY_HOST
    PROXY_HOST=${PROXY_HOST:-$DEFAULT_PROXY_HOST}
    
    read -p "代理服务器端口 [$DEFAULT_PROXY_PORT]: " PROXY_PORT
    PROXY_PORT=${PROXY_PORT:-$DEFAULT_PROXY_PORT}
    
    read -p "不使用代理的地址 [$DEFAULT_NO_PROXY]: " NO_PROXY_HOSTS
    NO_PROXY_HOSTS=${NO_PROXY_HOSTS:-$DEFAULT_NO_PROXY}
    
    echo ""
    echo "配置确认:"
    echo "  代理地址: $PROXY_HOST:$PROXY_PORT"
    echo "  不使用代理: $NO_PROXY_HOSTS"
    echo ""
    read -p "确认配置? (y/N): " confirm
    if [[ ! $confirm =~ ^[Yy]$ ]]; then
        echo "配置已取消"
        exit 0
    fi
}

# 函数：设置当前会话环境变量
set_session_proxy() {
    echo "步骤1: 设置当前会话环境变量"
    
    export http_proxy="http://$PROXY_HOST:$PROXY_PORT"
    export https_proxy="http://$PROXY_HOST:$PROXY_PORT"
    export HTTP_PROXY="http://$PROXY_HOST:$PROXY_PORT"
    export HTTPS_PROXY="http://$PROXY_HOST:$PROXY_PORT"
    export ftp_proxy="http://$PROXY_HOST:$PROXY_PORT"
    export FTP_PROXY="http://$PROXY_HOST:$PROXY_PORT"
    export no_proxy="$NO_PROXY_HOSTS"
    export NO_PROXY="$NO_PROXY_HOSTS"
    
    echo "✓ 当前会话代理环境变量已设置"
}

# 函数：设置systemd用户环境
set_systemd_proxy() {
    echo "步骤2: 设置systemd用户环境"
    
    systemctl --user set-environment http_proxy="http://$PROXY_HOST:$PROXY_PORT" 2>/dev/null
    systemctl --user set-environment https_proxy="http://$PROXY_HOST:$PROXY_PORT" 2>/dev/null
    systemctl --user set-environment HTTP_PROXY="http://$PROXY_HOST:$PROXY_PORT" 2>/dev/null
    systemctl --user set-environment HTTPS_PROXY="http://$PROXY_HOST:$PROXY_PORT" 2>/dev/null
    systemctl --user set-environment ftp_proxy="http://$PROXY_HOST:$PROXY_PORT" 2>/dev/null
    systemctl --user set-environment FTP_PROXY="http://$PROXY_HOST:$PROXY_PORT" 2>/dev/null
    systemctl --user set-environment no_proxy="$NO_PROXY_HOSTS" 2>/dev/null
    systemctl --user set-environment NO_PROXY="$NO_PROXY_HOSTS" 2>/dev/null
    
    systemctl --user daemon-reload 2>/dev/null
    
    echo "✓ systemd用户环境代理已设置"
}

# 函数：配置用户配置文件
set_config_files() {
    echo "步骤3: 配置用户配置文件"
    
    # 配置 ~/.profile
    if [ -f ~/.profile ]; then
        # 先移除已存在的代理配置
        sed -i '/^[[:space:]]*export[[:space:]]*.*proxy.*=/d' ~/.profile
        sed -i '/^[[:space:]]*#.*export[[:space:]]*.*proxy.*=/d' ~/.profile
    fi
    
    # 添加新的代理配置到 ~/.profile
    cat >> ~/.profile << EOF

# Proxy settings - added by set_proxy.sh
export http_proxy=http://$PROXY_HOST:$PROXY_PORT
export https_proxy=http://$PROXY_HOST:$PROXY_PORT
export HTTP_PROXY=http://$PROXY_HOST:$PROXY_PORT
export HTTPS_PROXY=http://$PROXY_HOST:$PROXY_PORT
export ftp_proxy=http://$PROXY_HOST:$PROXY_PORT
export FTP_PROXY=http://$PROXY_HOST:$PROXY_PORT
export no_proxy=$NO_PROXY_HOSTS
export NO_PROXY=$NO_PROXY_HOSTS
EOF
    
    echo "✓ ~/.profile 代理配置已更新"
    
    # 如果存在 ~/.bashrc，也添加配置
    if [ -f ~/.bashrc ]; then
        # 检查是否已有代理配置
        if ! grep -q "# Proxy settings - added by set_proxy.sh" ~/.bashrc; then
            cat >> ~/.bashrc << EOF

# Proxy settings - added by set_proxy.sh
export http_proxy=http://$PROXY_HOST:$PROXY_PORT
export https_proxy=http://$PROXY_HOST:$PROXY_PORT
export HTTP_PROXY=http://$PROXY_HOST:$PROXY_PORT
export HTTPS_PROXY=http://$PROXY_HOST:$PROXY_PORT
export ftp_proxy=http://$PROXY_HOST:$PROXY_PORT
export FTP_PROXY=http://$PROXY_HOST:$PROXY_PORT
export no_proxy=$NO_PROXY_HOSTS
export NO_PROXY=$NO_PROXY_HOSTS
EOF
            echo "✓ ~/.bashrc 代理配置已更新"
        else
            echo "✓ ~/.bashrc 已有代理配置，跳过"
        fi
    fi
}

# 函数：验证代理设置
verify_proxy() {
    echo "步骤4: 验证代理设置"
    
    echo "当前环境变量:"
    env | grep -i proxy | head -4
    
    echo "systemd用户环境:"
    systemctl --user show-environment 2>/dev/null | grep -i proxy | head -4
    
    # 测试网络连接
    echo "步骤5: 测试代理连接"
    if command -v curl >/dev/null 2>&1; then
        echo "测试网络连接（应该通过代理）:"
        if timeout 10 curl -s google.com >/dev/null 2>&1; then
            echo "✓ 网络连接正常，代理配置成功"
        else
            echo "⚠ 网络连接测试失败，请检查代理服务器是否可用"
        fi
    else
        echo "curl命令不可用，跳过网络测试"
    fi
}

# 解析命令行参数
PROXY_HOST="$DEFAULT_PROXY_HOST"
PROXY_PORT="$DEFAULT_PROXY_PORT"
NO_PROXY_HOSTS="$DEFAULT_NO_PROXY"
INTERACTIVE=false
CONFIG_ONLY=false
SESSION_ONLY=false

while [[ $# -gt 0 ]]; do
    case $1 in
        -h|--help)
            show_help
            exit 0
            ;;
        -i|--interactive)
            INTERACTIVE=true
            shift
            ;;
        -H|--host)
            PROXY_HOST="$2"
            shift 2
            ;;
        -p|--port)
            PROXY_PORT="$2"
            shift 2
            ;;
        -n|--no-proxy)
            NO_PROXY_HOSTS="$2"
            shift 2
            ;;
        -c|--config-only)
            CONFIG_ONLY=true
            shift
            ;;
        -s|--session-only)
            SESSION_ONLY=true
            shift
            ;;
        *)
            echo "未知选项: $1"
            show_help
            exit 1
            ;;
    esac
done

# 主程序
echo "开始配置代理..."

# 交互式配置
if [ "$INTERACTIVE" = true ]; then
    interactive_config
fi

# 显示配置信息
echo "代理配置信息:"
echo "  代理地址: $PROXY_HOST:$PROXY_PORT"
echo "  不使用代理: $NO_PROXY_HOSTS"
echo ""

# 执行配置
if [ "$CONFIG_ONLY" = false ]; then
    set_session_proxy
    set_systemd_proxy
fi

if [ "$SESSION_ONLY" = false ]; then
    set_config_files
fi

verify_proxy

echo ""
echo "=== 代理配置完成 ==="
echo "建议重新启动终端或执行 'source ~/.profile' 以确保所有更改生效"

#!/usr/bin/env python3
"""
GPU资源监控工具
用于监控LstmSync系统的GPU内存使用情况和资源泄漏
"""

import subprocess
import json
import time
import requests
import argparse
from typing import Dict, List, Any

def get_nvidia_smi_info() -> Dict[str, Any]:
    """获取nvidia-smi信息"""
    try:
        # 获取GPU基本信息
        cmd = ["nvidia-smi", "--query-gpu=index,name,memory.used,memory.total,utilization.gpu", "--format=csv,noheader,nounits"]
        result = subprocess.run(cmd, capture_output=True, text=True, check=True)
        
        gpu_info = []
        for line in result.stdout.strip().split('\n'):
            if line.strip():
                parts = [p.strip() for p in line.split(',')]
                gpu_info.append({
                    'gpu_id': int(parts[0]),
                    'name': parts[1],
                    'memory_used_mb': int(parts[2]),
                    'memory_total_mb': int(parts[3]),
                    'utilization_percent': int(parts[4]),
                    'memory_usage_percent': round(int(parts[2]) / int(parts[3]) * 100, 2)
                })
        
        # 获取进程信息
        cmd_processes = ["nvidia-smi", "--query-compute-apps=pid,process_name,gpu_uuid,used_memory", "--format=csv,noheader"]
        result_processes = subprocess.run(cmd_processes, capture_output=True, text=True, check=True)
        
        processes = []
        for line in result_processes.stdout.strip().split('\n'):
            if line.strip() and 'No running' not in line:
                parts = [p.strip() for p in line.split(',')]
                if len(parts) >= 4:
                    processes.append({
                        'pid': parts[0],
                        'process_name': parts[1],
                        'gpu_uuid': parts[2],
                        'used_memory_mb': parts[3].replace(' MiB', '')
                    })
        
        return {
            'gpu_info': gpu_info,
            'processes': processes,
            'timestamp': time.time()
        }
    except Exception as e:
        return {'error': str(e), 'timestamp': time.time()}

def get_api_gpu_stats(api_url: str = "http://localhost:5028") -> Dict[str, Any]:
    """获取API的GPU统计信息"""
    try:
        response = requests.get(f"{api_url}/api/gpu/stats", timeout=5)
        response.raise_for_status()
        return response.json()
    except Exception as e:
        return {'error': str(e)}

def get_api_gpu_memory(api_url: str = "http://localhost:5028") -> Dict[str, Any]:
    """获取API的GPU内存信息"""
    try:
        response = requests.get(f"{api_url}/api/gpu/memory", timeout=5)
        response.raise_for_status()
        return response.json()
    except Exception as e:
        return {'error': str(e)}

def cleanup_gpu_resources(api_url: str = "http://localhost:5028") -> Dict[str, Any]:
    """调用API清理GPU资源"""
    try:
        response = requests.post(f"{api_url}/api/gpu/cleanup", timeout=30)
        response.raise_for_status()
        return response.json()
    except Exception as e:
        return {'error': str(e)}

def print_gpu_status(nvidia_info: Dict, api_stats: Dict, api_memory: Dict):
    """打印GPU状态信息"""
    print("=" * 80)
    print(f"GPU监控报告 - {time.strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 80)
    
    # NVIDIA-SMI信息
    if 'gpu_info' in nvidia_info:
        print("\n📊 GPU硬件状态:")
        for gpu in nvidia_info['gpu_info']:
            print(f"  GPU {gpu['gpu_id']}: {gpu['name']}")
            print(f"    内存使用: {gpu['memory_used_mb']}MB / {gpu['memory_total_mb']}MB ({gpu['memory_usage_percent']}%)")
            print(f"    GPU利用率: {gpu['utilization_percent']}%")
    
    # 进程信息
    if 'processes' in nvidia_info and nvidia_info['processes']:
        print("\n🔄 GPU进程:")
        for proc in nvidia_info['processes']:
            print(f"    PID {proc['pid']}: {proc['process_name']} - {proc['used_memory_mb']}MB")
    else:
        print("\n🔄 GPU进程: 无活动进程")
    
    # API统计信息
    if 'error' not in api_stats:
        print(f"\n📈 LstmSync任务统计:")
        print(f"    当前任务: GPU0={api_stats['current_tasks'][0]}, GPU1={api_stats['current_tasks'][1]}")
        print(f"    总任务数: {api_stats['total_current_tasks']}/{api_stats['total_max_tasks']}")
        print(f"    GPU利用率: GPU0={api_stats['gpu_utilization'][0]}%, GPU1={api_stats['gpu_utilization'][1]}%")
    else:
        print(f"\n❌ API统计信息获取失败: {api_stats['error']}")

def monitor_continuous(interval: int = 10, api_url: str = "http://localhost:5028"):
    """连续监控模式"""
    print(f"开始连续监控，间隔{interval}秒...")
    print("按Ctrl+C停止监控")
    
    try:
        while True:
            nvidia_info = get_nvidia_smi_info()
            api_stats = get_api_gpu_stats(api_url)
            api_memory = get_api_gpu_memory(api_url)
            
            print_gpu_status(nvidia_info, api_stats, api_memory)
            
            time.sleep(interval)
    except KeyboardInterrupt:
        print("\n监控已停止")

def main():
    parser = argparse.ArgumentParser(description="LstmSync GPU资源监控工具")
    parser.add_argument("--mode", choices=["once", "monitor", "cleanup"], default="once",
                       help="运行模式: once(单次检查), monitor(连续监控), cleanup(清理资源)")
    parser.add_argument("--interval", type=int, default=10,
                       help="监控间隔(秒), 仅在monitor模式下有效")
    parser.add_argument("--api-url", default="http://localhost:5028",
                       help="LstmSync API地址")
    
    args = parser.parse_args()
    
    if args.mode == "once":
        # 单次检查
        nvidia_info = get_nvidia_smi_info()
        api_stats = get_api_gpu_stats(args.api_url)
        api_memory = get_api_gpu_memory(args.api_url)
        print_gpu_status(nvidia_info, api_stats, api_memory)
        
    elif args.mode == "monitor":
        # 连续监控
        monitor_continuous(args.interval, args.api_url)
        
    elif args.mode == "cleanup":
        # 清理资源
        print("正在清理GPU资源...")
        result = cleanup_gpu_resources(args.api_url)
        if 'error' in result:
            print(f"❌ 清理失败: {result['error']}")
        else:
            print("✅ 清理完成")
            if 'gpu_memory_after_cleanup' in result:
                print("清理后的GPU内存状态:")
                for gpu_info in result['gpu_memory_after_cleanup']:
                    print(f"  GPU {gpu_info['gpu_id']}: {gpu_info['memory_used']}MB / {gpu_info['memory_total']}MB")

if __name__ == "__main__":
    main()

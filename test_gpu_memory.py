#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import torch
import psutil
import subprocess
import time
import lstmsync_func

def get_gpu_memory_info():
    """获取GPU内存信息"""
    try:
        result = subprocess.run(['nvidia-smi', '--query-gpu=memory.used,memory.total', '--format=csv,nounits,noheader'], 
                              capture_output=True, text=True)
        lines = result.stdout.strip().split('\n')
        gpu_info = []
        for i, line in enumerate(lines):
            used, total = map(int, line.split(', '))
            gpu_info.append({
                'gpu_id': i,
                'used_mb': used,
                'total_mb': total,
                'free_mb': total - used,
                'usage_percent': (used / total) * 100
            })
        return gpu_info
    except Exception as e:
        print(f"获取GPU信息失败: {e}")
        return []

def test_model_memory_usage(gpu_id=0):
    """测试模型在指定GPU上的内存占用"""
    print(f"\n=== 测试GPU {gpu_id}上的模型内存占用 ===")
    
    # 设置GPU
    os.environ['CUDA_VISIBLE_DEVICES'] = str(gpu_id)
    
    # 获取初始GPU内存
    initial_memory = get_gpu_memory_info()
    if initial_memory:
        print(f"初始GPU内存使用: {initial_memory[0]['used_mb']} MB / {initial_memory[0]['total_mb']} MB")
    
    try:
        # 创建LstmSync实例
        print("正在加载LstmSync模型...")
        start_time = time.time()
        
        lstmsync = lstmsync_func.LstmSync(
            batch_size=4,
            sync_offset=0,
            scale_h=1.5,
            scale_w=3,
            key_file="./key.txt"
        )
        
        load_time = time.time() - start_time
        print(f"模型加载时间: {load_time:.2f} 秒")
        
        # 获取加载后的GPU内存
        after_load_memory = get_gpu_memory_info()
        if after_load_memory:
            memory_used = after_load_memory[0]['used_mb'] - initial_memory[0]['used_mb']
            print(f"模型加载后GPU内存使用: {after_load_memory[0]['used_mb']} MB / {after_load_memory[0]['total_mb']} MB")
            print(f"模型占用内存: {memory_used} MB")
            
            # 计算可用内存和理论并发数
            available_memory = after_load_memory[0]['free_mb']
            print(f"剩余可用内存: {available_memory} MB")
            
            # 估算单个任务的额外内存需求（视频处理缓冲区等）
            estimated_task_memory = 2048  # 2GB per task (估算)
            max_concurrent_tasks = available_memory // estimated_task_memory
            print(f"估算最大并发任务数: {max_concurrent_tasks} (假设每任务需要{estimated_task_memory}MB)")
        
        return {
            'gpu_id': gpu_id,
            'model_memory_mb': memory_used if after_load_memory else 0,
            'load_time_seconds': load_time,
            'available_memory_mb': available_memory if after_load_memory else 0,
            'estimated_max_tasks': max_concurrent_tasks if after_load_memory else 0
        }
        
    except Exception as e:
        print(f"测试失败: {e}")
        return None

def main():
    print("LstmSync GPU内存占用测试")
    print("=" * 50)
    
    # 获取初始GPU状态
    gpu_info = get_gpu_memory_info()
    print(f"检测到 {len(gpu_info)} 张GPU:")
    for info in gpu_info:
        print(f"  GPU {info['gpu_id']}: {info['used_mb']}/{info['total_mb']} MB ({info['usage_percent']:.1f}%)")
    
    # 测试每张GPU
    results = []
    for i in range(len(gpu_info)):
        result = test_model_memory_usage(i)
        if result:
            results.append(result)
        time.sleep(2)  # 等待GPU内存释放
    
    # 总结报告
    print("\n" + "=" * 50)
    print("测试总结:")
    total_estimated_tasks = 0
    for result in results:
        print(f"GPU {result['gpu_id']}:")
        print(f"  - 模型内存占用: {result['model_memory_mb']} MB")
        print(f"  - 加载时间: {result['load_time_seconds']:.2f} 秒")
        print(f"  - 可用内存: {result['available_memory_mb']} MB")
        print(f"  - 估算最大任务数: {result['estimated_max_tasks']}")
        total_estimated_tasks += result['estimated_max_tasks']
    
    print(f"\n总估算并发能力: {total_estimated_tasks} 个任务")
    print(f"推荐配置: 每GPU {min([r['estimated_max_tasks'] for r in results])} 个worker")

if __name__ == "__main__":
    main()

#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import time
import json
import requests
import argparse

# API服务器地址
API_BASE_URL = "http://localhost:5028"

def create_task(video_url, audio_url):
    """创建新任务"""
    url = f"{API_BASE_URL}/api/tasks"
    payload = {
        "video_url": video_url,
        "audio_url": audio_url
    }
    
    try:
        response = requests.post(url, json=payload)
        response.raise_for_status()
        return response.json()
    except requests.exceptions.RequestException as e:
        print(f"创建任务失败: {str(e)}")
        if hasattr(e, 'response') and e.response:
            print(f"响应内容: {e.response.text}")
        return None

def get_task_status(task_id):
    """获取任务状态"""
    url = f"{API_BASE_URL}/api/tasks/{task_id}"
    
    try:
        response = requests.get(url)
        response.raise_for_status()
        return response.json()
    except requests.exceptions.RequestException as e:
        print(f"获取任务状态失败: {str(e)}")
        if hasattr(e, 'response') and e.response:
            print(f"响应内容: {e.response.text}")
        return None

def check_health():
    """检查API服务健康状态"""
    url = f"{API_BASE_URL}/api/health"
    
    try:
        response = requests.get(url)
        response.raise_for_status()
        return response.json()
    except requests.exceptions.RequestException as e:
        print(f"健康检查失败: {str(e)}")
        return None

def monitor_task(task_id, interval=5, timeout=3600):
    """监控任务状态直到完成或超时"""
    print(f"开始监控任务 {task_id}")
    start_time = time.time()
    
    while time.time() - start_time < timeout:
        status = get_task_status(task_id)
        if not status:
            print("无法获取任务状态，请检查API服务是否正常运行")
            return False
            
        print(f"任务状态: {status['status']}, 消息: {status['message']}")
        
        if status['status'] == 'completed':
            print(f"任务完成! 结果URL: {status['result_url']}")
            return True
        elif status['status'] == 'failed':
            print(f"任务失败: {status['message']}")
            return False
            
        print(f"等待 {interval} 秒后重新检查...")
        time.sleep(interval)
    
    print(f"监控超时，任务可能仍在处理中")
    return False

def main():
    parser = argparse.ArgumentParser(description='LstmSync API测试工具')
    subparsers = parser.add_subparsers(dest='command', help='子命令')
    
    # 创建任务命令
    create_parser = subparsers.add_parser('create', help='创建新任务')
    create_parser.add_argument('--video', required=True, help='视频URL')
    create_parser.add_argument('--audio', required=True, help='音频URL')
    
    # 查询任务状态命令
    status_parser = subparsers.add_parser('status', help='查询任务状态')
    status_parser.add_argument('--task-id', required=True, help='任务ID')
    
    # 监控任务命令
    monitor_parser = subparsers.add_parser('monitor', help='监控任务状态直到完成')
    monitor_parser.add_argument('--task-id', required=True, help='任务ID')
    monitor_parser.add_argument('--interval', type=int, default=5, help='检查间隔(秒)')
    monitor_parser.add_argument('--timeout', type=int, default=3600, help='超时时间(秒)')
    
    # 健康检查命令
    subparsers.add_parser('health', help='检查API服务健康状态')
    
    args = parser.parse_args()
    
    if args.command == 'create':
        result = create_task(args.video, args.audio)
        if result:
            print(json.dumps(result, indent=2))
            print(f"\n要监控此任务，请运行: python {sys.argv[0]} monitor --task-id {result['task_id']}")
    
    elif args.command == 'status':
        result = get_task_status(args.task_id)
        if result:
            print(json.dumps(result, indent=2))
    
    elif args.command == 'monitor':
        monitor_task(args.task_id, args.interval, args.timeout)
    
    elif args.command == 'health':
        result = check_health()
        if result:
            print(json.dumps(result, indent=2))
    
    else:
        parser.print_help()

if __name__ == "__main__":
    main() 
#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import sys
import argparse
import requests
from typing import Optional
import socket
from urllib.parse import urlparse


def check_proxy_availability(proxy_url: str, timeout: int = 3) -> bool:
    """检查代理是否可用"""
    try:
        parsed = urlparse(proxy_url)
        host = parsed.hostname
        port = parsed.port

        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(timeout)
        result = sock.connect_ex((host, port))
        sock.close()
        return result == 0
    except Exception:
        return False

def get_smart_proxies(url: str) -> dict:
    """智能获取代理配置"""
    # 直连域名列表（不使用代理）
    direct_domains = [
        '**************',  # 内网地址
        'localhost',
        '127.0.0.1'
    ]

    # 检查是否为直连域名
    parsed_url = urlparse(url)
    domain = parsed_url.hostname

    if any(direct_domain in domain for direct_domain in direct_domains):
        print(f"[PROXY] 域名 {domain} 在直连列表中，跳过代理")
        # 返回明确的空代理配置，覆盖环境变量
        return {'http': None, 'https': None}

    # 检查系统代理配置
    http_proxy = os.environ.get('http_proxy') or os.environ.get('HTTP_PROXY')
    https_proxy = os.environ.get('https_proxy') or os.environ.get('HTTPS_PROXY')

    proxies = {}

    # 检查HTTP代理
    if http_proxy and check_proxy_availability(http_proxy):
        proxies['http'] = http_proxy
        print(f"[PROXY] HTTP代理可用: {http_proxy}")
    elif http_proxy:
        print(f"[PROXY] HTTP代理不可用，跳过: {http_proxy}")
        proxies['http'] = None  # 明确禁用代理

    # 检查HTTPS代理
    if https_proxy and check_proxy_availability(https_proxy):
        proxies['https'] = https_proxy
        print(f"[PROXY] HTTPS代理可用: {https_proxy}")
    elif https_proxy:
        print(f"[PROXY] HTTPS代理不可用，跳过: {https_proxy}")
        proxies['https'] = None  # 明确禁用代理

    return proxies

def upload_to_minio(file_path: str, biz_path: Optional[str] = None) -> str:
    """
    上传文件到Minio对象存储并返回URL
    
    参数:
        file_path: 要上传的文件路径
        biz_path: 业务路径，如不提供则默认为'video'
        
    返回:
        str: 上传成功后的文件URL
    """
    try:
        url = "http://**************:9033/cdu/sys/upload/uploadMinio"
        
        # 检查文件是否存在
        if not os.path.exists(file_path):
            raise FileNotFoundError(f"文件不存在: {file_path}")
            
        # 根据API提供方的要求，所有文件类型都使用'video'作为bizPath
        if biz_path is None:
            biz_path = 'video'
        
        print(f"正在上传文件: {file_path}")
        print(f"业务路径: {biz_path}")
        
        # 准备 multipart/form-data 请求
        with open(file_path, 'rb') as f:
            files = {
                'file': (os.path.basename(file_path), f, 'application/octet-stream')
            }
            
            params = {
                'bizPath': biz_path
            }
            
            headers = {
                'X-Access-Token': 'global'
            }
            
            # 获取智能代理配置
            proxies = get_smart_proxies(url)

            if proxies:
                print(f"[UPLOAD] 使用代理: {proxies}")
            else:
                print(f"[UPLOAD] 直接连接（无代理）")

            # 发送请求
            response = requests.post(
                url,
                files=files,
                params=params,
                headers=headers,
                proxies=proxies,
                timeout=120  # 增加超时时间到2分钟，大文件可能需要更长时间
            )
            
            if response.status_code == 200:
                result = response.json()
                
                if result.get('errno') == 0 and 'data' in result:
                    file_url = result['data'].get('url')
                    if file_url:
                        print(f"上传成功! URL: {file_url}")
                        return file_url
                    else:
                        raise ValueError("响应中没有URL信息")
                else:
                    error_msg = result.get('message', '未知错误')
                    print(f"服务器返回错误: {error_msg}")
                    print(f"完整响应: {result}")
                    raise ValueError(f"上传失败: {error_msg}")
            else:
                print(f"HTTP请求失败，状态码: {response.status_code}")
                print(f"响应内容: {response.text}")
                raise ValueError(f"请求失败，状态码: {response.status_code}, 响应: {response.text}")
                
    except Exception as e:
        print(f"上传文件到Minio时出错: {str(e)}")
        raise


def main():
    """主函数，处理命令行参数并执行上传"""
    parser = argparse.ArgumentParser(description='上传文件到Minio对象存储')
    parser.add_argument('file_path', help='要上传的文件路径')
    parser.add_argument('--biz-path', '-b', help='业务路径 (默认为video)')
    parser.add_argument('--verbose', '-v', action='store_true', help='显示详细信息')
    
    args = parser.parse_args()
    
    try:
        file_url = upload_to_minio(args.file_path, args.biz_path)
        print(f"上传成功！")
        print(f"文件URL: {file_url}")
        return 0
    except Exception as e:
        print(f"上传失败: {str(e)}")
        return 1


if __name__ == "__main__":
    sys.exit(main()) 
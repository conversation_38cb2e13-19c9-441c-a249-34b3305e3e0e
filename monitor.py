#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import time
import json
import requests
import subprocess
import psutil
from datetime import datetime
import redis

class LstmSyncMonitor:
    def __init__(self):
        self.api_url = "http://localhost:5028"
        self.redis_client = redis.Redis(host='localhost', port=6379, db=2, decode_responses=True)
        
    def get_gpu_info(self):
        """获取GPU信息"""
        try:
            result = subprocess.run(['nvidia-smi', '--query-gpu=index,name,memory.used,memory.total,utilization.gpu,temperature.gpu', '--format=csv,noheader,nounits'], 
                                  capture_output=True, text=True, timeout=10)
            gpu_info = []
            for line in result.stdout.strip().split('\n'):
                if line:
                    parts = [p.strip() for p in line.split(', ')]
                    gpu_info.append({
                        'gpu_id': int(parts[0]),
                        'name': parts[1],
                        'memory_used': int(parts[2]),
                        'memory_total': int(parts[3]),
                        'memory_free': int(parts[3]) - int(parts[2]),
                        'utilization': int(parts[4]),
                        'temperature': int(parts[5])
                    })
            return gpu_info
        except Exception as e:
            print(f"获取GPU信息失败: {e}")
            return []
    
    def get_system_info(self):
        """获取系统信息"""
        return {
            'cpu_percent': psutil.cpu_percent(interval=1),
            'memory': psutil.virtual_memory()._asdict(),
            'disk': psutil.disk_usage('/')._asdict(),
            'load_avg': psutil.getloadavg() if hasattr(psutil, 'getloadavg') else None
        }
    
    def get_api_stats(self):
        """获取API统计信息"""
        try:
            # 健康检查
            health_response = requests.get(f"{self.api_url}/api/health", timeout=5)
            health_data = health_response.json()
            
            # GPU统计
            gpu_stats_response = requests.get(f"{self.api_url}/api/gpu/stats", timeout=5)
            gpu_stats = gpu_stats_response.json()
            
            # GPU内存
            gpu_memory_response = requests.get(f"{self.api_url}/api/gpu/memory", timeout=5)
            gpu_memory = gpu_memory_response.json()
            
            return {
                'health': health_data,
                'gpu_stats': gpu_stats,
                'gpu_memory': gpu_memory,
                'api_available': True
            }
        except Exception as e:
            return {
                'api_available': False,
                'error': str(e)
            }
    
    def get_redis_info(self):
        """获取Redis信息"""
        try:
            info = self.redis_client.info()
            return {
                'connected_clients': info.get('connected_clients', 0),
                'used_memory': info.get('used_memory', 0),
                'used_memory_human': info.get('used_memory_human', '0B'),
                'keyspace_hits': info.get('keyspace_hits', 0),
                'keyspace_misses': info.get('keyspace_misses', 0),
                'total_commands_processed': info.get('total_commands_processed', 0),
                'redis_available': True
            }
        except Exception as e:
            return {
                'redis_available': False,
                'error': str(e)
            }
    
    def collect_metrics(self):
        """收集所有监控指标"""
        timestamp = datetime.now().isoformat()
        
        metrics = {
            'timestamp': timestamp,
            'gpu_info': self.get_gpu_info(),
            'system_info': self.get_system_info(),
            'api_stats': self.get_api_stats(),
            'redis_info': self.get_redis_info()
        }
        
        return metrics
    
    def print_metrics(self, metrics):
        """打印监控指标"""
        print(f"\n=== LstmSync 分布式监控 - {metrics['timestamp']} ===")
        
        # GPU信息
        print("\n🖥️  GPU状态:")
        for gpu in metrics['gpu_info']:
            print(f"  GPU {gpu['gpu_id']}: {gpu['name']}")
            print(f"    内存: {gpu['memory_used']}/{gpu['memory_total']} MB ({gpu['memory_used']/gpu['memory_total']*100:.1f}%)")
            print(f"    利用率: {gpu['utilization']}%")
            print(f"    温度: {gpu['temperature']}°C")
        
        # API状态
        api_stats = metrics['api_stats']
        if api_stats['api_available']:
            print(f"\n🌐 API状态: ✅ 正常")
            gpu_stats = api_stats['gpu_stats']
            print(f"  当前任务: {gpu_stats['total_current_tasks']}/{gpu_stats['total_max_tasks']}")
            print(f"  GPU 0: {gpu_stats['current_tasks'][0]}/{gpu_stats['max_tasks_per_gpu']} ({gpu_stats['gpu_utilization'][0]:.1f}%)")
            print(f"  GPU 1: {gpu_stats['current_tasks'][1]}/{gpu_stats['max_tasks_per_gpu']} ({gpu_stats['gpu_utilization'][1]:.1f}%)")
        else:
            print(f"\n🌐 API状态: ❌ 不可用 - {api_stats.get('error', '未知错误')}")
        
        # Redis状态
        redis_info = metrics['redis_info']
        if redis_info['redis_available']:
            print(f"\n📊 Redis状态: ✅ 正常")
            print(f"  连接数: {redis_info['connected_clients']}")
            print(f"  内存使用: {redis_info['used_memory_human']}")
            print(f"  命令处理: {redis_info['total_commands_processed']}")
        else:
            print(f"\n📊 Redis状态: ❌ 不可用 - {redis_info.get('error', '未知错误')}")
        
        # 系统信息
        sys_info = metrics['system_info']
        print(f"\n💻 系统状态:")
        print(f"  CPU使用率: {sys_info['cpu_percent']:.1f}%")
        print(f"  内存使用: {sys_info['memory']['percent']:.1f}%")
        print(f"  磁盘使用: {sys_info['disk']['percent']:.1f}%")
    
    def run_continuous_monitoring(self, interval=30):
        """持续监控"""
        print("开始LstmSync分布式系统监控...")
        print(f"监控间隔: {interval}秒")
        print("按 Ctrl+C 停止监控")
        
        try:
            while True:
                metrics = self.collect_metrics()
                self.print_metrics(metrics)
                time.sleep(interval)
        except KeyboardInterrupt:
            print("\n监控已停止")

if __name__ == "__main__":
    import sys
    
    monitor = LstmSyncMonitor()
    
    if len(sys.argv) > 1 and sys.argv[1] == "--once":
        # 单次监控
        metrics = monitor.collect_metrics()
        monitor.print_metrics(metrics)
    else:
        # 持续监控
        interval = int(sys.argv[1]) if len(sys.argv) > 1 else 30
        monitor.run_continuous_monitoring(interval)

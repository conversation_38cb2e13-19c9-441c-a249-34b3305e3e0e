# LstmSync 分布式架构用户手册

<div align="center">
<h2>无需网络，本地双GPU即可使用的数字人泛化模型分布式架构</h2>
<p>支持16个并发任务，智能GPU调度，企业级性能监控</p>
</div>

## 📋 目录

- [快速开始](#快速开始)
- [系统要求](#系统要求)
- [详细部署](#详细部署)
- [服务管理](#服务管理)
- [API使用说明](#api使用说明)
- [性能监控](#性能监控)
- [故障排除](#故障排除)
- [配置优化](#配置优化)
- [维护指南](#维护指南)

---

## 🚀 快速开始

### 一键启动（推荐）

```bash
# 确保脚本可执行
chmod +x quick_start.sh

# 一键启动分布式架构
./quick_start.sh
```

### 手动启动

```bash
# 启动所有服务
./service_manager.sh start

# 检查服务状态
./service_manager.sh status

# 验证API健康状态
curl http://localhost:5028/api/health
```

### 验证部署

```bash
# 运行分布式架构测试
python test_distributed.py

# 查看GPU统计信息
curl http://localhost:5028/api/gpu/stats
```

---

## 💻 系统要求

### 硬件要求
- **GPU**: 2x NVIDIA GeForce RTX 4090 (48GB显存)
- **内存**: 至少32GB系统内存
- **存储**: 高速SSD存储，至少100GB可用空间
- **网络**: 稳定的网络连接（用于文件下载）

### 软件要求
- **操作系统**: Linux (推荐Ubuntu 20.04+)
- **Python**: 3.8+
- **CUDA**: 12.4+
- **Redis**: 6.0+
- **FFmpeg**: 4.0+

### 环境验证

```bash
# 检查GPU环境
nvidia-smi
python -c "import torch; print(f'CUDA available: {torch.cuda.is_available()}, GPU count: {torch.cuda.device_count()}')"

# 检查Redis服务
redis-cli -p 6380 ping

# 检查FFmpeg
ffmpeg -version
```

---

## 🔧 详细部署

### 1. 环境准备

```bash
# 创建Conda环境
conda create -n lstmsync python=3.8
conda activate lstmsync

# 安装FFmpeg
apt-get install -y software-properties-common
wget https://johnvansickle.com/ffmpeg/releases/ffmpeg-release-amd64-static.tar.xz
tar -xf ffmpeg-release-amd64-static.tar.xz
mv ffmpeg-*-amd64-static/ffmpeg ffmpeg-*-amd64-static/ffprobe /usr/local/bin/
rm -rf ffmpeg-release-amd64-static.tar.xz ffmpeg-*-amd64-static

# 安装PyTorch (CUDA 12.1)
pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu121
```

### 2. Redis配置优化

```bash
# 安装Redis
apt-get install -y redis-server

# 使用优化的Redis配置
sudo cp redis_distributed.conf /etc/redis/redis.conf

# 启动Redis服务
sudo systemctl start redis-server
sudo systemctl enable redis-server

# 验证Redis连接
redis-cli -p 6380 ping
```

### 3. 项目依赖安装

```bash
# 安装项目依赖
pip install -r requirements.txt

# 验证关键依赖
python -c "import celery, fastapi, redis, torch; print('所有依赖安装成功')"
```

### 4. 模型文件准备

确保以下文件存在：
- `key.txt` - 授权密钥文件
- `checkpoints/192jm.pth` - 同步权重文件
- `checkpoints/chinese-hubert-large/` - 音频权重目录
- `lstmsync_func.cpython-38-x86_64-linux-gnu.so` - 编译模块

### 5. 目录结构创建

```bash
# 创建必要目录
mkdir -p logs pids

# 设置权限
chmod +x service_manager.sh
chmod +x quick_start.sh
```

---

## 🛠️ 服务管理

### 基本操作

```bash
# 启动所有服务
./service_manager.sh start

# 停止所有服务
./service_manager.sh stop

# 重启所有服务
./service_manager.sh restart

# 查看服务状态
./service_manager.sh status
```

### 查看日志

```bash
# 查看API服务日志
./service_manager.sh logs api

# 查看GPU 0 Worker日志
./service_manager.sh logs worker_gpu0

# 查看GPU 1 Worker日志
./service_manager.sh logs worker_gpu1

# 实时监控所有日志
tail -f logs/*.log
```

### Redis管理

```bash
# 重启Redis服务
./service_manager.sh restart-redis

# 检查Redis状态
./service_manager.sh redis-status

# 清理Redis数据（谨慎使用）
redis-cli -p 6380 FLUSHALL
```

---

## 🌐 API使用说明

### 基础接口

#### 创建任务

```bash
POST /api/tasks
Content-Type: application/json

{
  "video_url": "https://example.com/video.mp4",
  "audio_url": "https://example.com/audio.wav",
  "priority": 0  # 可选，优先级（数字越大优先级越高）
}
```

**响应示例**：
```json
{
  "task_id": "d1311191-1852-4441-a8b4-b896641e9693",
  "status": "success",
  "estimated_gpu": 0,
  "queue_position": 1
}
```

#### 查询任务状态

```bash
GET /api/tasks/{task_id}
```

**响应示例**：
```json
{
  "status": "completed",
  "result_url": "https://minio.example.com/result.mp4",
  "message": "任务在GPU 0上处理完成",
  "gpu_id": 0,
  "processing_time": 45.2
}
```

### 监控接口

#### 健康检查

```bash
GET /api/health
```

#### GPU统计信息

```bash
GET /api/gpu/stats
```

#### GPU内存信息

```bash
GET /api/gpu/memory
```

### 任务状态说明

- `pending`: 任务已创建，等待处理
- `processing`: 任务正在处理中
- `completed`: 任务处理完成
- `failed`: 任务处理失败

### 使用示例

```bash
# 创建任务
TASK_ID=$(curl -s -X POST http://localhost:5028/api/tasks \
  -H 'Content-Type: application/json' \
  -d '{"video_url": "https://example.com/video.mp4", "audio_url": "https://example.com/audio.wav"}' \
  | jq -r '.task_id')

# 查询任务状态
curl http://localhost:5028/api/tasks/$TASK_ID

# 监控GPU状态
curl http://localhost:5028/api/gpu/stats
```

---

## 📊 性能监控

### 实时监控

```bash
# 持续监控（30秒间隔）
python monitor.py

# 单次监控
python monitor.py --once

# 自定义间隔（10秒）
python monitor.py 10
```

### 性能测试

```bash
# 基础性能测试
python performance_test.py \
  --tasks 10 \
  --concurrent 8 \
  --video-url "https://example.com/test.mp4" \
  --audio-url "https://example.com/test.wav"

# 压力测试
python performance_test.py \
  --tasks 50 \
  --concurrent 16 \
  --video-url "https://example.com/test.mp4" \
  --audio-url "https://example.com/test.wav"
```

### 架构验证

```bash
# 运行完整的分布式架构测试
python test_distributed.py
```

### 监控指标

- **GPU利用率**: 实时GPU使用情况
- **内存使用**: GPU和系统内存使用率
- **任务队列**: 当前队列长度和处理速度
- **系统资源**: CPU、内存、磁盘使用率
- **网络状态**: Redis连接和API响应时间

---

## 🔧 故障排除

### 常见问题及解决方案

#### 1. Redis连接失败

**症状**: Celery Worker显示 "Error 111 connecting to localhost:6380. Connection refused"

**解决方案**:
```bash
# 检查Redis状态
sudo systemctl status redis-server

# 重启Redis服务
sudo systemctl restart redis-server

# 验证连接
redis-cli -p 6380 ping

# 如果仍有问题，检查Redis配置
sudo nano /etc/redis/redis.conf
```

#### 2. GPU内存不足

**症状**: CUDA out of memory错误

**解决方案**:
```bash
# 检查GPU内存使用
nvidia-smi

# 减少并发数（修改api_server.py）
MAX_TASKS_PER_GPU = 6  # 从8减少到6

# 重启服务
./service_manager.sh restart
```

#### 3. Worker无法启动

**症状**: Worker进程启动失败或立即退出

**解决方案**:
```bash
# 检查Worker日志
tail -f logs/celery_worker_gpu0.log
tail -f logs/celery_worker_gpu1.log

# 检查CUDA环境
echo $CUDA_VISIBLE_DEVICES
python -c "import torch; print(torch.cuda.is_available())"

# 手动启动Worker进行调试
python worker_gpu0.py
```

#### 4. 任务状态不更新

**症状**: 任务完成但状态仍显示"pending"

**解决方案**:
```bash
# 重启所有服务
./service_manager.sh restart

# 检查Redis数据
redis-cli -p 6380 keys "*"

# 运行健康检查
curl http://localhost:5028/api/health
```

#### 5. API服务无响应

**症状**: API请求超时或连接失败

**解决方案**:
```bash
# 检查API服务状态
./service_manager.sh status

# 查看API日志
./service_manager.sh logs api

# 检查端口占用
netstat -tlnp | grep 5028

# 重启API服务
./service_manager.sh restart
```

### 诊断工具

```bash
# 运行完整诊断
python test_distributed.py

# 检查系统资源
python monitor.py --once

# 验证API健康状态
python test_api.py health
```

### 日志文件位置

- API服务: `logs/api_server.log`
- GPU 0 Worker: `logs/celery_worker_gpu0.log`
- GPU 1 Worker: `logs/celery_worker_gpu1.log`
- 系统日志: `/var/log/redis/redis-server.log`

---

## ⚙️ 配置优化

### 性能参数调优

#### GPU并发数调整

```python
# 在api_server.py中修改
MAX_TASKS_PER_GPU = 8  # 根据GPU显存调整

# 保守设置（显存不足时）
MAX_TASKS_PER_GPU = 6

# 激进设置（显存充足时）
MAX_TASKS_PER_GPU = 10
```

#### Redis配置优化

```bash
# 编辑Redis配置
sudo nano /etc/redis/redis.conf

# 关键配置项
maxmemory 8gb
maxmemory-policy allkeys-lru
tcp-keepalive 300
timeout 0
```

#### 系统优化

```bash
# 增加文件描述符限制
ulimit -n 65536

# 优化网络参数
echo 'net.core.somaxconn = 65535' >> /etc/sysctl.conf
sysctl -p
```

### 环境变量配置

```bash
# 设置CUDA设备可见性
export CUDA_VISIBLE_DEVICES=0,1

# 设置Redis连接
export REDIS_URL=redis://localhost:6380/0

# 设置日志级别
export LOG_LEVEL=INFO
```

### 生产环境配置

#### 使用Supervisor管理进程

```bash
# 安装Supervisor
apt-get install supervisor

# 配置文件示例
sudo nano /etc/supervisor/conf.d/lstmsync.conf
```

#### Nginx反向代理

```nginx
server {
    listen 80;
    server_name your-domain.com;

    location / {
        proxy_pass http://localhost:5028;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
}
```

---

## 🔒 维护指南

### 日常维护

#### 定期检查

```bash
# 每日检查脚本
#!/bin/bash
echo "=== LstmSync 日常检查 ==="
./service_manager.sh status
python monitor.py --once
df -h  # 检查磁盘空间
```

#### 日志清理

```bash
# 清理旧日志（保留最近7天）
find logs/ -name "*.log" -mtime +7 -delete

# 轮转日志
logrotate -f /etc/logrotate.d/lstmsync
```

#### 性能监控

```bash
# 定期性能测试
python performance_test.py --tasks 5 --concurrent 4 \
  --video-url "https://example.com/test.mp4" \
  --audio-url "https://example.com/test.wav"
```

### 备份策略

#### Redis数据备份

```bash
# 手动备份
redis-cli -p 6380 BGSAVE

# 自动备份脚本
#!/bin/bash
DATE=$(date +%Y%m%d_%H%M%S)
redis-cli -p 6380 BGSAVE
cp /var/lib/redis/dump.rdb /backup/redis_$DATE.rdb
```

#### 配置文件备份

```bash
# 备份关键配置
tar -czf backup_$(date +%Y%m%d).tar.gz \
  api_server.py \
  worker_gpu*.py \
  service_manager.sh \
  redis_distributed.conf \
  requirements.txt
```

### 更新升级

#### 依赖更新

```bash
# 更新Python依赖
pip install -r requirements.txt --upgrade

# 检查兼容性
python test_distributed.py
```

#### 系统更新

```bash
# 更新系统包
apt-get update && apt-get upgrade

# 更新NVIDIA驱动（谨慎操作）
ubuntu-drivers autoinstall
```

### 监控告警

#### 设置监控脚本

```bash
#!/bin/bash
# 监控脚本示例
GPU_USAGE=$(nvidia-smi --query-gpu=utilization.gpu --format=csv,noheader,nounits | head -1)
if [ $GPU_USAGE -gt 90 ]; then
    echo "GPU使用率过高: $GPU_USAGE%" | mail -s "LstmSync Alert" <EMAIL>
fi
```

#### 健康检查

```bash
# 定期健康检查
*/5 * * * * curl -f http://localhost:5028/api/health || echo "API服务异常" | mail -s "LstmSync Alert" <EMAIL>
```

---

## 📈 架构特性

### 分布式架构优势

- **4倍并发提升**: 从4个并发任务提升到16个
- **智能GPU调度**: 自动负载均衡和资源分配
- **完全向后兼容**: 现有客户端无需修改
- **企业级监控**: 完整的性能监控和管理工具
- **生产就绪**: 包含完整的部署和运维文档

### 技术架构

```
┌─────────────────┐
│   FastAPI API   │ ← HTTP接口层
├─────────────────┤
│ GPU Scheduler   │ ← 智能调度层
├─────────────────┤
│ Redis Queues    │ ← 消息队列层
├─────────────────┤
│ Celery Workers  │ ← 任务处理层
├─────────────────┤
│ GPU Resources   │ ← 资源管理层
└─────────────────┘
```

### 并发模型

```
API Server (1进程)
├── GPU 0 Worker (8并发)
│   ├── Task 1 → Model Instance 0
│   ├── Task 2 → Model Instance 0
│   └── ...
└── GPU 1 Worker (8并发)
    ├── Task 9 → Model Instance 1
    ├── Task 10 → Model Instance 1
    └── ...
```

### 关键配置参数

- `GPU_COUNT = 2` - GPU数量
- `MAX_TASKS_PER_GPU = 8` - 每GPU最大并发
- `TOTAL_MAX_CONCURRENT_TASKS = 16` - 总最大并发
- Redis连接池: 50个连接
- Celery超时: 3600秒

---

## 📞 支持与联系

### 技术支持

- **作者主页**: [一码超人](https://b23.tv/RL1mGQR)
- **CSDN博客**: [一码超人技术博客](https://blog.csdn.net/weixin_47723549?type=blog)
- **宣传大使**: [浪子之心科技](https://b23.tv/4CKlq4Y)

### 合作企业

- [深圳优秘智能科技有限公司](https://www.umi6.com)
- 重庆爱文曲科技有限公司

### 问题反馈

如遇到问题，请提供以下信息：
1. 错误日志内容
2. 系统环境信息
3. GPU配置信息
4. 复现步骤

### 版本更新

- `2025/06/30`: 开源192尺寸泛化权重
- `2025/07/01`: 支持Linux系统兼容
- `2025/07/30`: 分布式架构重构完成

---

## 📄 许可证

本项目遵循相应的开源许可证。使用前请确保已获得必要的授权。

**重要提示**:
- 确保`key.txt`文件存在于项目根目录
- 下载权重文件: [百度网盘](https://pan.baidu.com/s/1_taYmPkm6mMWd0eCEUzeiA) 提取码: nv7n
- 解压后确保目录结构正确

---

*最后更新: 2025年7月30日*
*文档版本: v2.0 - 分布式架构版*

#!/bin/bash

# LstmSync 分布式架构快速启动脚本

set -e

echo "🚀 LstmSync 分布式架构快速启动"
echo "=================================="

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 检查函数
check_command() {
    if command -v $1 &> /dev/null; then
        echo -e "${GREEN}✓${NC} $1 已安装"
        return 0
    else
        echo -e "${RED}✗${NC} $1 未安装"
        return 1
    fi
}

check_gpu() {
    if nvidia-smi &> /dev/null; then
        GPU_COUNT=$(nvidia-smi --list-gpus | wc -l)
        echo -e "${GREEN}✓${NC} 检测到 $GPU_COUNT 个GPU"
        nvidia-smi --query-gpu=index,name,memory.total --format=csv,noheader
        return 0
    else
        echo -e "${RED}✗${NC} 未检测到NVIDIA GPU或驱动"
        return 1
    fi
}

check_redis() {
    if redis-cli ping &> /dev/null; then
        echo -e "${GREEN}✓${NC} Redis 服务正常"
        return 0
    else
        echo -e "${YELLOW}⚠${NC} Redis 服务未运行，尝试启动..."
        if sudo systemctl start redis-server 2>/dev/null; then
            sleep 2
            if redis-cli ping &> /dev/null; then
                echo -e "${GREEN}✓${NC} Redis 服务已启动"
                return 0
            fi
        fi
        echo -e "${RED}✗${NC} Redis 服务启动失败"
        return 1
    fi
}

# 环境检查
echo -e "\n${BLUE}1. 环境检查${NC}"
echo "----------------"

CHECKS_PASSED=true

if ! check_command python; then
    CHECKS_PASSED=false
fi

if ! check_command nvidia-smi; then
    CHECKS_PASSED=false
fi

if ! check_command redis-cli; then
    CHECKS_PASSED=false
fi

if ! check_gpu; then
    CHECKS_PASSED=false
fi

if ! check_redis; then
    CHECKS_PASSED=false
fi

# 检查Python环境
echo -e "\n${BLUE}2. Python环境检查${NC}"
echo "--------------------"

if python -c "import torch; print(f'PyTorch: {torch.__version__}, CUDA: {torch.cuda.is_available()}')" 2>/dev/null; then
    echo -e "${GREEN}✓${NC} PyTorch 和 CUDA 正常"
else
    echo -e "${RED}✗${NC} PyTorch 或 CUDA 环境异常"
    CHECKS_PASSED=false
fi

if python -c "import fastapi, celery, redis" 2>/dev/null; then
    echo -e "${GREEN}✓${NC} 核心依赖已安装"
else
    echo -e "${YELLOW}⚠${NC} 部分依赖缺失，尝试安装..."
    pip install -r requirements.txt
fi

if [ "$CHECKS_PASSED" = false ]; then
    echo -e "\n${RED}❌ 环境检查失败，请解决上述问题后重试${NC}"
    exit 1
fi

# 创建必要目录
echo -e "\n${BLUE}3. 创建目录结构${NC}"
echo "--------------------"

mkdir -p logs pids
echo -e "${GREEN}✓${NC} 目录结构已创建"

# 检查关键文件
echo -e "\n${BLUE}4. 检查关键文件${NC}"
echo "--------------------"

REQUIRED_FILES=(
    "api_server.py"
    "worker_gpu0.py"
    "worker_gpu1.py"
    "service_manager.sh"
    "key.txt"
    "checkpoints/192jm.pth"
    "checkpoints/chinese-hubert-large"
    "minio_uploader.py"
)

# 检查lstmsync_func模块（可能是.py或.so文件）
LSTMSYNC_FUNC_FILES=(
    "lstmsync_func.py"
    "lstmsync_func.cpython-38-x86_64-linux-gnu.so"
    "lstmsync_func.cp38-win_amd64.pyd"
)

for file in "${REQUIRED_FILES[@]}"; do
    if [ -f "$file" ] || [ -d "$file" ]; then
        echo -e "${GREEN}✓${NC} $file"
    else
        echo -e "${RED}✗${NC} $file 缺失"
        CHECKS_PASSED=false
    fi
done

# 检查lstmsync_func模块
LSTMSYNC_FUNC_FOUND=false
for func_file in "${LSTMSYNC_FUNC_FILES[@]}"; do
    if [ -f "$func_file" ]; then
        echo -e "${GREEN}✓${NC} $func_file (lstmsync_func模块)"
        LSTMSYNC_FUNC_FOUND=true
        break
    fi
done

if [ "$LSTMSYNC_FUNC_FOUND" = false ]; then
    echo -e "${RED}✗${NC} lstmsync_func模块缺失 (需要 .py 或 .so 文件)"
    CHECKS_PASSED=false
fi

if [ "$CHECKS_PASSED" = false ]; then
    echo -e "\n${RED}❌ 关键文件缺失，请确保所有文件都存在${NC}"
    exit 1
fi

# 启动服务
echo -e "\n${BLUE}5. 启动分布式服务${NC}"
echo "----------------------"

# 停止可能运行的旧服务
echo "停止旧服务..."
./service_manager.sh stop 2>/dev/null || true

# 启动新服务
echo "启动分布式服务..."
./service_manager.sh start

# 等待服务启动
echo "等待服务启动..."
sleep 10

# 验证服务
echo -e "\n${BLUE}6. 服务验证${NC}"
echo "----------------"

# 检查API服务
if curl -s http://localhost:5028/api/health > /dev/null; then
    echo -e "${GREEN}✓${NC} API服务正常"
    
    # 获取详细状态
    echo -e "\n${YELLOW}API健康状态:${NC}"
    curl -s http://localhost:5028/api/health | python -m json.tool
    
    echo -e "\n${YELLOW}GPU统计信息:${NC}"
    curl -s http://localhost:5028/api/gpu/stats | python -m json.tool
    
else
    echo -e "${RED}✗${NC} API服务异常"
    echo "检查日志: tail -f logs/api_server.log"
    exit 1
fi

# 检查Worker服务
./service_manager.sh status

# 性能测试提示
echo -e "\n${BLUE}7. 快速测试${NC}"
echo "----------------"

echo -e "${YELLOW}运行性能监控:${NC}"
echo "python monitor.py --once"

echo -e "\n${YELLOW}运行性能测试:${NC}"
echo "python performance_test.py \\"
echo "  --tasks 5 \\"
echo "  --concurrent 4 \\"
echo "  --video-url 'YOUR_VIDEO_URL' \\"
echo "  --audio-url 'YOUR_AUDIO_URL'"

echo -e "\n${YELLOW}创建测试任务:${NC}"
echo "curl -X POST http://localhost:5028/api/tasks \\"
echo "  -H 'Content-Type: application/json' \\"
echo "  -d '{\"video_url\": \"YOUR_VIDEO_URL\", \"audio_url\": \"YOUR_AUDIO_URL\"}'"

# 完成
echo -e "\n${GREEN}🎉 LstmSync 分布式架构启动完成！${NC}"
echo "=================================="
echo -e "API地址: ${BLUE}http://localhost:5028${NC}"
echo -e "健康检查: ${BLUE}http://localhost:5028/api/health${NC}"
echo -e "GPU统计: ${BLUE}http://localhost:5028/api/gpu/stats${NC}"
echo -e "实时监控: ${YELLOW}python monitor.py${NC}"
echo -e "服务管理: ${YELLOW}./service_manager.sh [start|stop|status]${NC}"
echo -e "部署文档: ${YELLOW}DISTRIBUTED_DEPLOYMENT.md${NC}"

echo -e "\n${YELLOW}注意事项:${NC}"
echo "- 确保有足够的GPU显存 (每个任务约4GB)"
echo "- 监控系统资源使用情况"
echo "- 查看日志文件排查问题"
echo "- 根据负载调整并发参数"

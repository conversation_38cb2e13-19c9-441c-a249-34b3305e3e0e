#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import asyncio
import aiohttp
import time
import json
import statistics
from datetime import datetime
import argparse

class LstmSyncPerformanceTest:
    def __init__(self, base_url="http://localhost:5028"):
        self.base_url = base_url
        self.results = []
        
    async def create_task(self, session, video_url, audio_url, priority=0):
        """创建单个任务"""
        start_time = time.time()
        
        try:
            async with session.post(
                f"{self.base_url}/api/tasks",
                json={
                    "video_url": video_url,
                    "audio_url": audio_url,
                    "priority": priority
                },
                timeout=aiohttp.ClientTimeout(total=30)
            ) as response:
                if response.status == 200:
                    data = await response.json()
                    create_time = time.time() - start_time
                    return {
                        "success": True,
                        "task_id": data["task_id"],
                        "create_time": create_time,
                        "estimated_gpu": data.get("estimated_gpu"),
                        "queue_position": data.get("queue_position")
                    }
                else:
                    return {
                        "success": False,
                        "error": f"HTTP {response.status}",
                        "create_time": time.time() - start_time
                    }
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "create_time": time.time() - start_time
            }
    
    async def check_task_status(self, session, task_id):
        """检查任务状态"""
        try:
            async with session.get(
                f"{self.base_url}/api/tasks/{task_id}",
                timeout=aiohttp.ClientTimeout(total=10)
            ) as response:
                if response.status == 200:
                    return await response.json()
                else:
                    return {"status": "error", "message": f"HTTP {response.status}"}
        except Exception as e:
            return {"status": "error", "message": str(e)}
    
    async def wait_for_completion(self, session, task_id, max_wait_time=3600):
        """等待任务完成"""
        start_time = time.time()
        
        while time.time() - start_time < max_wait_time:
            status = await self.check_task_status(session, task_id)
            
            if status["status"] in ["completed", "failed"]:
                return {
                    "final_status": status["status"],
                    "total_time": time.time() - start_time,
                    "processing_time": status.get("processing_time"),
                    "gpu_id": status.get("gpu_id"),
                    "result_url": status.get("result_url"),
                    "message": status.get("message")
                }
            
            await asyncio.sleep(5)  # 每5秒检查一次
        
        return {
            "final_status": "timeout",
            "total_time": time.time() - start_time,
            "message": "任务超时"
        }
    
    async def get_system_stats(self, session):
        """获取系统统计信息"""
        try:
            async with session.get(
                f"{self.base_url}/api/gpu/stats",
                timeout=aiohttp.ClientTimeout(total=10)
            ) as response:
                if response.status == 200:
                    return await response.json()
        except Exception:
            pass
        return None
    
    async def run_concurrent_test(self, num_tasks, video_url, audio_url, max_concurrent=16):
        """运行并发测试"""
        print(f"开始并发测试: {num_tasks}个任务，最大并发数: {max_concurrent}")
        print(f"视频URL: {video_url}")
        print(f"音频URL: {audio_url}")
        print(f"开始时间: {datetime.now().isoformat()}")
        
        connector = aiohttp.TCPConnector(limit=max_concurrent)
        async with aiohttp.ClientSession(connector=connector) as session:
            # 获取初始系统状态
            initial_stats = await self.get_system_stats(session)
            print(f"初始GPU状态: {initial_stats}")
            
            # 创建任务的信号量，限制并发数
            semaphore = asyncio.Semaphore(max_concurrent)
            
            async def create_and_wait_task(task_index):
                async with semaphore:
                    # 创建任务
                    create_result = await self.create_task(
                        session, video_url, audio_url, priority=task_index
                    )
                    
                    if not create_result["success"]:
                        return {
                            "task_index": task_index,
                            "success": False,
                            "error": create_result["error"],
                            "create_time": create_result["create_time"]
                        }
                    
                    print(f"任务 {task_index} 已创建: {create_result['task_id']} (GPU: {create_result.get('estimated_gpu')}, 队列: {create_result.get('queue_position')})")
                    
                    # 等待任务完成
                    completion_result = await self.wait_for_completion(
                        session, create_result["task_id"]
                    )
                    
                    result = {
                        "task_index": task_index,
                        "task_id": create_result["task_id"],
                        "success": completion_result["final_status"] == "completed",
                        "create_time": create_result["create_time"],
                        "total_time": completion_result["total_time"],
                        "processing_time": completion_result.get("processing_time"),
                        "gpu_id": completion_result.get("gpu_id"),
                        "final_status": completion_result["final_status"],
                        "message": completion_result.get("message"),
                        "estimated_gpu": create_result.get("estimated_gpu"),
                        "queue_position": create_result.get("queue_position")
                    }
                    
                    print(f"任务 {task_index} 完成: {result['final_status']} (GPU: {result.get('gpu_id')}, 耗时: {result.get('total_time', 0):.1f}s)")
                    return result
            
            # 并发执行所有任务
            tasks = [create_and_wait_task(i) for i in range(num_tasks)]
            results = await asyncio.gather(*tasks, return_exceptions=True)
            
            # 获取最终系统状态
            final_stats = await self.get_system_stats(session)
            print(f"最终GPU状态: {final_stats}")
            
            return results
    
    def analyze_results(self, results):
        """分析测试结果"""
        print("\n" + "="*60)
        print("性能测试结果分析")
        print("="*60)
        
        successful_tasks = [r for r in results if isinstance(r, dict) and r.get("success")]
        failed_tasks = [r for r in results if isinstance(r, dict) and not r.get("success")]
        exceptions = [r for r in results if not isinstance(r, dict)]
        
        print(f"总任务数: {len(results)}")
        print(f"成功任务: {len(successful_tasks)}")
        print(f"失败任务: {len(failed_tasks)}")
        print(f"异常任务: {len(exceptions)}")
        print(f"成功率: {len(successful_tasks)/len(results)*100:.1f}%")
        
        if successful_tasks:
            # 时间统计
            create_times = [t["create_time"] for t in successful_tasks]
            total_times = [t["total_time"] for t in successful_tasks]
            processing_times = [t["processing_time"] for t in successful_tasks if t.get("processing_time")]
            
            print(f"\n创建任务时间统计:")
            print(f"  平均: {statistics.mean(create_times):.2f}s")
            print(f"  中位数: {statistics.median(create_times):.2f}s")
            print(f"  最小: {min(create_times):.2f}s")
            print(f"  最大: {max(create_times):.2f}s")
            
            print(f"\n总处理时间统计:")
            print(f"  平均: {statistics.mean(total_times):.2f}s")
            print(f"  中位数: {statistics.median(total_times):.2f}s")
            print(f"  最小: {min(total_times):.2f}s")
            print(f"  最大: {max(total_times):.2f}s")
            
            if processing_times:
                print(f"\n实际处理时间统计:")
                print(f"  平均: {statistics.mean(processing_times):.2f}s")
                print(f"  中位数: {statistics.median(processing_times):.2f}s")
                print(f"  最小: {min(processing_times):.2f}s")
                print(f"  最大: {max(processing_times):.2f}s")
            
            # GPU分布统计
            gpu_distribution = {}
            for task in successful_tasks:
                gpu_id = task.get("gpu_id")
                if gpu_id is not None:
                    gpu_distribution[gpu_id] = gpu_distribution.get(gpu_id, 0) + 1
            
            print(f"\nGPU任务分布:")
            for gpu_id, count in sorted(gpu_distribution.items()):
                print(f"  GPU {gpu_id}: {count} 个任务 ({count/len(successful_tasks)*100:.1f}%)")
        
        if failed_tasks:
            print(f"\n失败任务详情:")
            for task in failed_tasks[:5]:  # 只显示前5个失败任务
                print(f"  任务 {task.get('task_index', '?')}: {task.get('message', task.get('error', '未知错误'))}")

async def main():
    parser = argparse.ArgumentParser(description="LstmSync分布式性能测试")
    parser.add_argument("--tasks", type=int, default=10, help="任务数量")
    parser.add_argument("--concurrent", type=int, default=16, help="最大并发数")
    parser.add_argument("--video-url", required=True, help="测试视频URL")
    parser.add_argument("--audio-url", required=True, help="测试音频URL")
    parser.add_argument("--base-url", default="http://localhost:5028", help="API基础URL")
    
    args = parser.parse_args()
    
    tester = LstmSyncPerformanceTest(args.base_url)
    
    start_time = time.time()
    results = await tester.run_concurrent_test(
        args.tasks, args.video_url, args.audio_url, args.concurrent
    )
    total_test_time = time.time() - start_time
    
    print(f"\n总测试时间: {total_test_time:.1f}s")
    tester.analyze_results(results)

if __name__ == "__main__":
    asyncio.run(main())

conda create -n lstmsync python=3.8
conda activate lstmsync
# 安装添加PPA所需的工具
apt-get install -y software-properties-common
# 安装FFmpeg
# add-apt-repository ppa:jonathonf/ffmpeg-4 -y && apt-get update && apt-get install -y ffmpeg
# 下载 FFmpeg 静态构建版本，解压后将其可执行文件移动到系统路径中
wget https://johnvansickle.com/ffmpeg/releases/ffmpeg-release-amd64-static.tar.xz &&     tar -xf ffmpeg-release-amd64-static.tar.xz &&     mv ffmpeg-*-amd64-static/ffmpeg ffmpeg-*-amd64-static/ffprobe /usr/local/bin/ &&     rm -rf ffmpeg-release-amd64-static.tar.xz ffmpeg-*-amd64-static
# CUDA 12.4 环境匹配的 PyTorch
pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu121
# 安装依赖
pip install -r requirements.txt
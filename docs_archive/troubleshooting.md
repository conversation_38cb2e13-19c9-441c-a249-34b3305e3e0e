# LstmSync API 故障排除指南

## 任务状态不更新问题

如果您遇到任务已经完成处理，但API查询仍显示任务状态为"pending"（等待处理）的情况，这可能是由以下原因导致的：

### 可能的原因

1. **Redis服务问题**：Redis用于在API服务和Celery worker之间共享任务状态。如果Redis服务不可用或出现问题，任务状态将无法正确更新。

2. **进程间通信问题**：API服务和Celery worker是独立的进程，它们通过Redis共享状态信息。如果这些进程之间的通信出现问题，状态更新可能失败。

3. **Redis数据丢失**：在某些情况下，Redis中的数据可能会丢失，特别是如果Redis未配置持久化。

### 解决方案

1. **重启Redis服务**：

   ```bash
   # 使用服务管理脚本重启Redis
   ./service_manager.sh restart-redis
   
   # 或者直接使用系统命令
   sudo systemctl restart redis-server
   ```

2. **重启API服务和Celery worker**：

   ```bash
   # 使用服务管理脚本重启所有服务
   ./service_manager.sh restart
   ```

3. **检查服务状态**：

   ```bash
   # 检查所有服务的状态
   ./service_manager.sh status
   
   # 或者使用测试脚本检查API健康状态
   python test_api.py health
   ```

4. **查看日志文件**：

   ```bash
   # 查看API服务日志
   ./service_manager.sh logs api
   
   # 查看Celery worker日志
   ./service_manager.sh logs worker
   ```

5. **使用测试脚本验证API功能**：

   ```bash
   # 创建测试任务
   python test_api.py create --video "https://example.com/video.mp4" --audio "https://example.com/audio.wav"
   
   # 监控任务状态
   python test_api.py monitor --task-id "任务ID"
   ```

## 预防措施

为了避免类似问题再次发生，我们已经进行了以下改进：

1. **使用Redis存储任务状态**：任务状态现在存储在Redis中，而不是内存中的字典，确保不同进程之间可以共享状态信息。

2. **添加健康检查接口**：API服务现在提供了`/api/health`端点，可以用来检查服务和Redis连接的健康状态。

3. **改进错误处理**：改进了错误处理和日志记录，使问题更容易诊断。

4. **添加任务状态恢复机制**：如果Redis中的任务状态丢失，API会尝试从Celery结果中恢复状态。

## 持久化配置（可选）

如果您需要确保任务状态在系统重启后仍然可用，建议配置Redis持久化：

1. 编辑Redis配置文件（通常在`/etc/redis/redis.conf`）：

   ```bash
   sudo nano /etc/redis/redis.conf
   ```

2. 启用RDB和AOF持久化：

   ```
   # 启用RDB持久化
   save 900 1
   save 300 10
   save 60 10000
   
   # 启用AOF持久化
   appendonly yes
   ```

3. 重启Redis服务：

   ```bash
   sudo systemctl restart redis-server
   ```

这些配置将确保Redis数据在服务器重启后仍然可用，防止任务状态丢失。 
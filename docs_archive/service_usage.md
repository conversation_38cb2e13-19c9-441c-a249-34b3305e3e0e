# LstmSync服务管理脚本使用说明

本文档介绍如何使用`service_manager.sh`脚本来管理LstmSync API服务。该脚本提供了启动、停止、重启服务以及查看日志的功能。

## 准备工作

在使用脚本前，请确保：

1. 已安装并配置好Conda环境
2. 已安装所有必要的依赖（参见`requirements.txt`）
3. 已正确设置项目文件和目录结构

## 使用方法

首先，确保脚本具有可执行权限：

```bash
chmod +x service_manager.sh
```

### 启动服务

启动API服务和Celery worker：

```bash
./service_manager.sh start
```

该命令会：
- 激活名为"lstmsync"的Conda环境
- 启动API服务器和Celery worker
- 将进程ID保存到`pids`目录
- 将日志输出保存到`logs`目录

### 停止服务

停止所有运行中的服务：

```bash
./service_manager.sh stop
```

### 重启服务

重启所有服务：

```bash
./service_manager.sh restart
```

### 查看服务状态

检查服务的运行状态：

```bash
./service_manager.sh status
```

### 查看日志

查看API服务的日志：

```bash
./service_manager.sh logs api
```

查看Celery worker的日志：

```bash
./service_manager.sh logs worker
```

## 日志和PID文件

脚本会创建以下目录和文件：

- `logs/api_server.log` - API服务的日志文件
- `logs/celery_worker.log` - Celery worker的日志文件
- `pids/api_server.pid` - API服务的进程ID文件
- `pids/celery_worker.pid` - Celery worker的进程ID文件

## 故障排除

如果遇到问题：

1. 检查日志文件中的错误信息
2. 确认Conda环境名称是否正确（默认为"lstmsync"）
3. 检查Redis服务是否正在运行
4. 确认所有依赖已正确安装

## 自定义配置

如需修改默认配置，请编辑脚本开头的以下变量：

```bash
CONDA_ENV="lstmsync"  # Conda环境名称
LOG_DIR="./logs"      # 日志目录
PID_DIR="./pids"      # PID文件目录
``` 
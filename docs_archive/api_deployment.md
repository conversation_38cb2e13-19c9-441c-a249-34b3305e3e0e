# LstmSync API 部署指南

## 系统要求

- Python 3.8
- CUDA 11.8+ (推荐)
- Redis 服务器
- FFmpeg

## 安装步骤

### 1. 环境准备

```bash
# 创建虚拟环境
conda create -n lstmsync python=3.8
conda activate lstmsync

# 安装FFmpeg (如果尚未安装)
apt-get install -y software-properties-common
wget https://johnvansickle.com/ffmpeg/releases/ffmpeg-release-amd64-static.tar.xz
tar -xf ffmpeg-release-amd64-static.tar.xz
mv ffmpeg-*-amd64-static/ffmpeg ffmpeg-*-amd64-static/ffprobe /usr/local/bin/
rm -rf ffmpeg-release-amd64-static.tar.xz ffmpeg-*-amd64-static

# 安装PyTorch
pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu121
```

### 2. 安装Redis

```bash
# 安装Redis
apt-get install -y redis-server

# 启动Redis服务
systemctl start redis-server
systemctl enable redis-server
```

### 3. 安装项目依赖

```bash
# 安装项目依赖
pip install -r requirements.txt
```

### 4. 配置项目

确保以下文件和目录存在：
- `key.txt` - 位于项目根目录
- `checkpoints/` 目录 - 包含必要的模型文件

## 运行服务

### 1. 启动Celery Worker

```bash
# 启动Celery Worker
python worker.py
```

### 2. 启动API服务器

```bash
# 启动API服务器
python api_server.py
```

默认情况下，API服务器将在 `http://0.0.0.0:8000` 上运行。

## API使用说明

### 创建新任务

**请求**:
```
POST /api/tasks

{
  "video_url": "https://example.com/video.mp4",
  "audio_url": "https://example.com/audio.wav"
}
```

**响应**:
```json
{
  "task_id": "d1311191-1852-4441-a8b4-b896641e9693",
  "status": "success"
}
```

### 查询任务状态

**请求**:
```
GET /api/tasks/{task_id}
```

**响应示例**:
```json
{
  "status": "completed",
  "result_url": "https://minio-server.example.com/bucket/video/result.mp4",
  "message": "任务处理完成"
}
```

## 状态说明

- `pending`: 任务已创建，等待处理
- `processing`: 任务正在处理中
- `completed`: 任务处理完成
- `failed`: 任务处理失败

## 生产环境部署

对于生产环境，建议：

1. 使用Supervisor或Systemd管理Celery Worker和API服务进程
2. 使用Nginx作为反向代理
3. 配置适当的日志记录
4. 使用环境变量管理敏感配置

## 故障排除

如果遇到问题：

1. 检查Redis服务是否正常运行
2. 确认所有依赖已正确安装
3. 检查日志文件获取详细错误信息
4. 确保模型文件路径正确 
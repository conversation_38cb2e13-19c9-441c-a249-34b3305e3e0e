# LstmSync 分布式架构部署指南

## 概述

本文档描述了LstmSync项目的分布式架构重构，该架构专为双GPU环境优化，实现最大化并发处理能力。

## 架构特性

### 🚀 核心优化
- **智能GPU调度**: 自动分配任务到最优GPU
- **模型预加载**: 避免重复加载，提升性能
- **资源管理**: GPU资源锁和任务队列管理
- **负载均衡**: 双GPU负载均衡和任务分发
- **性能监控**: 实时GPU和系统状态监控

### 📊 性能指标
- **理论并发**: 16个任务 (每GPU 8个)
- **GPU利用率**: 最大化48GB显存使用
- **API兼容性**: 100%向后兼容
- **故障恢复**: 自动重试和错误处理

## 环境要求

### 硬件要求
- 2x NVIDIA GeForce RTX 4090 (48GB显存)
- 至少32GB系统内存
- 高速SSD存储

### 软件要求
- Python 3.8+
- CUDA 12.4+
- Redis 6.0+
- 所有requirements.txt中的依赖

## 安装部署

### 1. 环境准备

```bash
# 激活conda环境
conda activate lstmsync

# 安装新依赖
pip install -r requirements.txt

# 验证GPU环境
nvidia-smi
python -c "import torch; print(f'CUDA available: {torch.cuda.is_available()}, GPU count: {torch.cuda.device_count()}')"
```

### 2. Redis配置优化

```bash
# 使用优化的Redis配置
sudo cp redis_distributed.conf /etc/redis/redis.conf

# 重启Redis服务
sudo systemctl restart redis-server

# 验证Redis连接
redis-cli ping
```

### 3. 服务启动

#### 方式一：使用服务管理脚本（推荐）

```bash
# 启动所有服务
./service_manager.sh start

# 检查服务状态
./service_manager.sh status

# 停止所有服务
./service_manager.sh stop
```

#### 方式二：手动启动

```bash
# 启动API服务
python api_server.py &

# 启动GPU 0 Worker
python worker_gpu0.py &

# 启动GPU 1 Worker
python worker_gpu1.py &
```

### 4. 验证部署

```bash
# 健康检查
curl http://localhost:5028/api/health

# GPU统计
curl http://localhost:5028/api/gpu/stats

# GPU内存信息
curl http://localhost:5028/api/gpu/memory
```

## 监控和管理

### 实时监控

```bash
# 持续监控（30秒间隔）
python monitor.py

# 单次监控
python monitor.py --once

# 自定义间隔（10秒）
python monitor.py 10
```

### 性能测试

```bash
# 基础性能测试
python performance_test.py \
  --tasks 10 \
  --concurrent 8 \
  --video-url "https://example.com/test.mp4" \
  --audio-url "https://example.com/test.wav"

# 压力测试
python performance_test.py \
  --tasks 50 \
  --concurrent 16 \
  --video-url "https://example.com/test.mp4" \
  --audio-url "https://example.com/test.wav"
```

## API接口

### 创建任务（增强版）

```bash
POST /api/tasks
{
  "video_url": "https://example.com/video.mp4",
  "audio_url": "https://example.com/audio.wav",
  "priority": 0  # 可选，优先级（数字越大优先级越高）
}

# 响应
{
  "task_id": "uuid",
  "status": "success",
  "estimated_gpu": 0,  # 预估分配的GPU
  "queue_position": 1  # 队列位置（如果需要排队）
}
```

### 查询任务状态（增强版）

```bash
GET /api/tasks/{task_id}

# 响应
{
  "status": "completed",
  "result_url": "https://minio.example.com/result.mp4",
  "message": "任务在GPU 0上处理完成",
  "gpu_id": 0,  # 实际使用的GPU
  "processing_time": 45.2  # 处理时间（秒）
}
```

### 新增接口

```bash
# GPU统计信息
GET /api/gpu/stats

# GPU内存信息
GET /api/gpu/memory

# 健康检查（增强版）
GET /api/health
```

## 配置参数

### GPU配置
- `GPU_COUNT`: GPU数量 (默认: 2)
- `MAX_TASKS_PER_GPU`: 每GPU最大并发 (默认: 8)
- `TOTAL_MAX_CONCURRENT_TASKS`: 总最大并发 (默认: 16)

### Redis配置
- 连接池大小: 50
- 超时设置: 5秒
- 重试机制: 启用

### Celery配置
- Worker并发数: 每GPU 8个
- 预取倍数: 1
- 任务超时: 1小时
- 队列分离: gpu0_queue, gpu1_queue

## 故障排除

### 常见问题

1. **GPU内存不足**
   ```bash
   # 检查GPU内存使用
   nvidia-smi
   
   # 减少并发数
   # 修改api_server.py中的MAX_TASKS_PER_GPU
   ```

2. **Redis连接失败**
   ```bash
   # 检查Redis状态
   sudo systemctl status redis-server
   
   # 重启Redis
   sudo systemctl restart redis-server
   ```

3. **Worker无法启动**
   ```bash
   # 检查日志
   tail -f logs/celery_worker_gpu0.log
   tail -f logs/celery_worker_gpu1.log
   
   # 检查CUDA环境
   echo $CUDA_VISIBLE_DEVICES
   ```

### 日志文件
- API服务: `logs/api_server.log`
- GPU 0 Worker: `logs/celery_worker_gpu0.log`
- GPU 1 Worker: `logs/celery_worker_gpu1.log`

### 性能调优

1. **增加并发数**（谨慎操作）
   ```python
   # 在api_server.py中修改
   MAX_TASKS_PER_GPU = 10  # 从8增加到10
   ```

2. **优化Redis**
   ```bash
   # 调整Redis内存限制
   # 在redis_distributed.conf中修改maxmemory
   ```

3. **系统优化**
   ```bash
   # 增加文件描述符限制
   ulimit -n 65536
   
   # 优化网络参数
   echo 'net.core.somaxconn = 65535' >> /etc/sysctl.conf
   ```

## 扩展性

### 水平扩展
- 支持添加更多GPU节点
- 支持多机器部署
- 支持Kubernetes部署

### 垂直扩展
- 支持更大显存GPU
- 支持调整并发参数
- 支持批处理优化

## 安全考虑

- Redis访问控制
- API接口限流
- 文件上传验证
- 资源使用监控

## 维护建议

1. **定期监控**
   - 每日检查GPU使用率
   - 监控Redis内存使用
   - 检查错误日志

2. **性能优化**
   - 定期运行性能测试
   - 根据负载调整并发数
   - 优化模型加载策略

3. **备份策略**
   - 定期备份Redis数据
   - 备份配置文件
   - 监控磁盘空间

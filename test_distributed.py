#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import requests
import time
import json

def test_api_endpoints():
    """测试API端点"""
    base_url = "http://localhost:5028"
    
    print("🧪 测试API端点")
    print("=" * 40)
    
    # 测试健康检查
    try:
        response = requests.get(f"{base_url}/api/health", timeout=10)
        if response.status_code == 200:
            health_data = response.json()
            print("✅ 健康检查通过")
            print(f"   状态: {health_data.get('status')}")
            print(f"   GPU数量: {health_data.get('gpu_stats', {}).get('gpu_count', 'N/A')}")
            print(f"   当前任务: {health_data.get('gpu_stats', {}).get('total_current_tasks', 'N/A')}")
        else:
            print(f"❌ 健康检查失败: HTTP {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 健康检查异常: {e}")
        return False
    
    # 测试GPU统计
    try:
        response = requests.get(f"{base_url}/api/gpu/stats", timeout=10)
        if response.status_code == 200:
            gpu_stats = response.json()
            print("✅ GPU统计接口正常")
            print(f"   GPU数量: {gpu_stats.get('gpu_count')}")
            print(f"   每GPU最大任务: {gpu_stats.get('max_tasks_per_gpu')}")
            print(f"   当前任务分布: {gpu_stats.get('current_tasks')}")
            print(f"   GPU利用率: {gpu_stats.get('gpu_utilization')}")
        else:
            print(f"❌ GPU统计接口失败: HTTP {response.status_code}")
    except Exception as e:
        print(f"❌ GPU统计接口异常: {e}")
    
    # 测试GPU内存
    try:
        response = requests.get(f"{base_url}/api/gpu/memory", timeout=10)
        if response.status_code == 200:
            gpu_memory = response.json()
            print("✅ GPU内存接口正常")
            for gpu in gpu_memory.get('gpu_memory', []):
                print(f"   GPU {gpu['gpu_id']}: {gpu['memory_used']}/{gpu['memory_total']} MB ({gpu['utilization_percent']:.1f}%)")
        else:
            print(f"❌ GPU内存接口失败: HTTP {response.status_code}")
    except Exception as e:
        print(f"❌ GPU内存接口异常: {e}")
    
    return True

def test_task_creation():
    """测试任务创建（不实际执行）"""
    base_url = "http://localhost:5028"
    
    print("\n🧪 测试任务创建接口")
    print("=" * 40)
    
    # 使用虚拟URL测试接口
    test_data = {
        "video_url": "https://example.com/test_video.mp4",
        "audio_url": "https://example.com/test_audio.wav",
        "priority": 1
    }
    
    try:
        response = requests.post(
            f"{base_url}/api/tasks",
            json=test_data,
            timeout=10
        )
        
        if response.status_code == 200:
            task_data = response.json()
            print("✅ 任务创建接口正常")
            print(f"   任务ID: {task_data.get('task_id')}")
            print(f"   预估GPU: {task_data.get('estimated_gpu')}")
            print(f"   队列位置: {task_data.get('queue_position')}")
            
            # 测试任务状态查询
            task_id = task_data.get('task_id')
            if task_id:
                time.sleep(1)
                status_response = requests.get(f"{base_url}/api/tasks/{task_id}", timeout=10)
                if status_response.status_code == 200:
                    status_data = status_response.json()
                    print("✅ 任务状态查询正常")
                    print(f"   状态: {status_data.get('status')}")
                    print(f"   消息: {status_data.get('message')}")
                    print(f"   GPU ID: {status_data.get('gpu_id')}")
                else:
                    print(f"❌ 任务状态查询失败: HTTP {status_response.status_code}")
            
            return True
        else:
            print(f"❌ 任务创建失败: HTTP {response.status_code}")
            try:
                error_data = response.json()
                print(f"   错误详情: {error_data}")
            except:
                print(f"   响应内容: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 任务创建异常: {e}")
        return False

def test_redis_connection():
    """测试Redis连接"""
    print("\n🧪 测试Redis连接")
    print("=" * 40)
    
    try:
        import redis
        redis_client = redis.Redis(host='localhost', port=6379, db=2, decode_responses=True)
        
        # 测试连接
        redis_client.ping()
        print("✅ Redis连接正常")
        
        # 测试写入
        test_key = "lstmsync:test:connection"
        test_value = {"test": True, "timestamp": time.time()}
        redis_client.set(test_key, json.dumps(test_value), ex=60)
        
        # 测试读取
        retrieved_value = redis_client.get(test_key)
        if retrieved_value:
            print("✅ Redis读写正常")
            redis_client.delete(test_key)
        else:
            print("❌ Redis读取失败")
            
        return True
        
    except Exception as e:
        print(f"❌ Redis连接异常: {e}")
        return False

def test_celery_workers():
    """测试Celery Worker状态"""
    print("\n🧪 测试Celery Worker状态")
    print("=" * 40)
    
    try:
        from celery import Celery
        
        # 创建Celery实例
        celery_app = Celery(
            'lstmsync_tasks',
            broker='redis://localhost:6379/0',
            backend='redis://localhost:6379/1'
        )
        
        # 检查活跃的worker
        inspect = celery_app.control.inspect()
        active_workers = inspect.active()
        
        if active_workers:
            print("✅ Celery Worker连接正常")
            for worker_name, tasks in active_workers.items():
                print(f"   Worker: {worker_name}")
                print(f"   活跃任务数: {len(tasks)}")
        else:
            print("❌ 未检测到活跃的Celery Worker")
            return False
            
        # 检查注册的任务
        registered_tasks = inspect.registered()
        if registered_tasks:
            print("✅ 任务注册正常")
            for worker_name, tasks in registered_tasks.items():
                gpu_tasks = [t for t in tasks if 'process_video_gpu' in t]
                print(f"   Worker {worker_name}: {len(gpu_tasks)} GPU任务")
        
        return True
        
    except Exception as e:
        print(f"❌ Celery Worker测试异常: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 LstmSync 分布式架构测试")
    print("=" * 50)
    
    tests = [
        ("API端点测试", test_api_endpoints),
        ("任务创建测试", test_task_creation),
        ("Redis连接测试", test_redis_connection),
        ("Celery Worker测试", test_celery_workers),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n📋 {test_name}")
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} 通过")
            else:
                print(f"❌ {test_name} 失败")
        except Exception as e:
            print(f"❌ {test_name} 异常: {e}")
    
    print(f"\n📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！分布式架构重构成功！")
        print("\n📝 下一步:")
        print("1. 运行性能监控: python monitor.py")
        print("2. 执行性能测试: python performance_test.py --help")
        print("3. 查看部署文档: DISTRIBUTED_DEPLOYMENT.md")
        return True
    else:
        print("⚠️  部分测试失败，请检查服务状态")
        print("\n🔧 故障排除:")
        print("1. 检查服务状态: ./service_manager.sh status")
        print("2. 查看日志: tail -f logs/*.log")
        print("3. 重启服务: ./service_manager.sh restart")
        return False

if __name__ == "__main__":
    main()

#!/bin/bash

# Linux代理清除脚本
# 用于完全清除系统中的所有代理配置

echo "=== Linux代理清除脚本 ==="
echo "开始清除所有代理配置..."

# 1. 清除当前shell会话的环境变量
echo "步骤1: 清除当前shell会话的环境变量"
unset http_proxy
unset https_proxy
unset HTTP_PROXY
unset HTTPS_PROXY
unset ftp_proxy
unset FTP_PROXY
unset no_proxy
unset NO_PROXY
echo "✓ 当前会话环境变量已清除"

# 2. 清除systemd用户环境变量
echo "步骤2: 清除systemd用户环境变量"
systemctl --user unset-environment http_proxy https_proxy HTTP_PROXY HTTPS_PROXY ftp_proxy FTP_PROXY no_proxy NO_PROXY 2>/dev/null
systemctl --user daemon-reload 2>/dev/null
systemctl --user reset-failed 2>/dev/null
echo "✓ systemd用户环境变量已清除"

# 3. 检查并注释配置文件中的代理设置（如果存在）
echo "步骤3: 检查配置文件中的代理设置"

# 检查并处理 ~/.bashrc
if [ -f ~/.bashrc ] && grep -q "^[^#]*proxy" ~/.bashrc; then
    echo "在 ~/.bashrc 中发现代理配置，正在注释..."
    sed -i 's/^[[:space:]]*export[[:space:]]*.*proxy.*/#&/' ~/.bashrc
    echo "✓ ~/.bashrc 中的代理配置已注释"
fi

# 检查并处理 ~/.profile
if [ -f ~/.profile ] && grep -q "^[^#]*proxy" ~/.profile; then
    echo "在 ~/.profile 中发现代理配置，正在注释..."
    sed -i 's/^[[:space:]]*export[[:space:]]*.*proxy.*/#&/' ~/.profile
    echo "✓ ~/.profile 中的代理配置已注释"
fi

# 检查并处理 ~/.bash_profile
if [ -f ~/.bash_profile ] && grep -q "^[^#]*proxy" ~/.bash_profile; then
    echo "在 ~/.bash_profile 中发现代理配置，正在注释..."
    sed -i 's/^[[:space:]]*export[[:space:]]*.*proxy.*/#&/' ~/.bash_profile
    echo "✓ ~/.bash_profile 中的代理配置已注释"
fi

# 检查并处理 ~/.zshrc
if [ -f ~/.zshrc ] && grep -q "^[^#]*proxy" ~/.zshrc; then
    echo "在 ~/.zshrc 中发现代理配置，正在注释..."
    sed -i 's/^[[:space:]]*export[[:space:]]*.*proxy.*/#&/' ~/.zshrc
    echo "✓ ~/.zshrc 中的代理配置已注释"
fi

# 检查系统级配置文件（需要root权限）
if [ "$EUID" -eq 0 ]; then
    # 检查并处理 /etc/environment
    if [ -f /etc/environment ] && grep -q "^[^#]*proxy" /etc/environment; then
        echo "在 /etc/environment 中发现代理配置，正在注释..."
        sed -i 's/^[[:space:]]*.*proxy.*/#&/' /etc/environment
        echo "✓ /etc/environment 中的代理配置已注释"
    fi
    
    # 检查并处理 /etc/profile
    if [ -f /etc/profile ] && grep -q "^[^#]*proxy" /etc/profile; then
        echo "在 /etc/profile 中发现代理配置，正在注释..."
        sed -i 's/^[[:space:]]*export[[:space:]]*.*proxy.*/#&/' /etc/profile
        echo "✓ /etc/profile 中的代理配置已注释"
    fi
else
    echo "注意: 非root用户，跳过系统级配置文件检查"
fi

# 4. 验证清除效果
echo "步骤4: 验证清除效果"
echo "当前环境变量状态:"
if env | grep -i proxy >/dev/null 2>&1; then
    echo "⚠ 仍有代理环境变量存在:"
    env | grep -i proxy
else
    echo "✓ 当前会话无代理环境变量"
fi

echo "systemd用户环境状态:"
if systemctl --user show-environment 2>/dev/null | grep -i proxy >/dev/null 2>&1; then
    echo "⚠ systemd用户环境仍有代理配置:"
    systemctl --user show-environment | grep -i proxy
else
    echo "✓ systemd用户环境无代理配置"
fi

# 5. 测试网络连接
echo "步骤5: 测试网络连接"
if command -v curl >/dev/null 2>&1; then
    echo "测试网络连接（应该不再通过代理）:"
    if curl -s --max-time 10 google.com 2>/dev/null; then
        echo "✓ 网络连接正常，代理已清除"
    else
        echo "⚠ 网络连接测试失败，可能代理已清除但网络不可达"
    fi
else
    echo "curl命令不可用，跳过网络测试"
fi

echo "=== 代理清除完成 ==="1.3..............................................................................................................................................................................................................................................................................................................................................................................................................................................................................................................................................................................................................................................................................................................................................................................................................................................................................................................................................................................................................................................................................................................................................................................................................................................................................................................................................................................................................................................................................................................................................................................................................................................................................................................................................................................................................................................................................................................................................................
echo "建议重新启动终端或重新登录以确保所有更改生效"

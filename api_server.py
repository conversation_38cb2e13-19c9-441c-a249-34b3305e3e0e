#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import uuid
import tempfile
import shutil
import json
import requests
import redis
import threading
import time
import subprocess
from typing import Dict, Optional, Any, List
from fastapi import FastAPI, HTTPException, BackgroundTasks
from pydantic import BaseModel
from celery import Celery
from kombu import Queue
import lstmsync_func
import minio_uploader

# 创建FastAPI应用
app = FastAPI(title="LstmSync API", description="音视频同步处理API - 分布式版本")

# Redis配置 - 优化连接池
REDIS_HOST = 'localhost'
REDIS_PORT = 6380
REDIS_DB = 2
REDIS_PREFIX = 'lstmsync:task:'
REDIS_GPU_STATS_PREFIX = 'lstmsync:gpu:stats:'

# 创建Redis连接池
redis_client = redis.Redis(
    host=REDIS_HOST,
    port=REDIS_PORT,
    db=REDIS_DB,
    decode_responses=True,
    max_connections=50,  # 增加连接池大小
    socket_timeout=5,
    socket_connect_timeout=5,
    retry_on_timeout=True
)

# GPU配置
GPU_COUNT = 2
MAX_TASKS_PER_GPU = 2  # 每张GPU最大并发任务数 (48GB / 3GB = 16, 优化内存使用)
TOTAL_MAX_CONCURRENT_TASKS = GPU_COUNT * MAX_TASKS_PER_GPU

# 创建Celery实例 - 配置多队列
celery_app = Celery(
    'lstmsync_tasks',
    broker=f'redis://{REDIS_HOST}:{REDIS_PORT}/0',
    backend=f'redis://{REDIS_HOST}:{REDIS_PORT}/1'
)

# Celery配置优化
celery_app.conf.update(
    task_serializer='json',
    accept_content=['json'],
    result_serializer='json',
    timezone='UTC',
    enable_utc=True,
    task_track_started=True,
    task_time_limit=3600,  # 1小时超时
    task_soft_time_limit=3300,  # 55分钟软超时
    worker_prefetch_multiplier=1,  # 防止任务堆积
    task_acks_late=True,
    worker_disable_rate_limits=True,
    # 连接重试配置
    broker_connection_retry=True,
    broker_connection_retry_on_startup=True,
    broker_connection_max_retries=10,
    broker_connection_retry_delay=2.0,
    task_routes={
        'process_video_gpu0': {'queue': 'gpu0_queue'},
        'process_video_gpu1': {'queue': 'gpu1_queue'},
    },
    task_default_queue='default',
    task_queues=(
        Queue('gpu0_queue', routing_key='gpu0'),
        Queue('gpu1_queue', routing_key='gpu1'),
        Queue('default', routing_key='default'),
    ),
)

# GPU资源管理器 - 使用Redis实现跨进程资源管理
class GPUResourceManager:
    def __init__(self):
        self.redis_client = redis.Redis(host='localhost', port=6379, db=0, decode_responses=True)
        self.lock = threading.Lock()
        # 初始化GPU资源计数
        for gpu_id in range(GPU_COUNT):
            key = f"gpu_{gpu_id}_tasks"
            if not self.redis_client.exists(key):
                self.redis_client.set(key, 0)

    def get_available_gpu(self) -> Optional[int]:
        """获取可用的GPU ID - 负载均衡选择"""
        gpu_loads = []
        for gpu_id in range(GPU_COUNT):
            current_tasks = int(self.redis_client.get(f"gpu_{gpu_id}_tasks") or 0)
            gpu_loads.append((gpu_id, current_tasks))

        # 按任务数排序，选择任务数最少的GPU
        gpu_loads.sort(key=lambda x: x[1])

        # 返回任务数最少且未满的GPU
        for gpu_id, current_tasks in gpu_loads:
            if current_tasks < MAX_TASKS_PER_GPU:
                return gpu_id
        return None

    def acquire_gpu(self, gpu_id: int) -> bool:
        """获取GPU资源"""
        try:
            # 使用Redis事务确保原子性
            with self.redis_client.pipeline() as pipe:
                while True:
                    try:
                        pipe.watch(f"gpu_{gpu_id}_tasks")
                        current_tasks = int(pipe.get(f"gpu_{gpu_id}_tasks") or 0)

                        if current_tasks >= MAX_TASKS_PER_GPU:
                            pipe.unwatch()
                            return False

                        pipe.multi()
                        pipe.incr(f"gpu_{gpu_id}_tasks")
                        pipe.execute()

                        self._update_gpu_stats(gpu_id)
                        print(f"[DEBUG] 成功获取GPU {gpu_id}资源，当前任务数: {current_tasks + 1}")
                        return True
                    except redis.WatchError:
                        # 重试
                        continue
        except Exception as e:
            print(f"[ERROR] 获取GPU {gpu_id}资源失败: {str(e)}")
            return False

    def release_gpu(self, gpu_id: int):
        """释放GPU资源"""
        try:
            current_tasks = self.redis_client.decr(f"gpu_{gpu_id}_tasks")
            if current_tasks < 0:
                self.redis_client.set(f"gpu_{gpu_id}_tasks", 0)
            self._update_gpu_stats(gpu_id)
            print(f"[DEBUG] 释放GPU {gpu_id}资源，当前任务数: {max(0, current_tasks)}")
        except Exception as e:
            print(f"[ERROR] 释放GPU {gpu_id}资源失败: {str(e)}")

    def get_gpu_stats(self) -> Dict[str, Any]:
        """获取GPU使用统计"""
        try:
            current_tasks = []
            for gpu_id in range(GPU_COUNT):
                count = int(self.redis_client.get(f"gpu_{gpu_id}_tasks") or 0)
                current_tasks.append(count)

            return {
                'gpu_count': GPU_COUNT,
                'max_tasks_per_gpu': MAX_TASKS_PER_GPU,
                'current_tasks': current_tasks,
                'total_current_tasks': sum(current_tasks),
                'total_max_tasks': TOTAL_MAX_CONCURRENT_TASKS,
                'gpu_utilization': [
                    round(count / MAX_TASKS_PER_GPU * 100, 2)
                    for count in current_tasks
                ]
            }
        except Exception as e:
            print(f"[ERROR] 获取GPU统计信息失败: {str(e)}")
            return {
                'gpu_count': GPU_COUNT,
                'max_tasks_per_gpu': MAX_TASKS_PER_GPU,
                'current_tasks': [0] * GPU_COUNT,
                'total_current_tasks': 0,
                'total_max_tasks': TOTAL_MAX_CONCURRENT_TASKS,
                'gpu_utilization': [0.0] * GPU_COUNT
            }

    def _update_gpu_stats(self, gpu_id: int):
        """更新Redis中的GPU统计信息"""
        try:
            stats = self.get_gpu_stats()
            redis_client.setex(
                f"{REDIS_GPU_STATS_PREFIX}current",
                300,  # 5分钟过期
                json.dumps(stats)
            )
        except Exception as e:
            print(f"更新GPU统计信息失败: {e}")

# 全局GPU资源管理器
gpu_manager = GPUResourceManager()

class TaskRequest(BaseModel):
    video_url: str
    audio_url: str
    priority: Optional[int] = 0  # 任务优先级，数字越大优先级越高

class TaskResponse(BaseModel):
    task_id: str
    status: str
    estimated_gpu: Optional[int] = None  # 预估分配的GPU
    queue_position: Optional[int] = None  # 队列位置

class TaskStatusResponse(BaseModel):
    status: str
    result_url: Optional[str] = None
    message: str
    gpu_id: Optional[int] = None  # 实际使用的GPU
    processing_time: Optional[float] = None  # 处理时间（秒）

class GPUStatsResponse(BaseModel):
    gpu_count: int
    max_tasks_per_gpu: int
    current_tasks: List[int]
    total_current_tasks: int
    total_max_tasks: int
    gpu_utilization: List[float]

# 智能GPU调度器
class GPUScheduler:
    def __init__(self):
        self.task_queue_gpu0 = []
        self.task_queue_gpu1 = []
        self.queue_lock = threading.Lock()

    def schedule_task(self, task_id: str, priority: int = 0) -> Optional[int]:
        """智能调度任务到最优GPU"""
        # 获取当前GPU负载情况
        gpu0_tasks = int(gpu_manager.redis_client.get("gpu_0_tasks") or 0)
        gpu1_tasks = int(gpu_manager.redis_client.get("gpu_1_tasks") or 0)
        print(f"[SCHEDULER] 当前GPU负载 - GPU0: {gpu0_tasks}, GPU1: {gpu1_tasks}")

        available_gpu = gpu_manager.get_available_gpu()
        print(f"[SCHEDULER] 负载均衡选择的GPU: {available_gpu}")

        if available_gpu is not None:
            return available_gpu

        # 如果没有立即可用的GPU，选择任务数较少的GPU队列
        with self.queue_lock:
            gpu0_tasks = int(gpu_manager.redis_client.get("gpu_0_tasks") or 0)
            gpu1_tasks = int(gpu_manager.redis_client.get("gpu_1_tasks") or 0)
            gpu0_load = len(self.task_queue_gpu0) + gpu0_tasks
            gpu1_load = len(self.task_queue_gpu1) + gpu1_tasks

            if gpu0_load <= gpu1_load:
                self.task_queue_gpu0.append((task_id, priority, time.time()))
                return 0
            else:
                self.task_queue_gpu1.append((task_id, priority, time.time()))
                return 1

    def get_queue_position(self, task_id: str, gpu_id: int) -> int:
        """获取任务在队列中的位置"""
        with self.queue_lock:
            queue = self.task_queue_gpu0 if gpu_id == 0 else self.task_queue_gpu1
            for i, (tid, _, _) in enumerate(queue):
                if tid == task_id:
                    return i + 1
            return 0

# 全局调度器
gpu_scheduler = GPUScheduler()

def get_task_status_key(task_id: str) -> str:
    """获取任务状态在Redis中的键名"""
    return f"{REDIS_PREFIX}{task_id}"

def save_task_status(task_id: str, status_data: Dict[str, Any]) -> None:
    """保存任务状态到Redis"""
    key = get_task_status_key(task_id)
    # 添加时间戳
    status_data['updated_at'] = time.time()
    redis_client.set(key, json.dumps(status_data))
    # 设置过期时间为7天
    redis_client.expire(key, 60 * 60 * 24 * 7)

def get_task_status(task_id: str) -> Optional[Dict[str, Any]]:
    """从Redis获取任务状态"""
    key = get_task_status_key(task_id)
    data = redis_client.get(key)
    if data:
        return json.loads(data)
    return None

def get_gpu_memory_info() -> List[Dict[str, Any]]:
    """获取GPU内存使用信息"""
    try:
        result = subprocess.run(['nvidia-smi', '--query-gpu=index,memory.used,memory.total', '--format=csv,noheader,nounits'],
                              capture_output=True, text=True, timeout=10)
        gpu_info = []
        for line in result.stdout.strip().split('\n'):
            if line:
                parts = line.split(', ')
                gpu_info.append({
                    'gpu_id': int(parts[0]),
                    'memory_used': int(parts[1]),
                    'memory_total': int(parts[2]),
                    'memory_free': int(parts[2]) - int(parts[1]),
                    'utilization_percent': round(int(parts[1]) / int(parts[2]) * 100, 2)
                })
        return gpu_info
    except Exception as e:
        print(f"获取GPU信息失败: {e}")
        return []

def check_proxy_availability(proxy_url: str, timeout: int = 3) -> bool:
    """检查代理是否可用"""
    try:
        import socket
        from urllib.parse import urlparse

        parsed = urlparse(proxy_url)
        host = parsed.hostname
        port = parsed.port

        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(timeout)
        result = sock.connect_ex((host, port))
        sock.close()
        return result == 0
    except Exception:
        return False

def get_smart_proxies(url: str) -> dict:
    """智能获取代理配置"""
    # 直连域名列表（不使用代理）
    direct_domains = [
        'minio.cdutcm.edu.cn',
        'localhost',
        '127.0.0.1',
        '**************'  # 内网地址
    ]

    # 检查是否为直连域名
    from urllib.parse import urlparse
    parsed_url = urlparse(url)
    domain = parsed_url.hostname

    if any(direct_domain in domain for direct_domain in direct_domains):
        print(f"[PROXY] 域名 {domain} 在直连列表中，跳过代理")
        # 返回明确的空代理配置，覆盖环境变量
        return {'http': None, 'https': None}

    # 检查系统代理配置
    import os
    http_proxy = os.environ.get('http_proxy') or os.environ.get('HTTP_PROXY')
    https_proxy = os.environ.get('https_proxy') or os.environ.get('HTTPS_PROXY')

    proxies = {}

    # 检查HTTP代理
    if http_proxy and check_proxy_availability(http_proxy):
        proxies['http'] = http_proxy
        print(f"[PROXY] HTTP代理可用: {http_proxy}")
    elif http_proxy:
        print(f"[PROXY] HTTP代理不可用，跳过: {http_proxy}")
        proxies['http'] = None  # 明确禁用代理

    # 检查HTTPS代理
    if https_proxy and check_proxy_availability(https_proxy):
        proxies['https'] = https_proxy
        print(f"[PROXY] HTTPS代理可用: {https_proxy}")
    elif https_proxy:
        print(f"[PROXY] HTTPS代理不可用，跳过: {https_proxy}")
        proxies['https'] = None  # 明确禁用代理

    return proxies

def download_file(url: str, local_path: str) -> bool:
    """下载文件到本地路径 - 智能代理处理"""
    try:
        # 获取智能代理配置
        proxies = get_smart_proxies(url)

        print(f"[DOWNLOAD] 开始下载: {url}")
        if proxies:
            print(f"[DOWNLOAD] 使用代理: {proxies}")
        else:
            print(f"[DOWNLOAD] 直接连接（无代理）")

        # 发起请求
        response = requests.get(
            url,
            stream=True,
            timeout=300,  # 5分钟超时
            proxies=proxies
        )
        response.raise_for_status()

        # 写入文件
        with open(local_path, 'wb') as f:
            for chunk in response.iter_content(chunk_size=8192):
                f.write(chunk)

        print(f"[DOWNLOAD] 下载成功: {local_path}")
        return True

    except Exception as e:
        print(f"下载文件失败: {str(e)}")

        # 如果使用了代理但失败，尝试直接连接
        if 'proxies' in locals() and proxies:
            print(f"[DOWNLOAD] 代理下载失败，尝试直接连接...")
            try:
                response = requests.get(url, stream=True, timeout=300, proxies={})
                response.raise_for_status()

                with open(local_path, 'wb') as f:
                    for chunk in response.iter_content(chunk_size=8192):
                        f.write(chunk)

                print(f"[DOWNLOAD] 直接连接下载成功: {local_path}")
                return True
            except Exception as e2:
                print(f"[DOWNLOAD] 直接连接也失败: {str(e2)}")

        return False

# GPU健康检查工具
def check_gpu_health(gpu_id: int) -> bool:
    """检查GPU是否可用和健康 - 在Celery worker环境中使用更宽松的检查"""
    try:
        import torch
        import os

        # 基本检查
        if not torch.cuda.is_available():
            return False

        if gpu_id >= torch.cuda.device_count():
            return False

        # 在Celery worker进程中，CUDA上下文可能已经被其他进程初始化
        # 所以我们使用更宽松的检查策略
        try:
            # 尝试获取GPU属性，这比创建tensor更安全
            device_name = torch.cuda.get_device_name(gpu_id)
            memory_info = torch.cuda.get_device_properties(gpu_id)
            return True
        except Exception as device_e:
            # 如果基本设备查询都失败，再尝试简单的tensor操作
            try:
                with torch.cuda.device(gpu_id):
                    test_tensor = torch.zeros(1, device=f'cuda:{gpu_id}')
                    del test_tensor
                    torch.cuda.synchronize()
                return True
            except Exception as tensor_e:
                print(f"[GPU_HEALTH] GPU {gpu_id} 健康检查失败: device_check={device_e}, tensor_check={tensor_e}")
                return False

    except Exception as e:
        print(f"[GPU_HEALTH] GPU {gpu_id} 健康检查异常: {e}")
        return False

# GPU资源清理工具
def clear_gpu_memory(gpu_id: int):
    """清理指定GPU的内存和缓存"""
    try:
        import torch
        import gc
        import os

        # 获取当前进程信息用于调试
        pid = os.getpid()

        # 首先进行GPU健康检查
        if not check_gpu_health(gpu_id):
            print(f"[GPU_CLEANUP] PID {pid}: GPU {gpu_id} 健康检查失败，跳过清理")
            # 尝试通用清理作为备选方案
            try:
                torch.cuda.empty_cache()
                gc.collect()
                print(f"[GPU_CLEANUP] PID {pid}: 执行通用清理作为备选方案")
            except Exception as fallback_e:
                print(f"[GPU_CLEANUP] PID {pid}: 通用清理也失败: {fallback_e}")
            return

        # 检查当前GPU上下文
        try:
            current_device = torch.cuda.current_device()
            print(f"[GPU_CLEANUP] PID {pid}: 当前GPU设备: {current_device}, 目标清理GPU: {gpu_id}")
        except Exception as ctx_e:
            print(f"[GPU_CLEANUP] PID {pid}: 无法获取当前GPU上下文: {ctx_e}")

        # 安全地设置GPU设备
        try:
            torch.cuda.set_device(gpu_id)
            print(f"[GPU_CLEANUP] PID {pid}: 成功设置GPU {gpu_id}")
        except Exception as set_e:
            print(f"[GPU_CLEANUP] PID {pid}: 设置GPU {gpu_id}失败: {set_e}")
            # 尝试不设置设备直接清理
            print(f"[GPU_CLEANUP] PID {pid}: 尝试通用清理方式")

        # 清空GPU缓存 - 使用更安全的方式
        try:
            # 如果有特定GPU上下文，使用with语句
            if gpu_id < torch.cuda.device_count():
                with torch.cuda.device(gpu_id):
                    torch.cuda.empty_cache()
                    torch.cuda.synchronize()
                print(f"[GPU_CLEANUP] PID {pid}: GPU {gpu_id}缓存已清理")
            else:
                # 通用清理
                torch.cuda.empty_cache()
                print(f"[GPU_CLEANUP] PID {pid}: 执行通用GPU缓存清理")
        except Exception as cache_e:
            print(f"[GPU_CLEANUP] PID {pid}: GPU {gpu_id}缓存清理失败: {cache_e}")
            # 最后尝试通用清理
            try:
                torch.cuda.empty_cache()
                print(f"[GPU_CLEANUP] PID {pid}: 通用缓存清理成功")
            except Exception as general_e:
                print(f"[GPU_CLEANUP] PID {pid}: 通用缓存清理也失败: {general_e}")

        # 强制垃圾回收
        gc.collect()
        print(f"[GPU_CLEANUP] PID {pid}: GPU {gpu_id} 内存清理完成")

    except Exception as e:
        import os
        pid = os.getpid()
        print(f"[GPU_CLEANUP] PID {pid}: 清理GPU {gpu_id}内存失败: {str(e)}")
        print(f"[GPU_CLEANUP] PID {pid}: 错误类型: {type(e).__name__}")

        # 尝试最基本的清理
        try:
            import torch
            import gc
            torch.cuda.empty_cache()
            gc.collect()
            print(f"[GPU_CLEANUP] PID {pid}: 基础清理完成")
        except Exception as basic_e:
            print(f"[GPU_CLEANUP] PID {pid}: 基础清理也失败: {basic_e}")

# 改进的模型管理器 - 支持资源清理
class ModelManager:
    def __init__(self):
        self._models = {}
        self._model_locks = {}
        self._load_lock = threading.Lock()
        self._model_usage_count = {}  # 跟踪模型使用次数

    def get_model(self, gpu_id: int):
        """获取指定GPU的模型实例"""
        model_key = f"gpu_{gpu_id}"

        if model_key not in self._models:
            with self._load_lock:
                if model_key not in self._models:
                    print(f"[MODEL_MANAGER] 开始加载模型到GPU {gpu_id}")

                    # 设置GPU环境
                    os.environ['CUDA_VISIBLE_DEVICES'] = str(gpu_id)

                    # 清理GPU内存
                    clear_gpu_memory(gpu_id)

                    # 创建模型实例
                    model = lstmsync_func.LstmSync(
                        batch_size=4,
                        sync_offset=0,
                        scale_h=1.5,
                        scale_w=3,
                        key_file="./key.txt"
                    )

                    self._models[model_key] = model
                    self._model_locks[model_key] = threading.Lock()
                    self._model_usage_count[model_key] = 0
                    print(f"[MODEL_MANAGER] 模型已加载到GPU {gpu_id}")

        # 增加使用计数
        self._model_usage_count[model_key] += 1
        return self._models[model_key], self._model_locks[model_key]

    def release_model(self, gpu_id: int, skip_cleanup: bool = False):
        """释放模型使用计数，并在适当时清理资源"""
        model_key = f"gpu_{gpu_id}"

        if model_key in self._model_usage_count:
            self._model_usage_count[model_key] -= 1
            print(f"[MODEL_MANAGER] GPU {gpu_id} 模型使用计数: {self._model_usage_count[model_key]}")

            # 如果使用计数为0且需要清理，可以选择清理模型
            # 这里暂时保留模型以提高性能，但清理GPU缓存
            if self._model_usage_count[model_key] <= 0 and not skip_cleanup:
                clear_gpu_memory(gpu_id)

    def cleanup_all_models(self):
        """清理所有模型和GPU资源"""
        print("[MODEL_MANAGER] 开始清理所有模型资源")
        for gpu_id in range(2):  # 假设有2个GPU
            clear_gpu_memory(gpu_id)

        # 清空模型缓存
        self._models.clear()
        self._model_locks.clear()
        self._model_usage_count.clear()
        print("[MODEL_MANAGER] 所有模型资源已清理")

# 全局模型管理器
model_manager = ModelManager()

@celery_app.task(name="process_video_gpu0", bind=True)
def process_video_gpu0(self, task_id: str, video_url: str, audio_url: str) -> Dict[str, Any]:
    """GPU 0 专用的视频处理任务"""
    return _process_video_on_gpu(self, task_id, video_url, audio_url, gpu_id=0)

@celery_app.task(name="process_video_gpu1", bind=True)
def process_video_gpu1(self, task_id: str, video_url: str, audio_url: str) -> Dict[str, Any]:
    """GPU 1 专用的视频处理任务"""
    return _process_video_on_gpu(self, task_id, video_url, audio_url, gpu_id=1)

def _process_video_on_gpu(task_instance, task_id: str, video_url: str, audio_url: str, gpu_id: int) -> Dict[str, Any]:
    """在指定GPU上处理视频的任务"""
    start_time = time.time()

    # 添加详细日志
    print(f"[DEBUG] 开始处理任务 {task_id} 在 GPU {gpu_id}")
    print(f"[DEBUG] 视频URL: {video_url}")
    print(f"[DEBUG] 音频URL: {audio_url}")

    # 获取GPU资源
    print(f"[DEBUG] 尝试获取GPU {gpu_id}资源...")
    if not gpu_manager.acquire_gpu(gpu_id):
        print(f"[ERROR] 无法获取GPU {gpu_id}资源")
        status_data = {
            "status": "failed",
            "message": f"无法获取GPU {gpu_id}资源",
            "gpu_id": gpu_id
        }
        save_task_status(task_id, status_data)
        return status_data

    print(f"[DEBUG] 成功获取GPU {gpu_id}资源")
    try:
        # 设置CUDA设备
        print(f"[DEBUG] 设置CUDA_VISIBLE_DEVICES={gpu_id}")
        os.environ['CUDA_VISIBLE_DEVICES'] = str(gpu_id)

        # 更新任务状态
        print(f"[DEBUG] 更新任务状态为processing")
        status_data = {
            "status": "processing",
            "message": "正在下载文件",
            "gpu_id": gpu_id,
            "start_time": start_time
        }
        save_task_status(task_id, status_data)
        print(f"[DEBUG] 任务状态已更新")

        # 创建临时工作目录
        work_dir = tempfile.mkdtemp(prefix=f"lstmsync_gpu{gpu_id}_")

        # 设置文件路径
        video_path = os.path.join(work_dir, "input.mp4")
        audio_path = os.path.join(work_dir, "input.wav")
        video_fps25_path = os.path.join(work_dir, "fps25_temp.mp4")
        video_temp_path = os.path.join(work_dir, "temp.mp4")
        audio_temp_path = os.path.join(work_dir, "temp.wav")
        video_out_path = os.path.join(work_dir, "result.mp4")

        # 下载输入文件
        print(f"[DEBUG] 开始下载视频文件: {video_url}")
        if not download_file(video_url, video_path):
            print(f"[ERROR] 视频文件下载失败")
            status_data = {
                "status": "failed",
                "message": "视频文件下载失败",
                "gpu_id": gpu_id,
                "processing_time": time.time() - start_time
            }
            save_task_status(task_id, status_data)
            return status_data

        print(f"[DEBUG] 视频文件下载成功，开始下载音频文件: {audio_url}")
        if not download_file(audio_url, audio_path):
            status_data = {
                "status": "failed",
                "message": "音频文件下载失败",
                "gpu_id": gpu_id,
                "processing_time": time.time() - start_time
            }
            save_task_status(task_id, status_data)
            return status_data

        # 更新任务状态
        status_data = {
            "status": "processing",
            "message": f"正在GPU {gpu_id}上处理音视频",
            "gpu_id": gpu_id
        }
        save_task_status(task_id, status_data)

        # 使用预加载的模型处理
        try:
            # 确保key.txt文件存在
            key_file = "./key.txt"
            if not os.path.exists(key_file):
                status_data = {
                    "status": "failed",
                    "message": "key.txt文件不存在",
                    "gpu_id": gpu_id,
                    "processing_time": time.time() - start_time
                }
                save_task_status(task_id, status_data)
                return status_data

            # 获取预加载的模型实例
            model, model_lock = model_manager.get_model(gpu_id)

            # 使用模型锁确保线程安全
            with model_lock:
                # 执行处理
                result = model.run(
                    human_path="./checkpoints/192jm.pth",
                    hubert_path="./checkpoints/chinese-hubert-large",
                    video_path=video_path,
                    video_fps25_path=video_fps25_path,
                    video_temp_path=video_temp_path,
                    audio_path=audio_path,
                    audio_temp_path=audio_temp_path,
                    video_out_path=video_out_path
                )

            # 检查处理结果
            if not os.path.exists(video_out_path):
                status_data = {
                    "status": "failed",
                    "message": "处理失败，输出文件不存在",
                    "gpu_id": gpu_id,
                    "processing_time": time.time() - start_time
                }
                save_task_status(task_id, status_data)
                return status_data

            # 上传结果到Minio
            status_data = {
                "status": "processing",
                "message": "正在上传处理结果",
                "gpu_id": gpu_id
            }
            save_task_status(task_id, status_data)

            try:
                # 使用minio_uploader上传文件
                result_url = minio_uploader.upload_to_minio(video_out_path, "video")

                processing_time = time.time() - start_time

                # 更新任务状态
                status_data = {
                    "status": "completed",
                    "result_url": result_url,
                    "message": f"任务在GPU {gpu_id}上处理完成",
                    "gpu_id": gpu_id,
                    "processing_time": processing_time
                }
                save_task_status(task_id, status_data)
            except Exception as e:
                status_data = {
                    "status": "failed",
                    "message": f"上传结果失败: {str(e)}",
                    "gpu_id": gpu_id,
                    "processing_time": time.time() - start_time
                }
                save_task_status(task_id, status_data)

        except Exception as e:
            status_data = {
                "status": "failed",
                "message": f"处理过程中出错: {str(e)}",
                "gpu_id": gpu_id,
                "processing_time": time.time() - start_time
            }
            save_task_status(task_id, status_data)

        # 清理临时文件
        try:
            shutil.rmtree(work_dir)
            print(f"[CLEANUP] 临时目录已清理: {work_dir}")
        except Exception as e:
            print(f"[CLEANUP] 清理临时目录失败: {str(e)}")

        return status_data
    except Exception as e:
        print(f"[ERROR] 任务 {task_id} 执行失败: {str(e)}")
        import traceback
        print(f"[ERROR] 详细错误信息: {traceback.format_exc()}")
        status_data = {
            "status": "failed",
            "message": f"任务执行失败: {str(e)}",
            "gpu_id": gpu_id,
            "processing_time": time.time() - start_time if 'start_time' in locals() else 0
        }
        save_task_status(task_id, status_data)
        return status_data
    finally:
        # 完整的资源清理流程
        print(f"[CLEANUP] 开始清理任务 {task_id} 的GPU {gpu_id} 资源")

        try:
            # 1. 释放模型使用计数（跳过自动清理，稍后统一清理）
            model_manager.release_model(gpu_id, skip_cleanup=True)

            # 2. 释放GPU资源计数
            gpu_manager.release_gpu(gpu_id)

            # 3. 统一清理GPU内存（只调用一次）
            clear_gpu_memory(gpu_id)

            # 4. 强制垃圾回收
            import gc
            gc.collect()

            print(f"[CLEANUP] 任务 {task_id} 的GPU {gpu_id} 资源清理完成")

        except Exception as cleanup_error:
            print(f"[CLEANUP] 资源清理过程中出错: {str(cleanup_error)}")

@app.post("/api/tasks", response_model=TaskResponse)
async def create_task(task_request: TaskRequest, background_tasks: BackgroundTasks):
    """创建新的处理任务 - 智能GPU调度"""
    # 生成唯一任务ID
    task_id = str(uuid.uuid4())

    # 智能调度到最优GPU
    assigned_gpu = gpu_scheduler.schedule_task(task_id, task_request.priority)

    if assigned_gpu is None:
        raise HTTPException(
            status_code=503,
            detail="所有GPU队列已满，请稍后重试"
        )

    # 获取队列位置
    queue_position = gpu_scheduler.get_queue_position(task_id, assigned_gpu)

    # 初始化任务状态
    status_data = {
        "status": "pending",
        "message": f"任务已分配到GPU {assigned_gpu}，队列位置: {queue_position}",
        "gpu_id": assigned_gpu,
        "priority": task_request.priority,
        "queue_position": queue_position
    }
    save_task_status(task_id, status_data)

    # 根据GPU分配启动对应的Celery任务
    if assigned_gpu == 0:
        process_video_gpu0.delay(task_id, task_request.video_url, task_request.audio_url)
    else:
        process_video_gpu1.delay(task_id, task_request.video_url, task_request.audio_url)

    # 返回任务信息
    return TaskResponse(
        task_id=task_id,
        status="success",
        estimated_gpu=assigned_gpu,
        queue_position=queue_position if queue_position > 0 else None
    )

@app.get("/api/tasks/{task_id}", response_model=TaskStatusResponse)
async def get_task_status_endpoint(task_id: str):
    """获取任务状态 - 增强版本"""
    task_info = get_task_status(task_id)

    if not task_info:
        # 尝试从Celery获取任务状态
        celery_task = celery_app.AsyncResult(task_id)
        if celery_task.state == 'SUCCESS':
            # 如果任务成功完成但Redis中没有状态，可能是因为Redis数据丢失
            # 尝试从Celery结果中恢复状态
            task_info = celery_task.result
            if task_info:
                save_task_status(task_id, task_info)
        else:
            raise HTTPException(status_code=404, detail="任务不存在")

    return TaskStatusResponse(
        status=task_info.get("status", "unknown"),
        result_url=task_info.get("result_url"),
        message=task_info.get("message", ""),
        gpu_id=task_info.get("gpu_id"),
        processing_time=task_info.get("processing_time")
    )

@app.get("/api/gpu/stats", response_model=GPUStatsResponse)
async def get_gpu_stats():
    """获取GPU使用统计"""
    stats = gpu_manager.get_gpu_stats()
    return GPUStatsResponse(**stats)

@app.get("/api/gpu/memory")
async def get_gpu_memory():
    """获取GPU内存使用情况"""
    memory_info = get_gpu_memory_info()
    return {
        "gpu_memory": memory_info,
        "timestamp": time.time()
    }

@app.post("/api/gpu/cleanup")
async def cleanup_gpu_resources():
    """手动清理GPU资源"""
    try:
        # 清理所有模型
        model_manager.cleanup_all_models()

        # 清理每个GPU的内存
        for gpu_id in range(GPU_COUNT):
            clear_gpu_memory(gpu_id)

        # 强制垃圾回收
        import gc
        gc.collect()

        # 获取清理后的内存状态
        memory_info = get_gpu_memory_info()

        return {
            "status": "success",
            "message": "GPU资源清理完成",
            "gpu_memory_after_cleanup": memory_info,
            "timestamp": time.time()
        }
    except Exception as e:
        return {
            "status": "error",
            "message": f"GPU资源清理失败: {str(e)}",
            "timestamp": time.time()
        }

@app.get("/api/health")
async def health_check():
    """健康检查接口 - 增强版本"""
    try:
        # 检查Redis连接
        redis_client.ping()

        # 获取GPU统计
        gpu_stats = gpu_manager.get_gpu_stats()

        # 获取GPU内存信息
        gpu_memory = get_gpu_memory_info()

        return {
            "status": "healthy",
            "message": "分布式服务正常运行",
            "gpu_stats": gpu_stats,
            "gpu_memory": gpu_memory,
            "timestamp": time.time()
        }
    except Exception as e:
        return {"status": "unhealthy", "message": f"服务异常: {str(e)}"}

if __name__ == "__main__":
    import uvicorn

    # 检查uvloop是否可用
    try:
        import uvloop
        loop_type = "uvloop"
    except ImportError:
        loop_type = "asyncio"
        print("uvloop不可用，使用默认的asyncio事件循环")

    # 优化的生产配置
    uvicorn.run(
        app,
        host="0.0.0.0",
        port=5028,
        workers=1,  # 单worker避免模型重复加载
        loop=loop_type,  # 使用可用的事件循环
        access_log=True,
        log_level="info"
    )
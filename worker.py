#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import sys
from api_server import celery_app

def main():
    """启动Celery worker"""
    # 获取GPU ID参数
    gpu_id = None
    if len(sys.argv) > 1:
        try:
            gpu_id = int(sys.argv[1])
        except ValueError:
            print("GPU ID必须是整数")
            sys.exit(1)

    if gpu_id is not None:
        # 启动指定GPU的worker
        if gpu_id == 0:
            worker_args = [
                'worker',
                '--loglevel=info',
                '--concurrency=8',  # 每个GPU 8个并发
                '--queues=gpu0_queue',
                '--hostname=worker_gpu0@%h',
                '--prefetch-multiplier=1'
            ]
        elif gpu_id == 1:
            worker_args = [
                'worker',
                '--loglevel=info',
                '--concurrency=8',  # 每个GPU 8个并发
                '--queues=gpu1_queue',
                '--hostname=worker_gpu1@%h',
                '--prefetch-multiplier=1'
            ]
        else:
            print(f"不支持的GPU ID: {gpu_id}")
            sys.exit(1)

        print(f"启动GPU {gpu_id} worker...")
        celery_app.worker_main(worker_args)
    else:
        # 启动默认worker（处理两个GPU队列）
        worker_args = [
            'worker',
            '--loglevel=info',
            '--concurrency=16',  # 总共16个并发
            '--queues=gpu0_queue,gpu1_queue',
            '--hostname=worker_multi@%h',
            '--prefetch-multiplier=1'
        ]
        print("启动多GPU worker...")
        celery_app.worker_main(worker_args)

if __name__ == '__main__':
    main()
/root/miniconda3/envs/lstmsync/lib/python3.8/site-packages/kornia/feature/lightglue.py:44: FutureWarning: `torch.cuda.amp.custom_fwd(args...)` is deprecated. Please use `torch.amp.custom_fwd(args..., device_type='cuda')` instead.
  @torch.cuda.amp.custom_fwd(cast_inputs=torch.float32)
INFO:     Started server process [585528]
INFO:     Waiting for application startup.
INFO:     Application startup complete.
INFO:     Uvicorn running on http://0.0.0.0:5028 (Press CTRL+C to quit)
[SCHEDULER] 当前GPU负载 - GPU0: 1, GPU1: 0
[SCHEDULER] 负载均衡选择的GPU: 1
INFO:     10.200.120.185:64321 - "POST /api/tasks HTTP/1.1" 200 OK
[SCHEDULER] 当前GPU负载 - GPU0: 1, GPU1: 1
[SCHEDULER] 负载均衡选择的GPU: 0
INFO:     10.200.120.185:64321 - "POST /api/tasks HTTP/1.1" 200 OK
[SCHEDULER] 当前GPU负载 - GPU0: 2, GPU1: 1
[SCHEDULER] 负载均衡选择的GPU: 1
INFO:     10.200.120.185:64321 - "POST /api/tasks HTTP/1.1" 200 OK
[SCHEDULER] 当前GPU负载 - GPU0: 2, GPU1: 2
[SCHEDULER] 负载均衡选择的GPU: 0
INFO:     10.200.120.185:64321 - "POST /api/tasks HTTP/1.1" 200 OK
[SCHEDULER] 当前GPU负载 - GPU0: 3, GPU1: 2
[SCHEDULER] 负载均衡选择的GPU: 1
INFO:     10.200.120.185:64321 - "POST /api/tasks HTTP/1.1" 200 OK
[SCHEDULER] 当前GPU负载 - GPU0: 3, GPU1: 3
[SCHEDULER] 负载均衡选择的GPU: 0
INFO:     10.200.120.185:64349 - "POST /api/tasks HTTP/1.1" 200 OK
[SCHEDULER] 当前GPU负载 - GPU0: 4, GPU1: 3
[SCHEDULER] 负载均衡选择的GPU: 1
INFO:     10.200.120.185:64349 - "POST /api/tasks HTTP/1.1" 200 OK
[SCHEDULER] 当前GPU负载 - GPU0: 4, GPU1: 4
[SCHEDULER] 负载均衡选择的GPU: 0
INFO:     10.200.120.185:64349 - "POST /api/tasks HTTP/1.1" 200 OK
[SCHEDULER] 当前GPU负载 - GPU0: 5, GPU1: 4
[SCHEDULER] 负载均衡选择的GPU: 1
INFO:     10.200.120.185:64349 - "POST /api/tasks HTTP/1.1" 200 OK
[SCHEDULER] 当前GPU负载 - GPU0: 5, GPU1: 5
[SCHEDULER] 负载均衡选择的GPU: None
INFO:     10.200.120.185:64349 - "POST /api/tasks HTTP/1.1" 200 OK
INFO:     10.200.120.178:53807 - "GET /api/tasks/adf7e736-4d57-476b-9a98-bab94b694c81 HTTP/1.1" 200 OK
INFO:     10.200.120.178:53857 - "GET /api/tasks/adf7e736-4d57-476b-9a98-bab94b694c81 HTTP/1.1" 200 OK
INFO:     10.200.120.231:64387 - "GET /api/tasks/925510ac-3437-4096-8383-35be37d2f542 HTTP/1.1" 200 OK

 
 -------------- worker_gpu0@test v5.3.6 (emerald-rush)
--- ***** ----- 
-- ******* ---- Linux-5.4.0-211-generic-x86_64-with-glibc2.17 2025-07-31 11:03:20
- *** --- * --- 
- ** ---------- [config]
- ** ---------- .> app:         lstmsync_tasks:0x7fa07e5aa7f0
- ** ---------- .> transport:   redis://localhost:6379/0
- ** ---------- .> results:     redis://localhost:6379/1
- *** --- * --- .> concurrency: 16 (prefork)
-- ******* ---- .> task events: OFF (enable -E to monitor tasks in this worker)
--- ***** ----- 
 -------------- [queues]
                .> gpu0_queue       exchange=default(direct) key=gpu0
                

[tasks]
  . process_video_gpu0
  . process_video_gpu1

/root/miniconda3/envs/lstmsync/lib/python3.8/site-packages/kornia/feature/lightglue.py:44: FutureWarning: `torch.cuda.amp.custom_fwd(args...)` is deprecated. Please use `torch.amp.custom_fwd(args..., device_type='cuda')` instead.
  @torch.cuda.amp.custom_fwd(cast_inputs=torch.float32)
/root/miniconda3/envs/lstmsync/lib/python3.8/site-packages/celery/platforms.py:829: SecurityWarning: You're running the worker with superuser privileges: this is
absolutely not recommended!

Please specify a different user using the --uid option.

User information: uid=0 euid=0 gid=0 egid=0

  warnings.warn(SecurityWarning(ROOT_DISCOURAGED.format(
[2025-07-31 11:03:22,676: INFO/MainProcess] Connected to redis://localhost:6379/0
[2025-07-31 11:03:22,679: INFO/MainProcess] mingle: searching for neighbors
[2025-07-31 11:03:23,687: INFO/MainProcess] mingle: all alone
[2025-07-31 11:03:23,702: INFO/MainProcess] worker_gpu0@test ready.
[2025-07-31 11:03:24,757: INFO/MainProcess] sync with worker_gpu1@test
[2025-07-31 11:04:22,307: INFO/MainProcess] Task process_video_gpu0[cdd90d12-1577-4d2f-92ca-9eee1e8c3044] received
/root/miniconda3/envs/lstmsync/lib/python3.8/site-packages/kornia/feature/lightglue.py:44: FutureWarning: `torch.cuda.amp.custom_fwd(args...)` is deprecated. Please use `torch.amp.custom_fwd(args..., device_type='cuda')` instead.
  @torch.cuda.amp.custom_fwd(cast_inputs=torch.float32)
/root/miniconda3/envs/lstmsync/lib/python3.8/site-packages/celery/platforms.py:829: SecurityWarning: You're running the worker with superuser privileges: this is
absolutely not recommended!

Please specify a different user using the --uid option.

User information: uid=0 euid=0 gid=0 egid=0

  warnings.warn(SecurityWarning(ROOT_DISCOURAGED.format(
[2025-07-31 11:04:22,312: WARNING/ForkPoolWorker-15] [DEBUG] 开始处理任务 aa86ab04-5ae1-4705-8832-f8f7d59e17c5 在 GPU 0
[2025-07-31 11:04:22,312: WARNING/ForkPoolWorker-15] [DEBUG] 视频URL: http://minio.cdutcm.edu.cn/three-service/video/d6972ead1cab01eb1ad765506b2a26fe_1728481024056.mp4
[2025-07-31 11:04:22,312: WARNING/ForkPoolWorker-15] [DEBUG] 音频URL: https://hsobs.cdutcm.edu.cn/bucket-zbkt-orgin/video/55d5105d7819b3f6e7f324c6df669870.MP3?AWSAccessKeyId=S1EHPJGC6CTCM9UGD6CA&Expires=2700009462&Signature=0L5NO0XtGIryBziWph4fQk393fg%3D
[2025-07-31 11:04:22,312: WARNING/ForkPoolWorker-15] [DEBUG] 尝试获取GPU 0资源...
[2025-07-31 11:04:22,316: WARNING/ForkPoolWorker-15] [DEBUG] 成功获取GPU 0资源，当前任务数: 2
[2025-07-31 11:04:22,316: WARNING/ForkPoolWorker-15] [DEBUG] 成功获取GPU 0资源
[2025-07-31 11:04:22,316: WARNING/ForkPoolWorker-15] [DEBUG] 设置CUDA_VISIBLE_DEVICES=0
[2025-07-31 11:04:22,317: WARNING/ForkPoolWorker-15] [DEBUG] 更新任务状态为processing
[2025-07-31 11:04:22,317: WARNING/ForkPoolWorker-15] [DEBUG] 任务状态已更新
[2025-07-31 11:04:22,318: WARNING/ForkPoolWorker-15] [DEBUG] 开始下载视频文件: http://minio.cdutcm.edu.cn/three-service/video/d6972ead1cab01eb1ad765506b2a26fe_1728481024056.mp4
[2025-07-31 11:04:22,318: WARNING/ForkPoolWorker-15] [PROXY] 域名 minio.cdutcm.edu.cn 在直连列表中，跳过代理
[2025-07-31 11:04:22,318: WARNING/ForkPoolWorker-15] [DOWNLOAD] 开始下载: http://minio.cdutcm.edu.cn/three-service/video/d6972ead1cab01eb1ad765506b2a26fe_1728481024056.mp4
[2025-07-31 11:04:22,318: WARNING/ForkPoolWorker-15] [DOWNLOAD] 使用代理: {'http': None, 'https': None}
[2025-07-31 11:04:22,355: WARNING/ForkPoolWorker-15] [DOWNLOAD] 下载成功: /tmp/lstmsync_gpu0_h4993rkw/input.mp4
[2025-07-31 11:04:22,355: WARNING/ForkPoolWorker-15] [DEBUG] 视频文件下载成功，开始下载音频文件: https://hsobs.cdutcm.edu.cn/bucket-zbkt-orgin/video/55d5105d7819b3f6e7f324c6df669870.MP3?AWSAccessKeyId=S1EHPJGC6CTCM9UGD6CA&Expires=2700009462&Signature=0L5NO0XtGIryBziWph4fQk393fg%3D
[2025-07-31 11:04:22,355: WARNING/ForkPoolWorker-15] [PROXY] HTTP代理不可用，跳过: http://127.0.0.1:7890
[2025-07-31 11:04:22,355: WARNING/ForkPoolWorker-15] [PROXY] HTTPS代理不可用，跳过: http://127.0.0.1:7890
[2025-07-31 11:04:22,355: WARNING/ForkPoolWorker-15] [DOWNLOAD] 开始下载: https://hsobs.cdutcm.edu.cn/bucket-zbkt-orgin/video/55d5105d7819b3f6e7f324c6df669870.MP3?AWSAccessKeyId=S1EHPJGC6CTCM9UGD6CA&Expires=2700009462&Signature=0L5NO0XtGIryBziWph4fQk393fg%3D
[2025-07-31 11:04:22,356: WARNING/ForkPoolWorker-15] [DOWNLOAD] 使用代理: {'http': None, 'https': None}
[2025-07-31 11:04:22,425: WARNING/ForkPoolWorker-15] [DOWNLOAD] 下载成功: /tmp/lstmsync_gpu0_h4993rkw/input.wav
[2025-07-31 11:04:22,427: WARNING/ForkPoolWorker-15] [MODEL_MANAGER] 开始加载模型到GPU 0
[2025-07-31 11:04:22,488: WARNING/ForkPoolWorker-15] [GPU_CLEANUP] PID 585985: 当前GPU设备: 0, 目标清理GPU: 0
[2025-07-31 11:04:22,488: WARNING/ForkPoolWorker-15] [GPU_CLEANUP] PID 585985: 成功设置GPU 0
[2025-07-31 11:04:22,624: WARNING/ForkPoolWorker-15] [GPU_CLEANUP] PID 585985: GPU 0缓存已清理
[2025-07-31 11:04:22,936: WARNING/ForkPoolWorker-15] [GPU_CLEANUP] PID 585985: GPU 0 内存清理完成
[2025-07-31 11:04:23,624: WARNING/ForkPoolWorker-15] Applied providers: ['CPUExecutionProvider'], with options: {'CPUExecutionProvider': {}}
[2025-07-31 11:04:23,752: WARNING/ForkPoolWorker-15] model ignore:
[2025-07-31 11:04:23,752: WARNING/ForkPoolWorker-15]  
[2025-07-31 11:04:23,752: WARNING/ForkPoolWorker-15] checkpoints/auxiliary/models/buffalo_l/1k3d68.onnx
[2025-07-31 11:04:23,752: WARNING/ForkPoolWorker-15]  
[2025-07-31 11:04:23,752: WARNING/ForkPoolWorker-15] landmark_3d_68
[2025-07-31 11:04:23,801: WARNING/ForkPoolWorker-15] Applied providers: ['CPUExecutionProvider'], with options: {'CPUExecutionProvider': {}}
[2025-07-31 11:04:23,805: WARNING/ForkPoolWorker-15] find model:
[2025-07-31 11:04:23,806: WARNING/ForkPoolWorker-15]  
[2025-07-31 11:04:23,806: WARNING/ForkPoolWorker-15] checkpoints/auxiliary/models/buffalo_l/2d106det.onnx
[2025-07-31 11:04:23,806: WARNING/ForkPoolWorker-15]  
[2025-07-31 11:04:23,806: WARNING/ForkPoolWorker-15] landmark_2d_106
[2025-07-31 11:04:23,806: WARNING/ForkPoolWorker-15]  
[2025-07-31 11:04:23,806: WARNING/ForkPoolWorker-15] ['None', 3, 192, 192]
[2025-07-31 11:04:23,806: WARNING/ForkPoolWorker-15]  
[2025-07-31 11:04:23,806: WARNING/ForkPoolWorker-15] 0.0
[2025-07-31 11:04:23,806: WARNING/ForkPoolWorker-15]  
[2025-07-31 11:04:23,806: WARNING/ForkPoolWorker-15] 1.0
[2025-07-31 11:04:23,893: WARNING/ForkPoolWorker-15] Applied providers: ['CPUExecutionProvider'], with options: {'CPUExecutionProvider': {}}
[2025-07-31 11:04:23,893: WARNING/ForkPoolWorker-15] find model:
[2025-07-31 11:04:23,893: WARNING/ForkPoolWorker-15]  
[2025-07-31 11:04:23,893: WARNING/ForkPoolWorker-15] checkpoints/auxiliary/models/buffalo_l/det_10g.onnx
[2025-07-31 11:04:23,893: WARNING/ForkPoolWorker-15]  
[2025-07-31 11:04:23,893: WARNING/ForkPoolWorker-15] detection
[2025-07-31 11:04:23,893: WARNING/ForkPoolWorker-15]  
[2025-07-31 11:04:23,893: WARNING/ForkPoolWorker-15] [1, 3, '?', '?']
[2025-07-31 11:04:23,893: WARNING/ForkPoolWorker-15]  
[2025-07-31 11:04:23,893: WARNING/ForkPoolWorker-15] 127.5
[2025-07-31 11:04:23,893: WARNING/ForkPoolWorker-15]  
[2025-07-31 11:04:23,893: WARNING/ForkPoolWorker-15] 128.0
[2025-07-31 11:04:23,931: WARNING/ForkPoolWorker-15] Applied providers: ['CPUExecutionProvider'], with options: {'CPUExecutionProvider': {}}
[2025-07-31 11:04:23,933: WARNING/ForkPoolWorker-15] model ignore:
[2025-07-31 11:04:23,933: WARNING/ForkPoolWorker-15]  
[2025-07-31 11:04:23,933: WARNING/ForkPoolWorker-15] checkpoints/auxiliary/models/buffalo_l/genderage.onnx
[2025-07-31 11:04:23,933: WARNING/ForkPoolWorker-15]  
[2025-07-31 11:04:23,933: WARNING/ForkPoolWorker-15] genderage
[2025-07-31 11:04:24,349: WARNING/ForkPoolWorker-15] Applied providers: ['CPUExecutionProvider'], with options: {'CPUExecutionProvider': {}}
[2025-07-31 11:04:24,479: WARNING/ForkPoolWorker-15] model ignore:
[2025-07-31 11:04:24,480: WARNING/ForkPoolWorker-15]  
[2025-07-31 11:04:24,480: WARNING/ForkPoolWorker-15] checkpoints/auxiliary/models/buffalo_l/w600k_r50.onnx
[2025-07-31 11:04:24,480: WARNING/ForkPoolWorker-15]  
[2025-07-31 11:04:24,480: WARNING/ForkPoolWorker-15] recognition
[2025-07-31 11:04:24,495: WARNING/ForkPoolWorker-15] set det-size:
[2025-07-31 11:04:24,495: WARNING/ForkPoolWorker-15]  
[2025-07-31 11:04:24,495: WARNING/ForkPoolWorker-15] (512, 512)
[2025-07-31 11:04:24,495: WARNING/ForkPoolWorker-15] [MODEL_MANAGER] 模型已加载到GPU 0
[2025-07-31 11:04:24,846: WARNING/ForkPoolWorker-15] Load checkpoint from: ./checkpoints/192jm.pth
[2025-07-31 11:04:25,433: INFO/MainProcess] Task process_video_gpu0[7c126676-1020-4fad-a58b-bdb6c8a4f624] received
/root/miniconda3/envs/lstmsync/lib/python3.8/site-packages/kornia/feature/lightglue.py:44: FutureWarning: `torch.cuda.amp.custom_fwd(args...)` is deprecated. Please use `torch.amp.custom_fwd(args..., device_type='cuda')` instead.
  @torch.cuda.amp.custom_fwd(cast_inputs=torch.float32)
/root/miniconda3/envs/lstmsync/lib/python3.8/site-packages/celery/platforms.py:829: SecurityWarning: You're running the worker with superuser privileges: this is
absolutely not recommended!

Please specify a different user using the --uid option.

User information: uid=0 euid=0 gid=0 egid=0

  warnings.warn(SecurityWarning(ROOT_DISCOURAGED.format(
[2025-07-31 11:04:25,441: WARNING/ForkPoolWorker-16] [DEBUG] 开始处理任务 0cf463e9-8483-4eb7-b304-fd012d964351 在 GPU 0
[2025-07-31 11:04:25,441: WARNING/ForkPoolWorker-16] [DEBUG] 视频URL: http://minio.cdutcm.edu.cn/three-service/video/d6972ead1cab01eb1ad765506b2a26fe_1728481024056.mp4
[2025-07-31 11:04:25,441: WARNING/ForkPoolWorker-16] [DEBUG] 音频URL: https://hsobs.cdutcm.edu.cn/bucket-zbkt-orgin/video/55d5105d7819b3f6e7f324c6df669870.MP3?AWSAccessKeyId=S1EHPJGC6CTCM9UGD6CA&Expires=2700009462&Signature=0L5NO0XtGIryBziWph4fQk393fg%3D
[2025-07-31 11:04:25,441: WARNING/ForkPoolWorker-16] [DEBUG] 尝试获取GPU 0资源...
[2025-07-31 11:04:25,447: WARNING/ForkPoolWorker-16] [DEBUG] 成功获取GPU 0资源，当前任务数: 3
[2025-07-31 11:04:25,448: WARNING/ForkPoolWorker-16] [DEBUG] 成功获取GPU 0资源
[2025-07-31 11:04:25,448: WARNING/ForkPoolWorker-16] [DEBUG] 设置CUDA_VISIBLE_DEVICES=0
[2025-07-31 11:04:25,448: WARNING/ForkPoolWorker-16] [DEBUG] 更新任务状态为processing
[2025-07-31 11:04:25,448: WARNING/ForkPoolWorker-16] [DEBUG] 任务状态已更新
[2025-07-31 11:04:25,449: WARNING/ForkPoolWorker-16] [DEBUG] 开始下载视频文件: http://minio.cdutcm.edu.cn/three-service/video/d6972ead1cab01eb1ad765506b2a26fe_1728481024056.mp4
[2025-07-31 11:04:25,449: WARNING/ForkPoolWorker-16] [PROXY] 域名 minio.cdutcm.edu.cn 在直连列表中，跳过代理
[2025-07-31 11:04:25,449: WARNING/ForkPoolWorker-16] [DOWNLOAD] 开始下载: http://minio.cdutcm.edu.cn/three-service/video/d6972ead1cab01eb1ad765506b2a26fe_1728481024056.mp4
[2025-07-31 11:04:25,449: WARNING/ForkPoolWorker-16] [DOWNLOAD] 使用代理: {'http': None, 'https': None}
[2025-07-31 11:04:25,485: WARNING/ForkPoolWorker-16] [DOWNLOAD] 下载成功: /tmp/lstmsync_gpu0_4czs9q8j/input.mp4
[2025-07-31 11:04:25,485: WARNING/ForkPoolWorker-16] [DEBUG] 视频文件下载成功，开始下载音频文件: https://hsobs.cdutcm.edu.cn/bucket-zbkt-orgin/video/55d5105d7819b3f6e7f324c6df669870.MP3?AWSAccessKeyId=S1EHPJGC6CTCM9UGD6CA&Expires=2700009462&Signature=0L5NO0XtGIryBziWph4fQk393fg%3D
[2025-07-31 11:04:25,485: WARNING/ForkPoolWorker-16] [PROXY] HTTP代理不可用，跳过: http://127.0.0.1:7890
[2025-07-31 11:04:25,486: WARNING/ForkPoolWorker-16] [PROXY] HTTPS代理不可用，跳过: http://127.0.0.1:7890
[2025-07-31 11:04:25,486: WARNING/ForkPoolWorker-16] [DOWNLOAD] 开始下载: https://hsobs.cdutcm.edu.cn/bucket-zbkt-orgin/video/55d5105d7819b3f6e7f324c6df669870.MP3?AWSAccessKeyId=S1EHPJGC6CTCM9UGD6CA&Expires=2700009462&Signature=0L5NO0XtGIryBziWph4fQk393fg%3D
[2025-07-31 11:04:25,486: WARNING/ForkPoolWorker-16] [DOWNLOAD] 使用代理: {'http': None, 'https': None}
[2025-07-31 11:04:25,554: WARNING/ForkPoolWorker-16] [DOWNLOAD] 下载成功: /tmp/lstmsync_gpu0_4czs9q8j/input.wav
[2025-07-31 11:04:25,557: WARNING/ForkPoolWorker-16] [MODEL_MANAGER] 开始加载模型到GPU 0
[2025-07-31 11:04:25,700: WARNING/ForkPoolWorker-16] [GPU_CLEANUP] PID 585987: 当前GPU设备: 0, 目标清理GPU: 0
[2025-07-31 11:04:25,700: WARNING/ForkPoolWorker-16] [GPU_CLEANUP] PID 585987: 成功设置GPU 0
[2025-07-31 11:04:25,819: WARNING/ForkPoolWorker-16] [GPU_CLEANUP] PID 585987: GPU 0缓存已清理
[2025-07-31 11:04:26,052: WARNING/ForkPoolWorker-16] [GPU_CLEANUP] PID 585987: GPU 0 内存清理完成
[2025-07-31 11:04:26,603: WARNING/ForkPoolWorker-16] Applied providers: ['CPUExecutionProvider'], with options: {'CPUExecutionProvider': {}}
[2025-07-31 11:04:26,745: WARNING/ForkPoolWorker-16] model ignore:
[2025-07-31 11:04:26,745: WARNING/ForkPoolWorker-16]  
[2025-07-31 11:04:26,745: WARNING/ForkPoolWorker-16] checkpoints/auxiliary/models/buffalo_l/1k3d68.onnx
[2025-07-31 11:04:26,745: WARNING/ForkPoolWorker-16]  
[2025-07-31 11:04:26,745: WARNING/ForkPoolWorker-16] landmark_3d_68
[2025-07-31 11:04:26,794: WARNING/ForkPoolWorker-16] Applied providers: ['CPUExecutionProvider'], with options: {'CPUExecutionProvider': {}}
[2025-07-31 11:04:26,799: WARNING/ForkPoolWorker-16] find model:
[2025-07-31 11:04:26,799: WARNING/ForkPoolWorker-16]  
[2025-07-31 11:04:26,799: WARNING/ForkPoolWorker-16] checkpoints/auxiliary/models/buffalo_l/2d106det.onnx
[2025-07-31 11:04:26,799: WARNING/ForkPoolWorker-16]  
[2025-07-31 11:04:26,799: WARNING/ForkPoolWorker-16] landmark_2d_106
[2025-07-31 11:04:26,799: WARNING/ForkPoolWorker-16]  
[2025-07-31 11:04:26,799: WARNING/ForkPoolWorker-16] ['None', 3, 192, 192]
[2025-07-31 11:04:26,799: WARNING/ForkPoolWorker-16]  
[2025-07-31 11:04:26,799: WARNING/ForkPoolWorker-16] 0.0
[2025-07-31 11:04:26,799: WARNING/ForkPoolWorker-16]  
[2025-07-31 11:04:26,799: WARNING/ForkPoolWorker-16] 1.0
[2025-07-31 11:04:26,885: WARNING/ForkPoolWorker-16] Applied providers: ['CPUExecutionProvider'], with options: {'CPUExecutionProvider': {}}
[2025-07-31 11:04:26,885: WARNING/ForkPoolWorker-16] find model:
[2025-07-31 11:04:26,885: WARNING/ForkPoolWorker-16]  
[2025-07-31 11:04:26,885: WARNING/ForkPoolWorker-16] checkpoints/auxiliary/models/buffalo_l/det_10g.onnx
[2025-07-31 11:04:26,885: WARNING/ForkPoolWorker-16]  
[2025-07-31 11:04:26,885: WARNING/ForkPoolWorker-16] detection
[2025-07-31 11:04:26,885: WARNING/ForkPoolWorker-16]  
[2025-07-31 11:04:26,885: WARNING/ForkPoolWorker-16] [1, 3, '?', '?']
[2025-07-31 11:04:26,885: WARNING/ForkPoolWorker-16]  
[2025-07-31 11:04:26,885: WARNING/ForkPoolWorker-16] 127.5
[2025-07-31 11:04:26,886: WARNING/ForkPoolWorker-16]  
[2025-07-31 11:04:26,886: WARNING/ForkPoolWorker-16] 128.0
[2025-07-31 11:04:26,917: WARNING/ForkPoolWorker-16] Applied providers: ['CPUExecutionProvider'], with options: {'CPUExecutionProvider': {}}
[2025-07-31 11:04:26,920: WARNING/ForkPoolWorker-16] model ignore:
[2025-07-31 11:04:26,920: WARNING/ForkPoolWorker-16]  
[2025-07-31 11:04:26,920: WARNING/ForkPoolWorker-16] checkpoints/auxiliary/models/buffalo_l/genderage.onnx
[2025-07-31 11:04:26,920: WARNING/ForkPoolWorker-16]  
[2025-07-31 11:04:26,920: WARNING/ForkPoolWorker-16] genderage
ffmpeg version 7.0.2-static https://johnvansickle.com/ffmpeg/  Copyright (c) 2000-2024 the FFmpeg developers
  built with gcc 8 (Debian 8.3.0-6)
  configuration: --enable-gpl --enable-version3 --enable-static --disable-debug --disable-ffplay --disable-indev=sndio --disable-outdev=sndio --cc=gcc --enable-fontconfig --enable-frei0r --enable-gnutls --enable-gmp --enable-libgme --enable-gray --enable-libaom --enable-libfribidi --enable-libass --enable-libvmaf --enable-libfreetype --enable-libmp3lame --enable-libopencore-amrnb --enable-libopencore-amrwb --enable-libopenjpeg --enable-librubberband --enable-libsoxr --enable-libspeex --enable-libsrt --enable-libvorbis --enable-libopus --enable-libtheora --enable-libvidstab --enable-libvo-amrwbenc --enable-libvpx --enable-libwebp --enable-libx264 --enable-libx265 --enable-libxml2 --enable-libdav1d --enable-libxvid --enable-libzvbi --enable-libzimg
  libavutil      59.  8.100 / 59.  8.100
  libavcodec     61.  3.100 / 61.  3.100
  libavformat    61.  1.100 / 61.  1.100
  libavdevice    61.  1.100 / 61.  1.100
  libavfilter    10.  1.100 / 10.  1.100
  libswscale      8.  1.100 /  8.  1.100
  libswresample   5.  1.100 /  5.  1.100
  libpostproc    58.  1.100 / 58.  1.100
Input #0, mov,mp4,m4a,3gp,3g2,mj2, from '/tmp/lstmsync_gpu0_h4993rkw/input.mp4':
  Metadata:
    major_brand     : isom
    minor_version   : 512
    compatible_brands: isomiso2avc1mp41
    encoder         : Lavf58.20.100
  Duration: 00:00:10.01, start: 0.000000, bitrate: 1581 kb/s
  Stream #0:0[0x1](eng): Video: h264 (Main) (avc1 / 0x31637661), yuv420p(progressive), 1280x1280, 1386 kb/s, SAR 1:1 DAR 1:1, 30 fps, 30 tbr, 15360 tbn (default)
      Metadata:
        handler_name    : ?Mainconcept Video Media Handler
        vendor_id       : [0][0][0][0]
  Stream #0:1[0x2](eng): Audio: aac (LC) (mp4a / 0x6134706D), 48000 Hz, stereo, fltp, 187 kb/s (default)
      Metadata:
        handler_name    : #Mainconcept MP4 Sound Media Handler
        vendor_id       : [0][0][0][0]
Stream mapping:
  Stream #0:0 -> #0:0 (h264 (native) -> h264 (libx264))
  Stream #0:1 -> #0:1 (aac (native) -> aac (native))
Press [q] to stop, [?] for help
[libx264 @ 0x2a451ec0] using SAR=1/1
[libx264 @ 0x2a451ec0] using cpu capabilities: MMX2 SSE2Fast SSSE3 SSE4.2 AVX FMA3 BMI2 AVX2 AVX512
[libx264 @ 0x2a451ec0] profile High, level 4.0, 4:2:0, 8-bit
[libx264 @ 0x2a451ec0] 264 - core 164 r3191 4613ac3 - H.264/MPEG-4 AVC codec - Copyleft 2003-2024 - http://www.videolan.org/x264.html - options: cabac=1 ref=3 deblock=1:0:0 analyse=0x3:0x113 me=hex subme=7 psy=1 psy_rd=1.00:0.00 mixed_ref=1 me_range=16 chroma_me=1 trellis=1 8x8dct=1 cqm=0 deadzone=21,11 fast_pskip=1 chroma_qp_offset=-2 threads=40 lookahead_threads=6 sliced_threads=0 nr=0 decimate=1 interlaced=0 bluray_compat=0 constrained_intra=0 bframes=3 b_pyramid=2 b_adapt=1 b_bias=0 direct=1 weightb=1 open_gop=0 weightp=2 keyint=250 keyint_min=25 scenecut=40 intra_refresh=0 rc_lookahead=40 rc=crf mbtree=1 crf=23.0 qcomp=0.60 qpmin=0 qpmax=69 qpstep=4 ip_ratio=1.40 aq=1:1.00
Output #0, mp4, to '/tmp/lstmsync_gpu0_h4993rkw/fps25_temp.mp4':
  Metadata:
    major_brand     : isom
    minor_version   : 512
    compatible_brands: isomiso2avc1mp41
    encoder         : Lavf61.1.100
  Stream #0:0(eng): Video: h264 (avc1 / 0x31637661), yuv420p(progressive), 1280x1280 [SAR 1:1 DAR 1:1], q=2-31, 25 fps, 12800 tbn (default)
      Metadata:
        handler_name    : ?Mainconcept Video Media Handler
        vendor_id       : [0][0][0][0]
        encoder         : Lavc61.3.100 libx264
      Side data:
        cpb: bitrate max/min/avg: 0/0/0 buffer size: 0 vbv_delay: N/A
  Stream #0:1(eng): Audio: aac (LC) (mp4a / 0x6134706D), 48000 Hz, stereo, fltp, 128 kb/s (default)
      Metadata:
        handler_name    : #Mainconcept MP4 Sound Media Handler
        vendor_id       : [0][0][0][0]
        encoder         : Lavc61.3.100 aac
[2025-07-31 11:04:27,437: WARNING/ForkPoolWorker-16] Applied providers: ['CPUExecutionProvider'], with options: {'CPUExecutionProvider': {}}
frame=    0 fps=0.0 q=0.0 size=       0KiB time=N/A bitrate=N/A dup=0 drop=15 speed=N/A    
[2025-07-31 11:04:27,611: WARNING/ForkPoolWorker-16] model ignore:
[2025-07-31 11:04:27,612: WARNING/ForkPoolWorker-16]  
[2025-07-31 11:04:27,612: WARNING/ForkPoolWorker-16] checkpoints/auxiliary/models/buffalo_l/w600k_r50.onnx
[2025-07-31 11:04:27,612: WARNING/ForkPoolWorker-16]  
[2025-07-31 11:04:27,612: WARNING/ForkPoolWorker-16] recognition
[2025-07-31 11:04:27,639: WARNING/ForkPoolWorker-16] set det-size:
[2025-07-31 11:04:27,639: WARNING/ForkPoolWorker-16]  
[2025-07-31 11:04:27,639: WARNING/ForkPoolWorker-16] (512, 512)
[2025-07-31 11:04:27,639: WARNING/ForkPoolWorker-16] [MODEL_MANAGER] 模型已加载到GPU 0
frame=   89 fps= 89 q=28.0 size=     512KiB time=00:00:03.48 bitrate=1205.4kbits/s dup=0 drop=35 speed=3.48x    
frame=  187 fps=125 q=28.0 size=     768KiB time=N/A bitrate=N/A dup=0 drop=48 speed=N/A    
[2025-07-31 11:04:28,812: WARNING/ForkPoolWorker-16] Load checkpoint from: ./checkpoints/192jm.pth
[out#0/mp4 @ 0x2a3f6d40] video:1153KiB audio:156KiB subtitle:0KiB other streams:0KiB global headers:0KiB muxing overhead: 0.675260%
frame=  252 fps=135 q=-1.0 Lsize=    1318KiB time=00:00:10.00 bitrate=1079.8kbits/s dup=0 drop=48 speed=5.36x    
[libx264 @ 0x2a451ec0] frame I:2     Avg QP:16.64  size: 23511
[libx264 @ 0x2a451ec0] frame P:79    Avg QP:17.47  size:  9984
[libx264 @ 0x2a451ec0] frame B:171   Avg QP:20.85  size:  2012
[libx264 @ 0x2a451ec0] consecutive B-frames:  7.1%  4.8%  7.1% 81.0%
[libx264 @ 0x2a451ec0] mb I  I16..4: 33.0% 60.8%  6.2%
[libx264 @ 0x2a451ec0] mb P  I16..4:  3.2%  4.3%  0.3%  P16..4: 22.3%  4.6%  2.0%  0.0%  0.0%    skip:63.2%
[libx264 @ 0x2a451ec0] mb B  I16..4:  0.1%  0.1%  0.0%  B16..8: 18.3%  0.5%  0.0%  direct: 1.8%  skip:79.3%  L0:50.4% L1:48.4% BI: 1.2%
[libx264 @ 0x2a451ec0] 8x8 transform intra:56.1% inter:74.3%
[libx264 @ 0x2a451ec0] coded y,uvDC,uvAC intra: 16.1% 37.9% 5.4% inter: 2.5% 7.7% 0.0%
[libx264 @ 0x2a451ec0] i16 v,h,dc,p: 33% 35% 12% 20%
[libx264 @ 0x2a451ec0] i8 v,h,dc,ddl,ddr,vr,hd,vl,hu: 28% 19% 45%  1%  1%  1%  2%  1%  2%
[libx264 @ 0x2a451ec0] i4 v,h,dc,ddl,ddr,vr,hd,vl,hu: 36% 23% 26%  2%  3%  3%  3%  2%  1%
[libx264 @ 0x2a451ec0] i8c dc,h,v,p: 56% 21% 20%  2%
[libx264 @ 0x2a451ec0] Weighted P-Frames: Y:0.0% UV:0.0%
[libx264 @ 0x2a451ec0] ref P L0: 74.2%  6.1% 15.3%  4.5%
[libx264 @ 0x2a451ec0] ref B L0: 89.7%  9.0%  1.3%
[libx264 @ 0x2a451ec0] ref B L1: 97.0%  3.0%
[libx264 @ 0x2a451ec0] kb/s:936.37
[aac @ 0x2b36ad00] Qavg: 2474.159
[2025-07-31 11:04:28,980: WARNING/ForkPoolWorker-15] Reading video frames...
ffmpeg version 7.0.2-static https://johnvansickle.com/ffmpeg/  Copyright (c) 2000-2024 the FFmpeg developers
  built with gcc 8 (Debian 8.3.0-6)
  configuration: --enable-gpl --enable-version3 --enable-static --disable-debug --disable-ffplay --disable-indev=sndio --disable-outdev=sndio --cc=gcc --enable-fontconfig --enable-frei0r --enable-gnutls --enable-gmp --enable-libgme --enable-gray --enable-libaom --enable-libfribidi --enable-libass --enable-libvmaf --enable-libfreetype --enable-libmp3lame --enable-libopencore-amrnb --enable-libopencore-amrwb --enable-libopenjpeg --enable-librubberband --enable-libsoxr --enable-libspeex --enable-libsrt --enable-libvorbis --enable-libopus --enable-libtheora --enable-libvidstab --enable-libvo-amrwbenc --enable-libvpx --enable-libwebp --enable-libx264 --enable-libx265 --enable-libxml2 --enable-libdav1d --enable-libxvid --enable-libzvbi --enable-libzimg
  libavutil      59.  8.100 / 59.  8.100
  libavcodec     61.  3.100 / 61.  3.100
  libavformat    61.  1.100 / 61.  1.100
  libavdevice    61.  1.100 / 61.  1.100
  libavfilter    10.  1.100 / 10.  1.100
  libswscale      8.  1.100 /  8.  1.100
  libswresample   5.  1.100 /  5.  1.100
  libpostproc    58.  1.100 / 58.  1.100
[mp3 @ 0x10d8cec0] Estimating duration from bitrate, this may be inaccurate
Input #0, mp3, from '/tmp/lstmsync_gpu0_h4993rkw/input.wav':
  Metadata:
    encoder         : LAME3.101 (beta 2)
  Duration: 00:01:19.12, start: 0.000000, bitrate: 128 kb/s
  Stream #0:0: Audio: mp3 (mp3float), 44100 Hz, stereo, fltp, 128 kb/s
Stream mapping:
  Stream #0:0 -> #0:0 (mp3 (mp3float) -> pcm_s16le (native))
Press [q] to stop, [?] for help
Output #0, wav, to '/tmp/lstmsync_gpu0_h4993rkw/temp.wav':
  Metadata:
    ISFT            : Lavf61.1.100
  Stream #0:0: Audio: pcm_s16le ([1][0][0][0] / 0x0001), 16000 Hz, mono, s16, 256 kb/s
      Metadata:
        encoder         : Lavc61.3.100 pcm_s16le
[out#0/wav @ 0x10d91200] video:0KiB audio:2473KiB subtitle:0KiB other streams:0KiB global headers:0KiB muxing overhead: 0.003081%
size=    2473KiB time=00:01:19.12 bitrate= 256.0kbits/s speed= 259x    
Some weights of the model checkpoint at ./checkpoints/chinese-hubert-large were not used when initializing HubertModel: ['hubert.feature_extractor.conv_layers.0.conv.bias', 'hubert.feature_extractor.conv_layers.1.conv.bias', 'hubert.feature_extractor.conv_layers.2.conv.bias', 'hubert.feature_extractor.conv_layers.3.conv.bias', 'hubert.feature_extractor.conv_layers.4.conv.bias', 'hubert.feature_extractor.conv_layers.5.conv.bias', 'hubert.feature_extractor.conv_layers.6.conv.bias', 'lm_head.bias', 'lm_head.weight']
- This IS expected if you are initializing HubertModel from the checkpoint of a model trained on another task or with another architecture (e.g. initializing a BertForSequenceClassification model from a BertForPreTraining model).
- This IS NOT expected if you are initializing HubertModel from the checkpoint of a model that you expect to be exactly identical (initializing a BertForSequenceClassification model from a BertForSequenceClassification model).
ffmpeg version 7.0.2-static https://johnvansickle.com/ffmpeg/  Copyright (c) 2000-2024 the FFmpeg developers
  built with gcc 8 (Debian 8.3.0-6)
  configuration: --enable-gpl --enable-version3 --enable-static --disable-debug --disable-ffplay --disable-indev=sndio --disable-outdev=sndio --cc=gcc --enable-fontconfig --enable-frei0r --enable-gnutls --enable-gmp --enable-libgme --enable-gray --enable-libaom --enable-libfribidi --enable-libass --enable-libvmaf --enable-libfreetype --enable-libmp3lame --enable-libopencore-amrnb --enable-libopencore-amrwb --enable-libopenjpeg --enable-librubberband --enable-libsoxr --enable-libspeex --enable-libsrt --enable-libvorbis --enable-libopus --enable-libtheora --enable-libvidstab --enable-libvo-amrwbenc --enable-libvpx --enable-libwebp --enable-libx264 --enable-libx265 --enable-libxml2 --enable-libdav1d --enable-libxvid --enable-libzvbi --enable-libzimg
  libavutil      59.  8.100 / 59.  8.100
  libavcodec     61.  3.100 / 61.  3.100
  libavformat    61.  1.100 / 61.  1.100
  libavdevice    61.  1.100 / 61.  1.100
  libavfilter    10.  1.100 / 10.  1.100
  libswscale      8.  1.100 /  8.  1.100
  libswresample   5.  1.100 /  5.  1.100
  libpostproc    58.  1.100 / 58.  1.100
Input #0, mov,mp4,m4a,3gp,3g2,mj2, from '/tmp/lstmsync_gpu0_4czs9q8j/input.mp4':
  Metadata:
    major_brand     : isom
    minor_version   : 512
    compatible_brands: isomiso2avc1mp41
    encoder         : Lavf58.20.100
  Duration: 00:00:10.01, start: 0.000000, bitrate: 1581 kb/s
  Stream #0:0[0x1](eng): Video: h264 (Main) (avc1 / 0x31637661), yuv420p(progressive), 1280x1280, 1386 kb/s, SAR 1:1 DAR 1:1, 30 fps, 30 tbr, 15360 tbn (default)
      Metadata:
        handler_name    : ?Mainconcept Video Media Handler
        vendor_id       : [0][0][0][0]
  Stream #0:1[0x2](eng): Audio: aac (LC) (mp4a / 0x6134706D), 48000 Hz, stereo, fltp, 187 kb/s (default)
      Metadata:
        handler_name    : #Mainconcept MP4 Sound Media Handler
        vendor_id       : [0][0][0][0]
Stream mapping:
  Stream #0:0 -> #0:0 (h264 (native) -> h264 (libx264))
  Stream #0:1 -> #0:1 (aac (native) -> aac (native))
Press [q] to stop, [?] for help
[libx264 @ 0x27ffeec0] using SAR=1/1
[libx264 @ 0x27ffeec0] using cpu capabilities: MMX2 SSE2Fast SSSE3 SSE4.2 AVX FMA3 BMI2 AVX2 AVX512
[libx264 @ 0x27ffeec0] profile High, level 4.0, 4:2:0, 8-bit
[libx264 @ 0x27ffeec0] 264 - core 164 r3191 4613ac3 - H.264/MPEG-4 AVC codec - Copyleft 2003-2024 - http://www.videolan.org/x264.html - options: cabac=1 ref=3 deblock=1:0:0 analyse=0x3:0x113 me=hex subme=7 psy=1 psy_rd=1.00:0.00 mixed_ref=1 me_range=16 chroma_me=1 trellis=1 8x8dct=1 cqm=0 deadzone=21,11 fast_pskip=1 chroma_qp_offset=-2 threads=40 lookahead_threads=6 sliced_threads=0 nr=0 decimate=1 interlaced=0 bluray_compat=0 constrained_intra=0 bframes=3 b_pyramid=2 b_adapt=1 b_bias=0 direct=1 weightb=1 open_gop=0 weightp=2 keyint=250 keyint_min=25 scenecut=40 intra_refresh=0 rc_lookahead=40 rc=crf mbtree=1 crf=23.0 qcomp=0.60 qpmin=0 qpmax=69 qpstep=4 ip_ratio=1.40 aq=1:1.00
Output #0, mp4, to '/tmp/lstmsync_gpu0_4czs9q8j/fps25_temp.mp4':
  Metadata:
    major_brand     : isom
    minor_version   : 512
    compatible_brands: isomiso2avc1mp41
    encoder         : Lavf61.1.100
  Stream #0:0(eng): Video: h264 (avc1 / 0x31637661), yuv420p(progressive), 1280x1280 [SAR 1:1 DAR 1:1], q=2-31, 25 fps, 12800 tbn (default)
      Metadata:
        handler_name    : ?Mainconcept Video Media Handler
        vendor_id       : [0][0][0][0]
        encoder         : Lavc61.3.100 libx264
      Side data:
        cpb: bitrate max/min/avg: 0/0/0 buffer size: 0 vbv_delay: N/A
  Stream #0:1(eng): Audio: aac (LC) (mp4a / 0x6134706D), 48000 Hz, stereo, fltp, 128 kb/s (default)
      Metadata:
        handler_name    : #Mainconcept MP4 Sound Media Handler
        vendor_id       : [0][0][0][0]
        encoder         : Lavc61.3.100 aac
[2025-07-31 11:04:32,812: WARNING/ForkPoolWorker-15] Length of rep chunks: 1974
OpenCV: FFMPEG: tag 0x58564944/'DIVX' is not supported with codec id 12 and format 'mp4 / MP4 (MPEG-4 Part 14)'
OpenCV: FFMPEG: fallback to use tag 0x7634706d/'mp4v'
[2025-07-31 11:04:32,839: WARNING/ForkPoolWorker-15] 
  0%|          | 0/494 [00:00<?, ?it/s]
[2025-07-31 11:04:32,840: WARNING/ForkPoolWorker-15] 
  0%|          | 0/252 [00:00<?, ?it/s]
[2025-07-31 11:04:32,840: WARNING/ForkPoolWorker-15] [A
frame=    0 fps=0.0 q=0.0 size=       0KiB time=N/A bitrate=N/A dup=0 drop=9 speed=N/A    
frame=    0 fps=0.0 q=0.0 size=       0KiB time=N/A bitrate=N/A dup=0 drop=15 speed=N/A    
[2025-07-31 11:04:33,588: WARNING/ForkPoolWorker-15] 
  0%|          | 1/252 [00:00<03:07,  1.34it/s]
[2025-07-31 11:04:33,588: WARNING/ForkPoolWorker-15] [A
[2025-07-31 11:04:33,737: WARNING/ForkPoolWorker-15] 
  1%|          | 2/252 [00:00<01:38,  2.53it/s]
[2025-07-31 11:04:33,737: WARNING/ForkPoolWorker-15] [A
[2025-07-31 11:04:33,860: WARNING/ForkPoolWorker-15] 
  1%|1         | 3/252 [00:01<01:07,  3.69it/s]
[2025-07-31 11:04:33,860: WARNING/ForkPoolWorker-15] [A
frame=   31 fps= 21 q=28.0 size=       0KiB time=00:00:01.16 bitrate=   0.3kbits/s dup=0 drop=22 speed=0.773x    
[2025-07-31 11:04:34,069: WARNING/ForkPoolWorker-15] 
  2%|1         | 4/252 [00:01<01:01,  4.05it/s]
[2025-07-31 11:04:34,069: WARNING/ForkPoolWorker-15] [A
[2025-07-31 11:04:34,218: WARNING/ForkPoolWorker-15] 
  2%|1         | 5/252 [00:01<00:52,  4.73it/s]
[2025-07-31 11:04:34,234: WARNING/ForkPoolWorker-15] [A
[2025-07-31 11:04:34,366: WARNING/ForkPoolWorker-15] 
  2%|2         | 6/252 [00:01<00:46,  5.27it/s]
[2025-07-31 11:04:34,366: WARNING/ForkPoolWorker-15] [A
frame=   82 fps= 41 q=28.0 size=     256KiB time=00:00:03.20 bitrate= 655.5kbits/s dup=0 drop=33 speed= 1.6x    
[2025-07-31 11:04:34,524: WARNING/ForkPoolWorker-15] 
  3%|2         | 7/252 [00:01<00:43,  5.57it/s]
[2025-07-31 11:04:34,524: WARNING/ForkPoolWorker-15] [A
[2025-07-31 11:04:34,661: WARNING/ForkPoolWorker-15] 
  3%|3         | 8/252 [00:01<00:40,  6.02it/s]
[2025-07-31 11:04:34,661: WARNING/ForkPoolWorker-15] [A
[2025-07-31 11:04:34,839: WARNING/ForkPoolWorker-15] 
  4%|3         | 9/252 [00:01<00:41,  5.90it/s]
[2025-07-31 11:04:34,839: WARNING/ForkPoolWorker-15] [A
[2025-07-31 11:04:34,984: WARNING/ForkPoolWorker-15] 
  4%|3         | 10/252 [00:02<00:39,  6.17it/s]
[2025-07-31 11:04:34,985: WARNING/ForkPoolWorker-15] [A
frame=  136 fps= 54 q=28.0 size=     768KiB time=00:00:05.36 bitrate=1173.9kbits/s dup=0 drop=44 speed=2.14x    
[2025-07-31 11:04:35,104: WARNING/ForkPoolWorker-15] 
  4%|4         | 11/252 [00:02<00:35,  6.71it/s]
[2025-07-31 11:04:35,104: WARNING/ForkPoolWorker-15] [A
[2025-07-31 11:04:35,312: WARNING/ForkPoolWorker-15] 
  5%|4         | 12/252 [00:02<00:40,  5.99it/s]
[2025-07-31 11:04:35,312: WARNING/ForkPoolWorker-15] [A
frame=  209 fps= 70 q=28.0 size=    1024KiB time=N/A bitrate=N/A dup=0 drop=48 speed=N/A    
[2025-07-31 11:04:35,541: WARNING/ForkPoolWorker-15] 
  5%|5         | 13/252 [00:02<00:44,  5.38it/s]
[2025-07-31 11:04:35,544: WARNING/ForkPoolWorker-15] [A
[2025-07-31 11:04:35,843: WARNING/ForkPoolWorker-15] 
  6%|5         | 14/252 [00:03<00:52,  4.53it/s]
[2025-07-31 11:04:35,843: WARNING/ForkPoolWorker-15] [A
[out#0/mp4 @ 0x27fa3d40] video:1153KiB audio:156KiB subtitle:0KiB other streams:0KiB global headers:0KiB muxing overhead: 0.675260%
frame=  252 fps= 75 q=-1.0 Lsize=    1318KiB time=00:00:10.00 bitrate=1079.8kbits/s dup=0 drop=48 speed=2.98x    
[libx264 @ 0x27ffeec0] frame I:2     Avg QP:16.64  size: 23511
[libx264 @ 0x27ffeec0] frame P:79    Avg QP:17.47  size:  9984
[libx264 @ 0x27ffeec0] frame B:171   Avg QP:20.85  size:  2012
[libx264 @ 0x27ffeec0] consecutive B-frames:  7.1%  4.8%  7.1% 81.0%
[libx264 @ 0x27ffeec0] mb I  I16..4: 33.0% 60.8%  6.2%
[libx264 @ 0x27ffeec0] mb P  I16..4:  3.2%  4.3%  0.3%  P16..4: 22.3%  4.6%  2.0%  0.0%  0.0%    skip:63.2%
[libx264 @ 0x27ffeec0] mb B  I16..4:  0.1%  0.1%  0.0%  B16..8: 18.3%  0.5%  0.0%  direct: 1.8%  skip:79.3%  L0:50.4% L1:48.4% BI: 1.2%
[libx264 @ 0x27ffeec0] 8x8 transform intra:56.1% inter:74.3%
[libx264 @ 0x27ffeec0] coded y,uvDC,uvAC intra: 16.1% 37.9% 5.4% inter: 2.5% 7.7% 0.0%
[libx264 @ 0x27ffeec0] i16 v,h,dc,p: 33% 35% 12% 20%
[libx264 @ 0x27ffeec0] i8 v,h,dc,ddl,ddr,vr,hd,vl,hu: 28% 19% 45%  1%  1%  1%  2%  1%  2%
[libx264 @ 0x27ffeec0] i4 v,h,dc,ddl,ddr,vr,hd,vl,hu: 36% 23% 26%  2%  3%  3%  3%  2%  1%
[libx264 @ 0x27ffeec0] i8c dc,h,v,p: 56% 21% 20%  2%
[libx264 @ 0x27ffeec0] Weighted P-Frames: Y:0.0% UV:0.0%
[libx264 @ 0x27ffeec0] ref P L0: 74.2%  6.1% 15.3%  4.5%
[libx264 @ 0x27ffeec0] ref B L0: 89.7%  9.0%  1.3%
[libx264 @ 0x27ffeec0] ref B L1: 97.0%  3.0%
[libx264 @ 0x27ffeec0] kb/s:936.37
[aac @ 0x28f17d00] Qavg: 2474.159
[2025-07-31 11:04:35,987: WARNING/ForkPoolWorker-16] Reading video frames...
[2025-07-31 11:04:35,994: WARNING/ForkPoolWorker-15] 
  6%|5         | 15/252 [00:03<00:47,  5.00it/s]
[2025-07-31 11:04:35,994: WARNING/ForkPoolWorker-15] [A
[2025-07-31 11:04:36,104: WARNING/ForkPoolWorker-15] 
  6%|6         | 16/252 [00:03<00:40,  5.79it/s]
[2025-07-31 11:04:36,115: WARNING/ForkPoolWorker-15] [A
[2025-07-31 11:04:36,332: WARNING/ForkPoolWorker-15] 
  7%|6         | 17/252 [00:03<00:44,  5.28it/s]
[2025-07-31 11:04:36,332: WARNING/ForkPoolWorker-15] [A
[2025-07-31 11:04:36,512: WARNING/ForkPoolWorker-15] 
  7%|7         | 18/252 [00:03<00:43,  5.36it/s]
[2025-07-31 11:04:36,512: WARNING/ForkPoolWorker-15] [A
[2025-07-31 11:04:36,776: WARNING/ForkPoolWorker-15] 
  8%|7         | 19/252 [00:03<00:48,  4.76it/s]
[2025-07-31 11:04:36,776: WARNING/ForkPoolWorker-15] [A
[2025-07-31 11:04:37,103: WARNING/ForkPoolWorker-15] 
  8%|7         | 20/252 [00:04<00:56,  4.08it/s]
[2025-07-31 11:04:37,103: WARNING/ForkPoolWorker-15] [A
[2025-07-31 11:04:37,283: WARNING/ForkPoolWorker-15] 
  8%|8         | 21/252 [00:04<00:52,  4.44it/s]
[2025-07-31 11:04:37,283: WARNING/ForkPoolWorker-15] [A
[2025-07-31 11:04:37,517: WARNING/ForkPoolWorker-15] 
  9%|8         | 22/252 [00:04<00:52,  4.39it/s]
[2025-07-31 11:04:37,517: WARNING/ForkPoolWorker-15] [A
[2025-07-31 11:04:37,777: WARNING/ForkPoolWorker-15] 
  9%|9         | 23/252 [00:04<00:54,  4.21it/s]
[2025-07-31 11:04:37,777: WARNING/ForkPoolWorker-15] [A
[2025-07-31 11:04:38,072: WARNING/ForkPoolWorker-15] 
 10%|9         | 24/252 [00:05<00:58,  3.92it/s]
[2025-07-31 11:04:38,072: WARNING/ForkPoolWorker-15] [A
[2025-07-31 11:04:38,279: WARNING/ForkPoolWorker-15] 
 10%|9         | 25/252 [00:05<00:54,  4.16it/s]
[2025-07-31 11:04:38,282: WARNING/ForkPoolWorker-15] [A
ffmpeg version 7.0.2-static https://johnvansickle.com/ffmpeg/  Copyright (c) 2000-2024 the FFmpeg developers
  built with gcc 8 (Debian 8.3.0-6)
  configuration: --enable-gpl --enable-version3 --enable-static --disable-debug --disable-ffplay --disable-indev=sndio --disable-outdev=sndio --cc=gcc --enable-fontconfig --enable-frei0r --enable-gnutls --enable-gmp --enable-libgme --enable-gray --enable-libaom --enable-libfribidi --enable-libass --enable-libvmaf --enable-libfreetype --enable-libmp3lame --enable-libopencore-amrnb --enable-libopencore-amrwb --enable-libopenjpeg --enable-librubberband --enable-libsoxr --enable-libspeex --enable-libsrt --enable-libvorbis --enable-libopus --enable-libtheora --enable-libvidstab --enable-libvo-amrwbenc --enable-libvpx --enable-libwebp --enable-libx264 --enable-libx265 --enable-libxml2 --enable-libdav1d --enable-libxvid --enable-libzvbi --enable-libzimg
  libavutil      59.  8.100 / 59.  8.100
  libavcodec     61.  3.100 / 61.  3.100
  libavformat    61.  1.100 / 61.  1.100
  libavdevice    61.  1.100 / 61.  1.100
  libavfilter    10.  1.100 / 10.  1.100
  libswscale      8.  1.100 /  8.  1.100
  libswresample   5.  1.100 /  5.  1.100
  libpostproc    58.  1.100 / 58.  1.100
[mp3 @ 0x1675fec0] Estimating duration from bitrate, this may be inaccurate
Input #0, mp3, from '/tmp/lstmsync_gpu0_4czs9q8j/input.wav':
  Metadata:
    encoder         : LAME3.101 (beta 2)
  Duration: 00:01:19.12, start: 0.000000, bitrate: 128 kb/s
  Stream #0:0: Audio: mp3 (mp3float), 44100 Hz, stereo, fltp, 128 kb/s
Stream mapping:
  Stream #0:0 -> #0:0 (mp3 (mp3float) -> pcm_s16le (native))
Press [q] to stop, [?] for help
Output #0, wav, to '/tmp/lstmsync_gpu0_4czs9q8j/temp.wav':
  Metadata:
    ISFT            : Lavf61.1.100
  Stream #0:0: Audio: pcm_s16le ([1][0][0][0] / 0x0001), 16000 Hz, mono, s16, 256 kb/s
      Metadata:
        encoder         : Lavc61.3.100 pcm_s16le
[2025-07-31 11:04:38,684: WARNING/ForkPoolWorker-15] 
 10%|#         | 26/252 [00:05<01:05,  3.45it/s]
[2025-07-31 11:04:38,684: WARNING/ForkPoolWorker-15] [A
size=     768KiB time=00:00:26.38 bitrate= 238.5kbits/s speed=52.8x    
[2025-07-31 11:04:38,923: WARNING/ForkPoolWorker-15] 
 11%|#         | 27/252 [00:06<01:01,  3.64it/s]
[2025-07-31 11:04:38,923: WARNING/ForkPoolWorker-15] [A
[out#0/wav @ 0x16764200] video:0KiB audio:2473KiB subtitle:0KiB other streams:0KiB global headers:0KiB muxing overhead: 0.003081%
size=    2473KiB time=00:01:19.12 bitrate= 256.0kbits/s speed= 107x    
[2025-07-31 11:04:39,110: WARNING/ForkPoolWorker-15] 
 11%|#1        | 28/252 [00:06<00:55,  4.03it/s]
[2025-07-31 11:04:39,110: WARNING/ForkPoolWorker-15] [A
[2025-07-31 11:04:39,329: WARNING/ForkPoolWorker-15] 
 12%|#1        | 29/252 [00:06<00:53,  4.17it/s]
[2025-07-31 11:04:39,329: WARNING/ForkPoolWorker-15] [A
[2025-07-31 11:04:39,609: WARNING/ForkPoolWorker-15] 
 12%|#1        | 30/252 [00:06<00:55,  3.97it/s]
[2025-07-31 11:04:39,609: WARNING/ForkPoolWorker-15] [A
Some weights of the model checkpoint at ./checkpoints/chinese-hubert-large were not used when initializing HubertModel: ['hubert.feature_extractor.conv_layers.0.conv.bias', 'hubert.feature_extractor.conv_layers.1.conv.bias', 'hubert.feature_extractor.conv_layers.2.conv.bias', 'hubert.feature_extractor.conv_layers.3.conv.bias', 'hubert.feature_extractor.conv_layers.4.conv.bias', 'hubert.feature_extractor.conv_layers.5.conv.bias', 'hubert.feature_extractor.conv_layers.6.conv.bias', 'lm_head.bias', 'lm_head.weight']
- This IS expected if you are initializing HubertModel from the checkpoint of a model trained on another task or with another architecture (e.g. initializing a BertForSequenceClassification model from a BertForPreTraining model).
- This IS NOT expected if you are initializing HubertModel from the checkpoint of a model that you expect to be exactly identical (initializing a BertForSequenceClassification model from a BertForSequenceClassification model).
[2025-07-31 11:04:39,801: WARNING/ForkPoolWorker-15] 
 12%|#2        | 31/252 [00:06<00:51,  4.28it/s]
[2025-07-31 11:04:39,801: WARNING/ForkPoolWorker-15] [A
[2025-07-31 11:04:40,138: WARNING/ForkPoolWorker-15] 
 13%|#2        | 32/252 [00:07<00:58,  3.78it/s]
[2025-07-31 11:04:40,138: WARNING/ForkPoolWorker-15] [A
[2025-07-31 11:04:40,436: WARNING/ForkPoolWorker-15] 
 13%|#3        | 33/252 [00:07<01:00,  3.64it/s]
[2025-07-31 11:04:40,436: WARNING/ForkPoolWorker-15] [A
[2025-07-31 11:04:40,682: WARNING/ForkPoolWorker-15] 
 13%|#3        | 34/252 [00:07<00:58,  3.75it/s]
[2025-07-31 11:04:40,683: WARNING/ForkPoolWorker-15] [A
[2025-07-31 11:04:40,920: WARNING/ForkPoolWorker-15] 
 14%|#3        | 35/252 [00:08<00:55,  3.88it/s]
[2025-07-31 11:04:40,920: WARNING/ForkPoolWorker-15] [A
[2025-07-31 11:04:41,130: WARNING/ForkPoolWorker-15] 
 14%|#4        | 36/252 [00:08<00:52,  4.11it/s]
[2025-07-31 11:04:41,138: WARNING/ForkPoolWorker-15] [A
[2025-07-31 11:04:41,563: WARNING/ForkPoolWorker-15] 
 15%|#4        | 37/252 [00:08<01:04,  3.33it/s]
[2025-07-31 11:04:41,563: WARNING/ForkPoolWorker-15] [A
[2025-07-31 11:04:41,566: WARNING/ForkPoolWorker-16] Length of rep chunks: 1974
OpenCV: FFMPEG: tag 0x58564944/'DIVX' is not supported with codec id 12 and format 'mp4 / MP4 (MPEG-4 Part 14)'
OpenCV: FFMPEG: fallback to use tag 0x7634706d/'mp4v'
[2025-07-31 11:04:41,598: WARNING/ForkPoolWorker-16] 
  0%|          | 0/494 [00:00<?, ?it/s]
[2025-07-31 11:04:41,598: WARNING/ForkPoolWorker-16] 
  0%|          | 0/252 [00:00<?, ?it/s]
[2025-07-31 11:04:41,599: WARNING/ForkPoolWorker-16] [A
[2025-07-31 11:04:41,837: WARNING/ForkPoolWorker-15] 
 15%|#5        | 38/252 [00:08<01:02,  3.42it/s]
[2025-07-31 11:04:41,837: WARNING/ForkPoolWorker-15] [A
[2025-07-31 11:04:42,510: WARNING/ForkPoolWorker-15] 
 15%|#5        | 39/252 [00:09<01:26,  2.46it/s]
[2025-07-31 11:04:42,510: WARNING/ForkPoolWorker-15] [A
[2025-07-31 11:04:42,866: WARNING/ForkPoolWorker-16] 
  0%|          | 1/252 [00:01<05:17,  1.27s/it]
[2025-07-31 11:04:42,869: WARNING/ForkPoolWorker-16] [A
[2025-07-31 11:04:42,870: WARNING/ForkPoolWorker-15] 
 16%|#5        | 40/252 [00:10<01:23,  2.55it/s]
[2025-07-31 11:04:42,870: WARNING/ForkPoolWorker-15] [A
[2025-07-31 11:04:43,158: WARNING/ForkPoolWorker-15] 
 16%|#6        | 41/252 [00:10<01:16,  2.77it/s]
[2025-07-31 11:04:43,158: WARNING/ForkPoolWorker-15] [A
[2025-07-31 11:04:43,342: WARNING/ForkPoolWorker-16] 
  1%|          | 2/252 [00:01<03:20,  1.25it/s]
[2025-07-31 11:04:43,350: WARNING/ForkPoolWorker-16] [A
[2025-07-31 11:04:43,360: WARNING/ForkPoolWorker-15] 
 17%|#6        | 42/252 [00:10<01:05,  3.19it/s]
[2025-07-31 11:04:43,360: WARNING/ForkPoolWorker-15] [A
[2025-07-31 11:04:43,607: WARNING/ForkPoolWorker-16] 
  1%|1         | 3/252 [00:02<02:18,  1.80it/s]
[2025-07-31 11:04:43,607: WARNING/ForkPoolWorker-16] [A
[2025-07-31 11:04:43,612: WARNING/ForkPoolWorker-15] 
 17%|#7        | 43/252 [00:10<01:01,  3.39it/s]
[2025-07-31 11:04:43,612: WARNING/ForkPoolWorker-15] [A
[2025-07-31 11:04:43,958: WARNING/ForkPoolWorker-15] 
 17%|#7        | 44/252 [00:11<01:04,  3.22it/s]
[2025-07-31 11:04:43,958: WARNING/ForkPoolWorker-15] [A
[2025-07-31 11:04:43,959: WARNING/ForkPoolWorker-16] 
  2%|1         | 4/252 [00:02<01:57,  2.10it/s]
[2025-07-31 11:04:43,959: WARNING/ForkPoolWorker-16] [A
[2025-07-31 11:04:44,204: WARNING/ForkPoolWorker-15] 
 18%|#7        | 45/252 [00:11<01:00,  3.44it/s]
[2025-07-31 11:04:44,204: WARNING/ForkPoolWorker-15] [A
[2025-07-31 11:04:44,541: WARNING/ForkPoolWorker-16] 
  2%|1         | 5/252 [00:02<02:06,  1.95it/s]
[2025-07-31 11:04:44,554: WARNING/ForkPoolWorker-16] [A
[2025-07-31 11:04:44,827: WARNING/ForkPoolWorker-16] 
  2%|2         | 6/252 [00:03<01:47,  2.29it/s]
[2025-07-31 11:04:44,844: WARNING/ForkPoolWorker-16] [A
[2025-07-31 11:04:45,008: WARNING/ForkPoolWorker-15] 
 18%|#8        | 46/252 [00:12<01:31,  2.25it/s]
[2025-07-31 11:04:45,009: WARNING/ForkPoolWorker-15] [A
[2025-07-31 11:04:45,275: WARNING/ForkPoolWorker-16] 
  3%|2         | 7/252 [00:03<01:47,  2.27it/s]
[2025-07-31 11:04:45,275: WARNING/ForkPoolWorker-16] [A
[2025-07-31 11:04:45,521: WARNING/ForkPoolWorker-15] 
 19%|#8        | 47/252 [00:12<01:35,  2.15it/s]
[2025-07-31 11:04:45,521: WARNING/ForkPoolWorker-15] [A
[2025-07-31 11:04:45,764: WARNING/ForkPoolWorker-16] 
  3%|3         | 8/252 [00:04<01:51,  2.19it/s]
[2025-07-31 11:04:45,765: WARNING/ForkPoolWorker-16] [A
[2025-07-31 11:04:45,991: WARNING/ForkPoolWorker-15] 
 19%|#9        | 48/252 [00:13<01:35,  2.14it/s]
[2025-07-31 11:04:45,991: WARNING/ForkPoolWorker-15] [A
[2025-07-31 11:04:46,224: WARNING/ForkPoolWorker-16] 
  4%|3         | 9/252 [00:04<01:51,  2.19it/s]
[2025-07-31 11:04:46,250: WARNING/ForkPoolWorker-16] [A
[2025-07-31 11:04:46,576: WARNING/ForkPoolWorker-16] 
  4%|3         | 10/252 [00:04<01:42,  2.35it/s]
[2025-07-31 11:04:46,590: WARNING/ForkPoolWorker-16] [A
[2025-07-31 11:04:46,586: WARNING/ForkPoolWorker-15] 
 19%|#9        | 49/252 [00:13<01:42,  1.98it/s]
[2025-07-31 11:04:46,593: WARNING/ForkPoolWorker-15] [A
[2025-07-31 11:04:46,889: WARNING/ForkPoolWorker-16] 
  4%|4         | 11/252 [00:05<01:34,  2.56it/s]
[2025-07-31 11:04:46,889: WARNING/ForkPoolWorker-16] [A
[2025-07-31 11:04:46,947: WARNING/ForkPoolWorker-15] 
 20%|#9        | 50/252 [00:14<01:33,  2.16it/s]
[2025-07-31 11:04:46,947: WARNING/ForkPoolWorker-15] [A
[2025-07-31 11:04:47,208: WARNING/ForkPoolWorker-16] 
  5%|4         | 12/252 [00:05<01:28,  2.71it/s]
[2025-07-31 11:04:47,211: WARNING/ForkPoolWorker-16] [A
[2025-07-31 11:04:47,287: WARNING/ForkPoolWorker-15] 
 20%|##        | 51/252 [00:14<01:25,  2.35it/s]
[2025-07-31 11:04:47,288: WARNING/ForkPoolWorker-15] [A
[2025-07-31 11:04:47,727: WARNING/ForkPoolWorker-15] 
 21%|##        | 52/252 [00:14<01:25,  2.33it/s]
[2025-07-31 11:04:47,727: WARNING/ForkPoolWorker-15] [A
[2025-07-31 11:04:47,805: WARNING/ForkPoolWorker-16] 
  5%|5         | 13/252 [00:06<01:44,  2.28it/s]
[2025-07-31 11:04:47,805: WARNING/ForkPoolWorker-16] [A
[2025-07-31 11:04:48,069: WARNING/ForkPoolWorker-15] 
 21%|##1       | 53/252 [00:15<01:20,  2.48it/s]
[2025-07-31 11:04:48,097: WARNING/ForkPoolWorker-15] [A
[2025-07-31 11:04:48,240: WARNING/ForkPoolWorker-16] 
  6%|5         | 14/252 [00:06<01:44,  2.29it/s]
[2025-07-31 11:04:48,240: WARNING/ForkPoolWorker-16] [A
[2025-07-31 11:04:48,661: WARNING/ForkPoolWorker-15] 
 21%|##1       | 54/252 [00:15<01:31,  2.17it/s]
[2025-07-31 11:04:48,662: WARNING/ForkPoolWorker-15] [A
[2025-07-31 11:04:48,846: WARNING/ForkPoolWorker-16] 
  6%|5         | 15/252 [00:07<01:55,  2.05it/s]
[2025-07-31 11:04:48,847: WARNING/ForkPoolWorker-16] [A
[2025-07-31 11:04:49,076: WARNING/ForkPoolWorker-15] 
 22%|##1       | 55/252 [00:16<01:27,  2.24it/s]
[2025-07-31 11:04:49,076: WARNING/ForkPoolWorker-15] [A
[2025-07-31 11:04:49,160: WARNING/ForkPoolWorker-16] 
  6%|6         | 16/252 [00:07<01:42,  2.30it/s]
[2025-07-31 11:04:49,160: WARNING/ForkPoolWorker-16] [A
[2025-07-31 11:04:49,519: WARNING/ForkPoolWorker-16] 
  7%|6         | 17/252 [00:07<01:36,  2.42it/s]
[2025-07-31 11:04:49,520: WARNING/ForkPoolWorker-16] [A
[2025-07-31 11:04:49,521: WARNING/ForkPoolWorker-15] 
 22%|##2       | 56/252 [00:16<01:27,  2.24it/s]
[2025-07-31 11:04:49,521: WARNING/ForkPoolWorker-15] [A
[2025-07-31 11:04:49,928: WARNING/ForkPoolWorker-16] 
  7%|7         | 18/252 [00:08<01:36,  2.43it/s]
[2025-07-31 11:04:49,928: WARNING/ForkPoolWorker-16] [A
[2025-07-31 11:04:50,136: WARNING/ForkPoolWorker-15] 
 23%|##2       | 57/252 [00:17<01:36,  2.01it/s]
[2025-07-31 11:04:50,136: WARNING/ForkPoolWorker-15] [A
[2025-07-31 11:04:50,402: WARNING/ForkPoolWorker-16] 
  8%|7         | 19/252 [00:08<01:40,  2.32it/s]
[2025-07-31 11:04:50,402: WARNING/ForkPoolWorker-16] [A
[2025-07-31 11:04:50,551: WARNING/ForkPoolWorker-15] 
 23%|##3       | 58/252 [00:17<01:31,  2.12it/s]
[2025-07-31 11:04:50,551: WARNING/ForkPoolWorker-15] [A
[2025-07-31 11:04:50,801: WARNING/ForkPoolWorker-16] 
  8%|7         | 20/252 [00:09<01:37,  2.38it/s]
[2025-07-31 11:04:50,802: WARNING/ForkPoolWorker-16] [A
[2025-07-31 11:04:50,966: WARNING/ForkPoolWorker-15] 
 23%|##3       | 59/252 [00:18<01:27,  2.20it/s]
[2025-07-31 11:04:50,970: WARNING/ForkPoolWorker-15] [A
[2025-07-31 11:04:51,083: WARNING/ForkPoolWorker-16] 
  8%|8         | 21/252 [00:09<01:27,  2.64it/s]
[2025-07-31 11:04:51,083: WARNING/ForkPoolWorker-16] [A
[2025-07-31 11:04:51,348: WARNING/ForkPoolWorker-15] 
 24%|##3       | 60/252 [00:18<01:23,  2.31it/s]
[2025-07-31 11:04:51,348: WARNING/ForkPoolWorker-15] [A
[2025-07-31 11:04:51,467: WARNING/ForkPoolWorker-16] 
  9%|8         | 22/252 [00:09<01:27,  2.63it/s]
[2025-07-31 11:04:51,486: WARNING/ForkPoolWorker-16] [A
[2025-07-31 11:04:51,790: WARNING/ForkPoolWorker-15] 
 24%|##4       | 61/252 [00:18<01:23,  2.29it/s]
[2025-07-31 11:04:51,791: WARNING/ForkPoolWorker-15] [A
[2025-07-31 11:04:51,976: WARNING/ForkPoolWorker-16] 
  9%|9         | 23/252 [00:10<01:35,  2.39it/s]
[2025-07-31 11:04:51,976: WARNING/ForkPoolWorker-16] [A
[2025-07-31 11:04:52,042: WARNING/ForkPoolWorker-15] 
 25%|##4       | 62/252 [00:19<01:12,  2.63it/s]
[2025-07-31 11:04:52,042: WARNING/ForkPoolWorker-15] [A
[2025-07-31 11:04:52,259: WARNING/ForkPoolWorker-15] 
 25%|##5       | 63/252 [00:19<01:02,  3.02it/s]
[2025-07-31 11:04:52,259: WARNING/ForkPoolWorker-15] [A
[2025-07-31 11:04:52,393: WARNING/ForkPoolWorker-16] 
 10%|9         | 24/252 [00:10<01:35,  2.39it/s]
[2025-07-31 11:04:52,393: WARNING/ForkPoolWorker-16] [A
[2025-07-31 11:04:52,608: WARNING/ForkPoolWorker-15] 
 25%|##5       | 64/252 [00:19<01:03,  2.97it/s]
[2025-07-31 11:04:52,608: WARNING/ForkPoolWorker-15] [A
[2025-07-31 11:04:52,619: WARNING/ForkPoolWorker-16] 
 10%|9         | 25/252 [00:11<01:21,  2.77it/s]
[2025-07-31 11:04:52,619: WARNING/ForkPoolWorker-16] [A
[2025-07-31 11:04:53,084: WARNING/ForkPoolWorker-15] 
 26%|##5       | 65/252 [00:20<01:10,  2.64it/s]
[2025-07-31 11:04:53,084: WARNING/ForkPoolWorker-15] [A
[2025-07-31 11:04:53,111: WARNING/ForkPoolWorker-16] 
 10%|#         | 26/252 [00:11<01:30,  2.50it/s]
[2025-07-31 11:04:53,111: WARNING/ForkPoolWorker-16] [A
[2025-07-31 11:04:53,340: WARNING/ForkPoolWorker-15] 
 26%|##6       | 66/252 [00:20<01:03,  2.93it/s]
[2025-07-31 11:04:53,340: WARNING/ForkPoolWorker-15] [A
[2025-07-31 11:04:53,400: WARNING/ForkPoolWorker-16] 
 11%|#         | 27/252 [00:11<01:22,  2.73it/s]
[2025-07-31 11:04:53,400: WARNING/ForkPoolWorker-16] [A
[2025-07-31 11:04:53,687: WARNING/ForkPoolWorker-15] 
 27%|##6       | 67/252 [00:20<01:03,  2.91it/s]
[2025-07-31 11:04:53,687: WARNING/ForkPoolWorker-15] [A
[2025-07-31 11:04:53,854: WARNING/ForkPoolWorker-16] 
 11%|#1        | 28/252 [00:12<01:27,  2.55it/s]
[2025-07-31 11:04:53,854: WARNING/ForkPoolWorker-16] [A
[2025-07-31 11:04:54,020: INFO/MainProcess] Task process_video_gpu0[78c97625-afa0-4cc0-a558-159313402881] received
/root/miniconda3/envs/lstmsync/lib/python3.8/site-packages/kornia/feature/lightglue.py:44: FutureWarning: `torch.cuda.amp.custom_fwd(args...)` is deprecated. Please use `torch.amp.custom_fwd(args..., device_type='cuda')` instead.
  @torch.cuda.amp.custom_fwd(cast_inputs=torch.float32)
/root/miniconda3/envs/lstmsync/lib/python3.8/site-packages/celery/platforms.py:829: SecurityWarning: You're running the worker with superuser privileges: this is
absolutely not recommended!

Please specify a different user using the --uid option.

User information: uid=0 euid=0 gid=0 egid=0

  warnings.warn(SecurityWarning(ROOT_DISCOURAGED.format(
[2025-07-31 11:04:54,023: WARNING/ForkPoolWorker-1] [DEBUG] 开始处理任务 5a830ec3-6a9f-474d-bac2-3d2596841936 在 GPU 0
[2025-07-31 11:04:54,023: WARNING/ForkPoolWorker-1] [DEBUG] 视频URL: http://minio.cdutcm.edu.cn/three-service/video/d6972ead1cab01eb1ad765506b2a26fe_1728481024056.mp4
[2025-07-31 11:04:54,023: WARNING/ForkPoolWorker-1] [DEBUG] 音频URL: https://hsobs.cdutcm.edu.cn/bucket-zbkt-orgin/video/55d5105d7819b3f6e7f324c6df669870.MP3?AWSAccessKeyId=S1EHPJGC6CTCM9UGD6CA&Expires=2700009462&Signature=0L5NO0XtGIryBziWph4fQk393fg%3D
[2025-07-31 11:04:54,023: WARNING/ForkPoolWorker-1] [DEBUG] 尝试获取GPU 0资源...
[2025-07-31 11:04:54,025: WARNING/ForkPoolWorker-1] [DEBUG] 成功获取GPU 0资源，当前任务数: 4
[2025-07-31 11:04:54,025: WARNING/ForkPoolWorker-1] [DEBUG] 成功获取GPU 0资源
[2025-07-31 11:04:54,025: WARNING/ForkPoolWorker-1] [DEBUG] 设置CUDA_VISIBLE_DEVICES=0
[2025-07-31 11:04:54,025: WARNING/ForkPoolWorker-1] [DEBUG] 更新任务状态为processing
[2025-07-31 11:04:54,025: WARNING/ForkPoolWorker-1] [DEBUG] 任务状态已更新
[2025-07-31 11:04:54,025: WARNING/ForkPoolWorker-1] [DEBUG] 开始下载视频文件: http://minio.cdutcm.edu.cn/three-service/video/d6972ead1cab01eb1ad765506b2a26fe_1728481024056.mp4
[2025-07-31 11:04:54,025: WARNING/ForkPoolWorker-1] [PROXY] 域名 minio.cdutcm.edu.cn 在直连列表中，跳过代理
[2025-07-31 11:04:54,026: WARNING/ForkPoolWorker-1] [DOWNLOAD] 开始下载: http://minio.cdutcm.edu.cn/three-service/video/d6972ead1cab01eb1ad765506b2a26fe_1728481024056.mp4
[2025-07-31 11:04:54,026: WARNING/ForkPoolWorker-1] [DOWNLOAD] 使用代理: {'http': None, 'https': None}
[2025-07-31 11:04:54,065: WARNING/ForkPoolWorker-15] 
 27%|##6       | 68/252 [00:21<01:05,  2.83it/s]
[2025-07-31 11:04:54,066: WARNING/ForkPoolWorker-15] [A
[2025-07-31 11:04:54,072: WARNING/ForkPoolWorker-1] [DOWNLOAD] 下载成功: /tmp/lstmsync_gpu0_d4eghldb/input.mp4
[2025-07-31 11:04:54,072: WARNING/ForkPoolWorker-1] [DEBUG] 视频文件下载成功，开始下载音频文件: https://hsobs.cdutcm.edu.cn/bucket-zbkt-orgin/video/55d5105d7819b3f6e7f324c6df669870.MP3?AWSAccessKeyId=S1EHPJGC6CTCM9UGD6CA&Expires=2700009462&Signature=0L5NO0XtGIryBziWph4fQk393fg%3D
[2025-07-31 11:04:54,072: WARNING/ForkPoolWorker-1] [PROXY] HTTP代理不可用，跳过: http://127.0.0.1:7890
[2025-07-31 11:04:54,072: WARNING/ForkPoolWorker-1] [PROXY] HTTPS代理不可用，跳过: http://127.0.0.1:7890
[2025-07-31 11:04:54,073: WARNING/ForkPoolWorker-1] [DOWNLOAD] 开始下载: https://hsobs.cdutcm.edu.cn/bucket-zbkt-orgin/video/55d5105d7819b3f6e7f324c6df669870.MP3?AWSAccessKeyId=S1EHPJGC6CTCM9UGD6CA&Expires=2700009462&Signature=0L5NO0XtGIryBziWph4fQk393fg%3D
[2025-07-31 11:04:54,073: WARNING/ForkPoolWorker-1] [DOWNLOAD] 使用代理: {'http': None, 'https': None}
[2025-07-31 11:04:54,110: WARNING/ForkPoolWorker-16] 
 12%|#1        | 29/252 [00:12<01:18,  2.84it/s]
[2025-07-31 11:04:54,110: WARNING/ForkPoolWorker-16] [A
[2025-07-31 11:04:54,158: WARNING/ForkPoolWorker-1] [DOWNLOAD] 下载成功: /tmp/lstmsync_gpu0_d4eghldb/input.wav
[2025-07-31 11:04:54,160: WARNING/ForkPoolWorker-1] [MODEL_MANAGER] 开始加载模型到GPU 0
[2025-07-31 11:04:54,421: WARNING/ForkPoolWorker-15] 
 27%|##7       | 69/252 [00:21<01:04,  2.82it/s]
[2025-07-31 11:04:54,421: WARNING/ForkPoolWorker-15] [A
[2025-07-31 11:04:54,524: WARNING/ForkPoolWorker-1] [GPU_CLEANUP] PID 585896: 当前GPU设备: 0, 目标清理GPU: 0
[2025-07-31 11:04:54,524: WARNING/ForkPoolWorker-1] [GPU_CLEANUP] PID 585896: 成功设置GPU 0
[2025-07-31 11:04:54,640: WARNING/ForkPoolWorker-16] 
 12%|#1        | 30/252 [00:13<01:29,  2.47it/s]
[2025-07-31 11:04:54,640: WARNING/ForkPoolWorker-16] [A
[2025-07-31 11:04:54,662: WARNING/ForkPoolWorker-15] 
 28%|##7       | 70/252 [00:21<00:58,  3.12it/s]
[2025-07-31 11:04:54,662: WARNING/ForkPoolWorker-15] [A
[2025-07-31 11:04:54,700: WARNING/ForkPoolWorker-1] [GPU_CLEANUP] PID 585896: GPU 0缓存已清理
[2025-07-31 11:04:54,881: WARNING/ForkPoolWorker-16] 
 12%|#2        | 31/252 [00:13<01:18,  2.81it/s]
[2025-07-31 11:04:54,881: WARNING/ForkPoolWorker-16] [A
[2025-07-31 11:04:55,020: WARNING/ForkPoolWorker-15] 
 28%|##8       | 71/252 [00:22<01:00,  3.02it/s]
[2025-07-31 11:04:55,053: WARNING/ForkPoolWorker-15] [A
[2025-07-31 11:04:55,270: WARNING/ForkPoolWorker-16] 
 13%|#2        | 32/252 [00:13<01:20,  2.73it/s]
[2025-07-31 11:04:55,278: WARNING/ForkPoolWorker-16] [A
[2025-07-31 11:04:55,297: WARNING/ForkPoolWorker-1] [GPU_CLEANUP] PID 585896: GPU 0 内存清理完成
[2025-07-31 11:04:55,428: WARNING/ForkPoolWorker-15] 
 29%|##8       | 72/252 [00:22<01:03,  2.82it/s]
[2025-07-31 11:04:55,428: WARNING/ForkPoolWorker-15] [A
[2025-07-31 11:04:55,658: WARNING/ForkPoolWorker-16] 
 13%|#3        | 33/252 [00:14<01:21,  2.68it/s]
[2025-07-31 11:04:55,658: WARNING/ForkPoolWorker-16] [A
[2025-07-31 11:04:55,793: WARNING/ForkPoolWorker-15] 
 29%|##8       | 73/252 [00:22<01:04,  2.80it/s]
[2025-07-31 11:04:55,793: WARNING/ForkPoolWorker-15] [A
[2025-07-31 11:04:56,072: WARNING/ForkPoolWorker-15] 
 29%|##9       | 74/252 [00:23<00:59,  2.99it/s]
[2025-07-31 11:04:56,072: WARNING/ForkPoolWorker-16] 
 13%|#3        | 34/252 [00:14<01:23,  2.60it/s]
[2025-07-31 11:04:56,072: WARNING/ForkPoolWorker-15] [A
[2025-07-31 11:04:56,072: WARNING/ForkPoolWorker-16] [A
[2025-07-31 11:04:56,334: INFO/MainProcess] Task process_video_gpu0[b3f105cc-170d-4774-85b0-2b5f7b8d47e8] received
/root/miniconda3/envs/lstmsync/lib/python3.8/site-packages/kornia/feature/lightglue.py:44: FutureWarning: `torch.cuda.amp.custom_fwd(args...)` is deprecated. Please use `torch.amp.custom_fwd(args..., device_type='cuda')` instead.
  @torch.cuda.amp.custom_fwd(cast_inputs=torch.float32)
/root/miniconda3/envs/lstmsync/lib/python3.8/site-packages/celery/platforms.py:829: SecurityWarning: You're running the worker with superuser privileges: this is
absolutely not recommended!

Please specify a different user using the --uid option.

User information: uid=0 euid=0 gid=0 egid=0

  warnings.warn(SecurityWarning(ROOT_DISCOURAGED.format(
[2025-07-31 11:04:56,337: WARNING/ForkPoolWorker-2] [DEBUG] 开始处理任务 0aa24898-d728-4fc9-b565-4acda0461c61 在 GPU 0
[2025-07-31 11:04:56,337: WARNING/ForkPoolWorker-2] [DEBUG] 视频URL: http://minio.cdutcm.edu.cn/three-service/video/d6972ead1cab01eb1ad765506b2a26fe_1728481024056.mp4
[2025-07-31 11:04:56,337: WARNING/ForkPoolWorker-2] [DEBUG] 音频URL: https://hsobs.cdutcm.edu.cn/bucket-zbkt-orgin/video/55d5105d7819b3f6e7f324c6df669870.MP3?AWSAccessKeyId=S1EHPJGC6CTCM9UGD6CA&Expires=2700009462&Signature=0L5NO0XtGIryBziWph4fQk393fg%3D
[2025-07-31 11:04:56,337: WARNING/ForkPoolWorker-2] [DEBUG] 尝试获取GPU 0资源...
[2025-07-31 11:04:56,339: WARNING/ForkPoolWorker-2] [DEBUG] 成功获取GPU 0资源，当前任务数: 5
[2025-07-31 11:04:56,339: WARNING/ForkPoolWorker-2] [DEBUG] 成功获取GPU 0资源
[2025-07-31 11:04:56,339: WARNING/ForkPoolWorker-2] [DEBUG] 设置CUDA_VISIBLE_DEVICES=0
[2025-07-31 11:04:56,339: WARNING/ForkPoolWorker-2] [DEBUG] 更新任务状态为processing
[2025-07-31 11:04:56,339: WARNING/ForkPoolWorker-2] [DEBUG] 任务状态已更新
[2025-07-31 11:04:56,339: WARNING/ForkPoolWorker-2] [DEBUG] 开始下载视频文件: http://minio.cdutcm.edu.cn/three-service/video/d6972ead1cab01eb1ad765506b2a26fe_1728481024056.mp4
[2025-07-31 11:04:56,339: WARNING/ForkPoolWorker-2] [PROXY] 域名 minio.cdutcm.edu.cn 在直连列表中，跳过代理
[2025-07-31 11:04:56,339: WARNING/ForkPoolWorker-2] [DOWNLOAD] 开始下载: http://minio.cdutcm.edu.cn/three-service/video/d6972ead1cab01eb1ad765506b2a26fe_1728481024056.mp4
[2025-07-31 11:04:56,340: WARNING/ForkPoolWorker-2] [DOWNLOAD] 使用代理: {'http': None, 'https': None}
[2025-07-31 11:04:56,383: WARNING/ForkPoolWorker-2] [DOWNLOAD] 下载成功: /tmp/lstmsync_gpu0_hct5x_rq/input.mp4
[2025-07-31 11:04:56,383: WARNING/ForkPoolWorker-2] [DEBUG] 视频文件下载成功，开始下载音频文件: https://hsobs.cdutcm.edu.cn/bucket-zbkt-orgin/video/55d5105d7819b3f6e7f324c6df669870.MP3?AWSAccessKeyId=S1EHPJGC6CTCM9UGD6CA&Expires=2700009462&Signature=0L5NO0XtGIryBziWph4fQk393fg%3D
[2025-07-31 11:04:56,384: WARNING/ForkPoolWorker-2] [PROXY] HTTP代理不可用，跳过: http://127.0.0.1:7890
[2025-07-31 11:04:56,384: WARNING/ForkPoolWorker-2] [PROXY] HTTPS代理不可用，跳过: http://127.0.0.1:7890
[2025-07-31 11:04:56,384: WARNING/ForkPoolWorker-2] [DOWNLOAD] 开始下载: https://hsobs.cdutcm.edu.cn/bucket-zbkt-orgin/video/55d5105d7819b3f6e7f324c6df669870.MP3?AWSAccessKeyId=S1EHPJGC6CTCM9UGD6CA&Expires=2700009462&Signature=0L5NO0XtGIryBziWph4fQk393fg%3D
[2025-07-31 11:04:56,384: WARNING/ForkPoolWorker-2] [DOWNLOAD] 使用代理: {'http': None, 'https': None}
[2025-07-31 11:04:56,441: WARNING/ForkPoolWorker-16] 
 14%|#3        | 35/252 [00:14<01:22,  2.63it/s]
[2025-07-31 11:04:56,458: WARNING/ForkPoolWorker-16] [A
[2025-07-31 11:04:56,682: WARNING/ForkPoolWorker-2] [DOWNLOAD] 下载成功: /tmp/lstmsync_gpu0_hct5x_rq/input.wav
[2025-07-31 11:04:56,684: WARNING/ForkPoolWorker-2] [MODEL_MANAGER] 开始加载模型到GPU 0
[2025-07-31 11:04:56,698: WARNING/ForkPoolWorker-15] 
 30%|##9       | 75/252 [00:23<01:14,  2.37it/s]
[2025-07-31 11:04:56,704: WARNING/ForkPoolWorker-15] [A
[2025-07-31 11:04:56,760: WARNING/ForkPoolWorker-2] [GPU_CLEANUP] PID 585897: 当前GPU设备: 0, 目标清理GPU: 0
[2025-07-31 11:04:56,760: WARNING/ForkPoolWorker-2] [GPU_CLEANUP] PID 585897: 成功设置GPU 0
[2025-07-31 11:04:56,872: WARNING/ForkPoolWorker-15] 
 30%|###       | 76/252 [00:24<01:01,  2.88it/s]
[2025-07-31 11:04:56,882: WARNING/ForkPoolWorker-15] [A
[2025-07-31 11:04:57,042: WARNING/ForkPoolWorker-2] [GPU_CLEANUP] PID 585897: GPU 0缓存已清理
[2025-07-31 11:04:57,055: WARNING/ForkPoolWorker-1] Applied providers: ['CPUExecutionProvider'], with options: {'CPUExecutionProvider': {}}
[2025-07-31 11:04:57,068: WARNING/ForkPoolWorker-16] 
 14%|#4        | 36/252 [00:15<01:38,  2.20it/s]
[2025-07-31 11:04:57,068: WARNING/ForkPoolWorker-16] [A
[2025-07-31 11:04:57,272: WARNING/ForkPoolWorker-15] 
 31%|###       | 77/252 [00:24<01:03,  2.75it/s]
[2025-07-31 11:04:57,272: WARNING/ForkPoolWorker-15] [A
[2025-07-31 11:04:57,414: WARNING/ForkPoolWorker-16] 
 15%|#4        | 37/252 [00:15<01:30,  2.37it/s]
[2025-07-31 11:04:57,434: WARNING/ForkPoolWorker-16] [A
[2025-07-31 11:04:57,434: WARNING/ForkPoolWorker-1] model ignore:
[2025-07-31 11:04:57,434: WARNING/ForkPoolWorker-1]  
[2025-07-31 11:04:57,434: WARNING/ForkPoolWorker-1] checkpoints/auxiliary/models/buffalo_l/1k3d68.onnx
[2025-07-31 11:04:57,434: WARNING/ForkPoolWorker-1]  
[2025-07-31 11:04:57,434: WARNING/ForkPoolWorker-1] landmark_3d_68
[2025-07-31 11:04:57,559: WARNING/ForkPoolWorker-1] Applied providers: ['CPUExecutionProvider'], with options: {'CPUExecutionProvider': {}}
[2025-07-31 11:04:57,573: WARNING/ForkPoolWorker-1] find model:
[2025-07-31 11:04:57,577: WARNING/ForkPoolWorker-1]  
[2025-07-31 11:04:57,577: WARNING/ForkPoolWorker-1] checkpoints/auxiliary/models/buffalo_l/2d106det.onnx
[2025-07-31 11:04:57,577: WARNING/ForkPoolWorker-1]  
[2025-07-31 11:04:57,577: WARNING/ForkPoolWorker-1] landmark_2d_106
[2025-07-31 11:04:57,577: WARNING/ForkPoolWorker-1]  
[2025-07-31 11:04:57,577: WARNING/ForkPoolWorker-1] ['None', 3, 192, 192]
[2025-07-31 11:04:57,578: WARNING/ForkPoolWorker-1]  
[2025-07-31 11:04:57,578: WARNING/ForkPoolWorker-1] 0.0
[2025-07-31 11:04:57,578: WARNING/ForkPoolWorker-1]  
[2025-07-31 11:04:57,578: WARNING/ForkPoolWorker-1] 1.0
[2025-07-31 11:04:57,738: WARNING/ForkPoolWorker-2] [GPU_CLEANUP] PID 585897: GPU 0 内存清理完成
[2025-07-31 11:04:57,788: WARNING/ForkPoolWorker-16] 
 15%|#5        | 38/252 [00:16<01:27,  2.45it/s]
[2025-07-31 11:04:57,788: WARNING/ForkPoolWorker-16] [A
[2025-07-31 11:04:57,950: WARNING/ForkPoolWorker-1] Applied providers: ['CPUExecutionProvider'], with options: {'CPUExecutionProvider': {}}
[2025-07-31 11:04:57,950: WARNING/ForkPoolWorker-1] find model:
[2025-07-31 11:04:57,950: WARNING/ForkPoolWorker-1]  
[2025-07-31 11:04:57,950: WARNING/ForkPoolWorker-1] checkpoints/auxiliary/models/buffalo_l/det_10g.onnx
[2025-07-31 11:04:57,950: WARNING/ForkPoolWorker-1]  
[2025-07-31 11:04:57,950: WARNING/ForkPoolWorker-1] detection
[2025-07-31 11:04:57,950: WARNING/ForkPoolWorker-1]  
[2025-07-31 11:04:57,950: WARNING/ForkPoolWorker-1] [1, 3, '?', '?']
[2025-07-31 11:04:57,950: WARNING/ForkPoolWorker-1]  
[2025-07-31 11:04:57,950: WARNING/ForkPoolWorker-1] 127.5
[2025-07-31 11:04:57,950: WARNING/ForkPoolWorker-1]  
[2025-07-31 11:04:57,950: WARNING/ForkPoolWorker-1] 128.0
[2025-07-31 11:04:58,035: WARNING/ForkPoolWorker-15] 
 31%|###       | 78/252 [00:25<01:24,  2.07it/s]
[2025-07-31 11:04:58,035: WARNING/ForkPoolWorker-15] [A
[2025-07-31 11:04:58,079: WARNING/ForkPoolWorker-1] Applied providers: ['CPUExecutionProvider'], with options: {'CPUExecutionProvider': {}}
[2025-07-31 11:04:58,081: WARNING/ForkPoolWorker-1] model ignore:
[2025-07-31 11:04:58,081: WARNING/ForkPoolWorker-1]  
[2025-07-31 11:04:58,081: WARNING/ForkPoolWorker-1] checkpoints/auxiliary/models/buffalo_l/genderage.onnx
[2025-07-31 11:04:58,082: WARNING/ForkPoolWorker-1]  
[2025-07-31 11:04:58,082: WARNING/ForkPoolWorker-1] genderage
[2025-07-31 11:04:58,083: WARNING/ForkPoolWorker-16] 
 15%|#5        | 39/252 [00:16<01:19,  2.68it/s]
[2025-07-31 11:04:58,083: WARNING/ForkPoolWorker-16] [A
[2025-07-31 11:04:58,476: INFO/MainProcess] Task process_video_gpu0[c4403826-9e65-4734-8de3-5b76225c2374] received
/root/miniconda3/envs/lstmsync/lib/python3.8/site-packages/kornia/feature/lightglue.py:44: FutureWarning: `torch.cuda.amp.custom_fwd(args...)` is deprecated. Please use `torch.amp.custom_fwd(args..., device_type='cuda')` instead.
  @torch.cuda.amp.custom_fwd(cast_inputs=torch.float32)
/root/miniconda3/envs/lstmsync/lib/python3.8/site-packages/celery/platforms.py:829: SecurityWarning: You're running the worker with superuser privileges: this is
absolutely not recommended!

Please specify a different user using the --uid option.

User information: uid=0 euid=0 gid=0 egid=0

  warnings.warn(SecurityWarning(ROOT_DISCOURAGED.format(
[2025-07-31 11:04:58,480: WARNING/ForkPoolWorker-3] [DEBUG] 开始处理任务 adf7e736-4d57-476b-9a98-bab94b694c81 在 GPU 0
[2025-07-31 11:04:58,480: WARNING/ForkPoolWorker-3] [DEBUG] 视频URL: http://minio.cdutcm.edu.cn/three-service/video/d6972ead1cab01eb1ad765506b2a26fe_1728481024056.mp4
[2025-07-31 11:04:58,480: WARNING/ForkPoolWorker-3] [DEBUG] 音频URL: https://hsobs.cdutcm.edu.cn/bucket-zbkt-orgin/video/55d5105d7819b3f6e7f324c6df669870.MP3?AWSAccessKeyId=S1EHPJGC6CTCM9UGD6CA&Expires=2700009462&Signature=0L5NO0XtGIryBziWph4fQk393fg%3D
[2025-07-31 11:04:58,480: WARNING/ForkPoolWorker-3] [DEBUG] 尝试获取GPU 0资源...
[2025-07-31 11:04:58,481: WARNING/ForkPoolWorker-3] [ERROR] 无法获取GPU 0资源
[2025-07-31 11:04:58,483: INFO/ForkPoolWorker-3] Task process_video_gpu0[c4403826-9e65-4734-8de3-5b76225c2374] succeeded in 0.005066123005235568s: {'status': 'failed', 'message': '无法获取GPU 0资源', 'gpu_id': 0, 'updated_at': 1753931098.481526}
[2025-07-31 11:04:58,536: WARNING/ForkPoolWorker-15] 
 31%|###1      | 79/252 [00:25<01:24,  2.05it/s]
[2025-07-31 11:04:58,536: WARNING/ForkPoolWorker-15] [A
[2025-07-31 11:04:58,728: WARNING/ForkPoolWorker-16] 
 16%|#5        | 40/252 [00:17<01:36,  2.20it/s]
[2025-07-31 11:04:58,728: WARNING/ForkPoolWorker-16] [A
[2025-07-31 11:04:58,973: WARNING/ForkPoolWorker-15] 
 32%|###1      | 80/252 [00:26<01:21,  2.11it/s]
[2025-07-31 11:04:58,973: WARNING/ForkPoolWorker-15] [A
[2025-07-31 11:04:59,160: WARNING/ForkPoolWorker-16] 
 16%|#6        | 41/252 [00:17<01:34,  2.23it/s]
[2025-07-31 11:04:59,160: WARNING/ForkPoolWorker-16] [A
[2025-07-31 11:04:59,383: WARNING/ForkPoolWorker-2] Applied providers: ['CPUExecutionProvider'], with options: {'CPUExecutionProvider': {}}
[2025-07-31 11:04:59,445: WARNING/ForkPoolWorker-15] 
 32%|###2      | 81/252 [00:26<01:20,  2.12it/s]
[2025-07-31 11:04:59,445: WARNING/ForkPoolWorker-15] [A
[2025-07-31 11:04:59,493: WARNING/ForkPoolWorker-16] 
 17%|#6        | 42/252 [00:17<01:26,  2.42it/s]
[2025-07-31 11:04:59,493: WARNING/ForkPoolWorker-16] [A
[2025-07-31 11:04:59,593: WARNING/ForkPoolWorker-1] Applied providers: ['CPUExecutionProvider'], with options: {'CPUExecutionProvider': {}}
[2025-07-31 11:04:59,713: WARNING/ForkPoolWorker-16] 
 17%|#7        | 43/252 [00:18<01:14,  2.81it/s]
[2025-07-31 11:04:59,722: WARNING/ForkPoolWorker-16] [A
[2025-07-31 11:04:59,782: WARNING/ForkPoolWorker-2] model ignore:
[2025-07-31 11:04:59,806: WARNING/ForkPoolWorker-2]  
[2025-07-31 11:04:59,806: WARNING/ForkPoolWorker-2] checkpoints/auxiliary/models/buffalo_l/1k3d68.onnx
[2025-07-31 11:04:59,806: WARNING/ForkPoolWorker-2]  
[2025-07-31 11:04:59,806: WARNING/ForkPoolWorker-2] landmark_3d_68
[2025-07-31 11:04:59,858: WARNING/ForkPoolWorker-15] 
 33%|###2      | 82/252 [00:27<01:17,  2.20it/s]
[2025-07-31 11:04:59,858: WARNING/ForkPoolWorker-15] [A
[2025-07-31 11:04:59,908: WARNING/ForkPoolWorker-2] Applied providers: ['CPUExecutionProvider'], with options: {'CPUExecutionProvider': {}}
[2025-07-31 11:04:59,911: WARNING/ForkPoolWorker-1] model ignore:
[2025-07-31 11:04:59,911: WARNING/ForkPoolWorker-1]  
[2025-07-31 11:04:59,911: WARNING/ForkPoolWorker-1] checkpoints/auxiliary/models/buffalo_l/w600k_r50.onnx
[2025-07-31 11:04:59,911: WARNING/ForkPoolWorker-1]  
[2025-07-31 11:04:59,911: WARNING/ForkPoolWorker-1] recognition
[2025-07-31 11:04:59,940: WARNING/ForkPoolWorker-2] find model:
[2025-07-31 11:04:59,940: WARNING/ForkPoolWorker-2]  
[2025-07-31 11:04:59,940: WARNING/ForkPoolWorker-2] checkpoints/auxiliary/models/buffalo_l/2d106det.onnx
[2025-07-31 11:04:59,940: WARNING/ForkPoolWorker-2]  
[2025-07-31 11:04:59,940: WARNING/ForkPoolWorker-2] landmark_2d_106
[2025-07-31 11:04:59,940: WARNING/ForkPoolWorker-2]  
[2025-07-31 11:04:59,940: WARNING/ForkPoolWorker-2] ['None', 3, 192, 192]
[2025-07-31 11:04:59,940: WARNING/ForkPoolWorker-2]  
[2025-07-31 11:04:59,940: WARNING/ForkPoolWorker-2] 0.0
[2025-07-31 11:04:59,941: WARNING/ForkPoolWorker-2]  
[2025-07-31 11:04:59,941: WARNING/ForkPoolWorker-2] 1.0
[2025-07-31 11:04:59,966: WARNING/ForkPoolWorker-1] set det-size:
[2025-07-31 11:04:59,986: WARNING/ForkPoolWorker-1]  
[2025-07-31 11:04:59,986: WARNING/ForkPoolWorker-1] (512, 512)
[2025-07-31 11:04:59,986: WARNING/ForkPoolWorker-1] [MODEL_MANAGER] 模型已加载到GPU 0
[2025-07-31 11:05:00,217: WARNING/ForkPoolWorker-15] 
 33%|###2      | 83/252 [00:27<01:12,  2.35it/s]
[2025-07-31 11:05:00,217: WARNING/ForkPoolWorker-15] [A
[2025-07-31 11:05:00,304: WARNING/ForkPoolWorker-2] Applied providers: ['CPUExecutionProvider'], with options: {'CPUExecutionProvider': {}}
[2025-07-31 11:05:00,318: WARNING/ForkPoolWorker-2] find model:
[2025-07-31 11:05:00,318: WARNING/ForkPoolWorker-2]  
[2025-07-31 11:05:00,318: WARNING/ForkPoolWorker-2] checkpoints/auxiliary/models/buffalo_l/det_10g.onnx
[2025-07-31 11:05:00,319: WARNING/ForkPoolWorker-2]  
[2025-07-31 11:05:00,319: WARNING/ForkPoolWorker-2] detection
[2025-07-31 11:05:00,319: WARNING/ForkPoolWorker-2]  
[2025-07-31 11:05:00,319: WARNING/ForkPoolWorker-2] [1, 3, '?', '?']
[2025-07-31 11:05:00,319: WARNING/ForkPoolWorker-2]  
[2025-07-31 11:05:00,319: WARNING/ForkPoolWorker-2] 127.5
[2025-07-31 11:05:00,319: WARNING/ForkPoolWorker-2]  
[2025-07-31 11:05:00,319: WARNING/ForkPoolWorker-2] 128.0
[2025-07-31 11:05:00,322: WARNING/ForkPoolWorker-16] 
 17%|#7        | 44/252 [00:18<01:29,  2.32it/s]
[2025-07-31 11:05:00,322: WARNING/ForkPoolWorker-16] [A
[2025-07-31 11:05:00,454: WARNING/ForkPoolWorker-2] Applied providers: ['CPUExecutionProvider'], with options: {'CPUExecutionProvider': {}}
[2025-07-31 11:05:00,457: WARNING/ForkPoolWorker-2] model ignore:
[2025-07-31 11:05:00,462: WARNING/ForkPoolWorker-2]  
[2025-07-31 11:05:00,462: WARNING/ForkPoolWorker-2] checkpoints/auxiliary/models/buffalo_l/genderage.onnx
[2025-07-31 11:05:00,462: WARNING/ForkPoolWorker-2]  
[2025-07-31 11:05:00,462: WARNING/ForkPoolWorker-2] genderage
[2025-07-31 11:05:00,812: WARNING/ForkPoolWorker-16] 
 18%|#7        | 45/252 [00:19<01:32,  2.23it/s]
[2025-07-31 11:05:00,812: WARNING/ForkPoolWorker-16] [A
[2025-07-31 11:05:00,916: WARNING/ForkPoolWorker-15] 
 33%|###3      | 84/252 [00:28<01:25,  1.97it/s]
[2025-07-31 11:05:00,916: WARNING/ForkPoolWorker-15] [A
[2025-07-31 11:05:01,236: WARNING/ForkPoolWorker-16] 
 18%|#8        | 46/252 [00:19<01:30,  2.26it/s]
[2025-07-31 11:05:01,250: WARNING/ForkPoolWorker-16] [A
[2025-07-31 11:05:01,272: WARNING/ForkPoolWorker-15] 
 34%|###3      | 85/252 [00:28<01:17,  2.16it/s]
[2025-07-31 11:05:01,282: WARNING/ForkPoolWorker-15] [A
[2025-07-31 11:05:01,560: WARNING/ForkPoolWorker-15] 
 34%|###4      | 86/252 [00:28<01:08,  2.44it/s]
[2025-07-31 11:05:01,570: WARNING/ForkPoolWorker-15] [A
[2025-07-31 11:05:01,632: WARNING/ForkPoolWorker-16] 
 19%|#8        | 47/252 [00:20<01:27,  2.34it/s]
[2025-07-31 11:05:01,650: WARNING/ForkPoolWorker-16] [A
[2025-07-31 11:05:01,641: WARNING/ForkPoolWorker-2] Applied providers: ['CPUExecutionProvider'], with options: {'CPUExecutionProvider': {}}
[2025-07-31 11:05:01,980: WARNING/ForkPoolWorker-15] 
 35%|###4      | 87/252 [00:29<01:08,  2.42it/s]
[2025-07-31 11:05:01,983: WARNING/ForkPoolWorker-15] [A
[2025-07-31 11:05:02,150: WARNING/ForkPoolWorker-2] model ignore:
[2025-07-31 11:05:02,151: WARNING/ForkPoolWorker-2]  
[2025-07-31 11:05:02,151: WARNING/ForkPoolWorker-2] checkpoints/auxiliary/models/buffalo_l/w600k_r50.onnx
[2025-07-31 11:05:02,151: WARNING/ForkPoolWorker-2]  
[2025-07-31 11:05:02,151: WARNING/ForkPoolWorker-2] recognition
[2025-07-31 11:05:02,184: WARNING/ForkPoolWorker-2] set det-size:
[2025-07-31 11:05:02,185: WARNING/ForkPoolWorker-2]  
[2025-07-31 11:05:02,185: WARNING/ForkPoolWorker-2] (512, 512)
[2025-07-31 11:05:02,185: WARNING/ForkPoolWorker-2] [MODEL_MANAGER] 模型已加载到GPU 0
[2025-07-31 11:05:02,204: WARNING/ForkPoolWorker-16] 
 19%|#9        | 48/252 [00:20<01:36,  2.12it/s]
[2025-07-31 11:05:02,204: WARNING/ForkPoolWorker-16] [A
[2025-07-31 11:05:02,293: WARNING/ForkPoolWorker-15] 
 35%|###4      | 88/252 [00:29<01:02,  2.61it/s]
[2025-07-31 11:05:02,293: WARNING/ForkPoolWorker-15] [A
[2025-07-31 11:05:02,667: WARNING/ForkPoolWorker-15] 
 35%|###5      | 89/252 [00:29<01:01,  2.63it/s]
[2025-07-31 11:05:02,667: WARNING/ForkPoolWorker-15] [A
[2025-07-31 11:05:02,708: WARNING/ForkPoolWorker-16] 
 19%|#9        | 49/252 [00:21<01:37,  2.08it/s]
[2025-07-31 11:05:02,708: WARNING/ForkPoolWorker-16] [A
[2025-07-31 11:05:03,062: WARNING/ForkPoolWorker-15] 
 36%|###5      | 90/252 [00:30<01:02,  2.60it/s]
[2025-07-31 11:05:03,062: WARNING/ForkPoolWorker-15] [A
[2025-07-31 11:05:03,232: WARNING/ForkPoolWorker-16] 
 20%|#9        | 50/252 [00:21<01:39,  2.02it/s]
[2025-07-31 11:05:03,232: WARNING/ForkPoolWorker-16] [A
[2025-07-31 11:05:03,485: WARNING/ForkPoolWorker-15] 
 36%|###6      | 91/252 [00:30<01:03,  2.52it/s]
[2025-07-31 11:05:03,485: WARNING/ForkPoolWorker-15] [A
[2025-07-31 11:05:03,640: WARNING/ForkPoolWorker-16] 
 20%|##        | 51/252 [00:22<01:34,  2.14it/s]
[2025-07-31 11:05:03,640: WARNING/ForkPoolWorker-16] [A
[2025-07-31 11:05:03,871: WARNING/ForkPoolWorker-15] 
 37%|###6      | 92/252 [00:31<01:02,  2.54it/s]
[2025-07-31 11:05:03,871: WARNING/ForkPoolWorker-15] [A
[2025-07-31 11:05:04,003: WARNING/ForkPoolWorker-16] 
 21%|##        | 52/252 [00:22<01:27,  2.29it/s]
[2025-07-31 11:05:04,003: WARNING/ForkPoolWorker-16] [A
[2025-07-31 11:05:04,144: WARNING/ForkPoolWorker-15] 
 37%|###6      | 93/252 [00:31<00:56,  2.80it/s]
[2025-07-31 11:05:04,144: WARNING/ForkPoolWorker-15] [A
[2025-07-31 11:05:04,366: WARNING/ForkPoolWorker-16] 
 21%|##1       | 53/252 [00:22<01:22,  2.41it/s]
[2025-07-31 11:05:04,388: WARNING/ForkPoolWorker-16] [A
[2025-07-31 11:05:04,419: WARNING/ForkPoolWorker-1] Load checkpoint from: ./checkpoints/192jm.pth
[2025-07-31 11:05:04,892: WARNING/ForkPoolWorker-15] 
 37%|###7      | 94/252 [00:32<01:14,  2.11it/s]
[2025-07-31 11:05:04,902: WARNING/ForkPoolWorker-15] [A
[2025-07-31 11:05:04,968: WARNING/ForkPoolWorker-16] 
 21%|##1       | 54/252 [00:23<01:33,  2.12it/s]
[2025-07-31 11:05:04,969: WARNING/ForkPoolWorker-16] [A
[2025-07-31 11:05:05,427: WARNING/ForkPoolWorker-15] 
 38%|###7      | 95/252 [00:32<01:15,  2.09it/s]
[2025-07-31 11:05:05,428: WARNING/ForkPoolWorker-15] [A
[2025-07-31 11:05:05,662: WARNING/ForkPoolWorker-16] 
 22%|##1       | 55/252 [00:24<01:45,  1.86it/s]
[2025-07-31 11:05:05,663: WARNING/ForkPoolWorker-16] [A
[2025-07-31 11:05:05,865: WARNING/ForkPoolWorker-15] 
 38%|###8      | 96/252 [00:33<01:14,  2.08it/s]
[2025-07-31 11:05:05,894: WARNING/ForkPoolWorker-15] [A
[2025-07-31 11:05:06,206: WARNING/ForkPoolWorker-16] 
 22%|##2       | 56/252 [00:24<01:45,  1.85it/s]
[2025-07-31 11:05:06,226: WARNING/ForkPoolWorker-16] [A
[2025-07-31 11:05:06,413: WARNING/ForkPoolWorker-15] 
 38%|###8      | 97/252 [00:33<01:17,  2.00it/s]
[2025-07-31 11:05:06,456: WARNING/ForkPoolWorker-15] [A
[2025-07-31 11:05:06,944: WARNING/ForkPoolWorker-15] 
 39%|###8      | 98/252 [00:34<01:18,  1.96it/s]
[2025-07-31 11:05:06,944: WARNING/ForkPoolWorker-15] [A
[2025-07-31 11:05:06,990: WARNING/ForkPoolWorker-16] 
 23%|##2       | 57/252 [00:25<01:59,  1.63it/s]
[2025-07-31 11:05:06,992: WARNING/ForkPoolWorker-16] [A
[2025-07-31 11:05:07,371: WARNING/ForkPoolWorker-2] Load checkpoint from: ./checkpoints/192jm.pth
[2025-07-31 11:05:07,384: WARNING/ForkPoolWorker-15] 
 39%|###9      | 99/252 [00:34<01:14,  2.05it/s]
[2025-07-31 11:05:07,384: WARNING/ForkPoolWorker-15] [A
[2025-07-31 11:05:07,480: WARNING/ForkPoolWorker-16] 
 23%|##3       | 58/252 [00:25<01:51,  1.74it/s]
[2025-07-31 11:05:07,480: WARNING/ForkPoolWorker-16] [A
[2025-07-31 11:05:07,682: WARNING/ForkPoolWorker-16] 
 23%|##3       | 59/252 [00:26<01:29,  2.16it/s]
[2025-07-31 11:05:07,683: WARNING/ForkPoolWorker-16] [A
[2025-07-31 11:05:07,836: WARNING/ForkPoolWorker-15] 
 40%|###9      | 100/252 [00:34<01:12,  2.09it/s]
[2025-07-31 11:05:07,846: WARNING/ForkPoolWorker-15] [A
[2025-07-31 11:05:07,997: WARNING/ForkPoolWorker-16] 
 24%|##3       | 60/252 [00:26<01:20,  2.38it/s]
[2025-07-31 11:05:07,998: WARNING/ForkPoolWorker-16] [A
[2025-07-31 11:05:08,276: WARNING/ForkPoolWorker-15] 
 40%|####      | 101/252 [00:35<01:10,  2.14it/s]
[2025-07-31 11:05:08,277: WARNING/ForkPoolWorker-15] [A
[2025-07-31 11:05:08,436: WARNING/ForkPoolWorker-16] 
 24%|##4       | 61/252 [00:26<01:21,  2.35it/s]
[2025-07-31 11:05:08,437: WARNING/ForkPoolWorker-16] [A
[2025-07-31 11:05:08,580: WARNING/ForkPoolWorker-15] 
 40%|####      | 102/252 [00:35<01:02,  2.39it/s]
[2025-07-31 11:05:08,581: WARNING/ForkPoolWorker-15] [A
[2025-07-31 11:05:08,954: WARNING/ForkPoolWorker-16] 
 25%|##4       | 62/252 [00:27<01:26,  2.21it/s]
[2025-07-31 11:05:08,955: WARNING/ForkPoolWorker-16] [A
[2025-07-31 11:05:08,974: WARNING/ForkPoolWorker-15] 
 41%|####      | 103/252 [00:36<01:01,  2.44it/s]
[2025-07-31 11:05:08,978: WARNING/ForkPoolWorker-15] [A
[2025-07-31 11:05:09,181: WARNING/ForkPoolWorker-15] 
 41%|####1     | 104/252 [00:36<00:51,  2.86it/s]
[2025-07-31 11:05:09,276: WARNING/ForkPoolWorker-15] [A
[2025-07-31 11:05:09,280: WARNING/ForkPoolWorker-16] 
 25%|##5       | 63/252 [00:27<01:18,  2.41it/s]
[2025-07-31 11:05:09,280: WARNING/ForkPoolWorker-16] [A
[2025-07-31 11:05:09,704: WARNING/ForkPoolWorker-15] 
 42%|####1     | 105/252 [00:36<00:59,  2.49it/s]
[2025-07-31 11:05:09,704: WARNING/ForkPoolWorker-15] [A
[2025-07-31 11:05:09,711: WARNING/ForkPoolWorker-16] 
 25%|##5       | 64/252 [00:28<01:18,  2.38it/s]
[2025-07-31 11:05:09,711: WARNING/ForkPoolWorker-16] [A
[2025-07-31 11:05:10,034: WARNING/ForkPoolWorker-16] 
 26%|##5       | 65/252 [00:28<01:13,  2.56it/s]
[2025-07-31 11:05:10,035: WARNING/ForkPoolWorker-16] [A
[2025-07-31 11:05:10,224: WARNING/ForkPoolWorker-15] 
 42%|####2     | 106/252 [00:37<01:03,  2.29it/s]
[2025-07-31 11:05:10,224: WARNING/ForkPoolWorker-15] [A
[2025-07-31 11:05:10,464: WARNING/ForkPoolWorker-16] 
 26%|##6       | 66/252 [00:28<01:14,  2.48it/s]
[2025-07-31 11:05:10,464: WARNING/ForkPoolWorker-16] [A
[2025-07-31 11:05:10,664: WARNING/ForkPoolWorker-15] 
 42%|####2     | 107/252 [00:37<01:03,  2.28it/s]
[2025-07-31 11:05:10,682: WARNING/ForkPoolWorker-15] [A
[2025-07-31 11:05:10,908: WARNING/ForkPoolWorker-16] 
 27%|##6       | 67/252 [00:29<01:16,  2.41it/s]
[2025-07-31 11:05:10,908: WARNING/ForkPoolWorker-16] [A
[2025-07-31 11:05:10,987: WARNING/ForkPoolWorker-15] 
 43%|####2     | 108/252 [00:38<00:58,  2.48it/s]
[2025-07-31 11:05:10,988: WARNING/ForkPoolWorker-15] [A
ffmpeg version 7.0.2-static https://johnvansickle.com/ffmpeg/  Copyright (c) 2000-2024 the FFmpeg developers
  built with gcc 8 (Debian 8.3.0-6)
  configuration: --enable-gpl --enable-version3 --enable-static --disable-debug --disable-ffplay --disable-indev=sndio --disable-outdev=sndio --cc=gcc --enable-fontconfig --enable-frei0r --enable-gnutls --enable-gmp --enable-libgme --enable-gray --enable-libaom --enable-libfribidi --enable-libass --enable-libvmaf --enable-libfreetype --enable-libmp3lame --enable-libopencore-amrnb --enable-libopencore-amrwb --enable-libopenjpeg --enable-librubberband --enable-libsoxr --enable-libspeex --enable-libsrt --enable-libvorbis --enable-libopus --enable-libtheora --enable-libvidstab --enable-libvo-amrwbenc --enable-libvpx --enable-libwebp --enable-libx264 --enable-libx265 --enable-libxml2 --enable-libdav1d --enable-libxvid --enable-libzvbi --enable-libzimg
  libavutil      59.  8.100 / 59.  8.100
  libavcodec     61.  3.100 / 61.  3.100
  libavformat    61.  1.100 / 61.  1.100
  libavdevice    61.  1.100 / 61.  1.100
  libavfilter    10.  1.100 / 10.  1.100
  libswscale      8.  1.100 /  8.  1.100
  libswresample   5.  1.100 /  5.  1.100
  libpostproc    58.  1.100 / 58.  1.100
Input #0, mov,mp4,m4a,3gp,3g2,mj2, from '/tmp/lstmsync_gpu0_d4eghldb/input.mp4':
  Metadata:
    major_brand     : isom
    minor_version   : 512
    compatible_brands: isomiso2avc1mp41
    encoder         : Lavf58.20.100
  Duration: 00:00:10.01, start: 0.000000, bitrate: 1581 kb/s
  Stream #0:0[0x1](eng): Video: h264 (Main) (avc1 / 0x31637661), yuv420p(progressive), 1280x1280, 1386 kb/s, SAR 1:1 DAR 1:1, 30 fps, 30 tbr, 15360 tbn (default)
      Metadata:
        handler_name    : ?Mainconcept Video Media Handler
        vendor_id       : [0][0][0][0]
  Stream #0:1[0x2](eng): Audio: aac (LC) (mp4a / 0x6134706D), 48000 Hz, stereo, fltp, 187 kb/s (default)
      Metadata:
        handler_name    : #Mainconcept MP4 Sound Media Handler
        vendor_id       : [0][0][0][0]
[2025-07-31 11:05:11,399: WARNING/ForkPoolWorker-15] 
 43%|####3     | 109/252 [00:38<00:58,  2.46it/s]
[2025-07-31 11:05:11,399: WARNING/ForkPoolWorker-15] [A
Stream mapping:
  Stream #0:0 -> #0:0 (h264 (native) -> h264 (libx264))
  Stream #0:1 -> #0:1 (aac (native) -> aac (native))
Press [q] to stop, [?] for help
[2025-07-31 11:05:11,502: WARNING/ForkPoolWorker-16] 
 27%|##6       | 68/252 [00:29<01:26,  2.13it/s]
[2025-07-31 11:05:11,502: WARNING/ForkPoolWorker-16] [A
[2025-07-31 11:05:12,003: WARNING/ForkPoolWorker-16] 
 27%|##7       | 69/252 [00:30<01:27,  2.09it/s]
[2025-07-31 11:05:12,004: WARNING/ForkPoolWorker-16] [A
[2025-07-31 11:05:12,007: WARNING/ForkPoolWorker-15] 
 44%|####3     | 110/252 [00:39<01:06,  2.14it/s]
[2025-07-31 11:05:12,007: WARNING/ForkPoolWorker-15] [A
[libx264 @ 0x923cec0] using SAR=1/1
[libx264 @ 0x923cec0] using cpu capabilities: MMX2 SSE2Fast SSSE3 SSE4.2 AVX FMA3 BMI2 AVX2 AVX512
[libx264 @ 0x923cec0] profile High, level 4.0, 4:2:0, 8-bit
[libx264 @ 0x923cec0] 264 - core 164 r3191 4613ac3 - H.264/MPEG-4 AVC codec - Copyleft 2003-2024 - http://www.videolan.org/x264.html - options: cabac=1 ref=3 deblock=1:0:0 analyse=0x3:0x113 me=hex subme=7 psy=1 psy_rd=1.00:0.00 mixed_ref=1 me_range=16 chroma_me=1 trellis=1 8x8dct=1 cqm=0 deadzone=21,11 fast_pskip=1 chroma_qp_offset=-2 threads=40 lookahead_threads=6 sliced_threads=0 nr=0 decimate=1 interlaced=0 bluray_compat=0 constrained_intra=0 bframes=3 b_pyramid=2 b_adapt=1 b_bias=0 direct=1 weightb=1 open_gop=0 weightp=2 keyint=250 keyint_min=25 scenecut=40 intra_refresh=0 rc_lookahead=40 rc=crf mbtree=1 crf=23.0 qcomp=0.60 qpmin=0 qpmax=69 qpstep=4 ip_ratio=1.40 aq=1:1.00
Output #0, mp4, to '/tmp/lstmsync_gpu0_d4eghldb/fps25_temp.mp4':
  Metadata:
    major_brand     : isom
    minor_version   : 512
    compatible_brands: isomiso2avc1mp41
    encoder         : Lavf61.1.100
  Stream #0:0(eng): Video: h264 (avc1 / 0x31637661), yuv420p(progressive), 1280x1280 [SAR 1:1 DAR 1:1], q=2-31, 25 fps, 12800 tbn (default)
      Metadata:
        handler_name    : ?Mainconcept Video Media Handler
        vendor_id       : [0][0][0][0]
        encoder         : Lavc61.3.100 libx264
      Side data:
        cpb: bitrate max/min/avg: 0/0/0 buffer size: 0 vbv_delay: N/A
  Stream #0:1(eng): Audio: aac (LC) (mp4a / 0x6134706D), 48000 Hz, stereo, fltp, 128 kb/s (default)
      Metadata:
        handler_name    : #Mainconcept MP4 Sound Media Handler
        vendor_id       : [0][0][0][0]
        encoder         : Lavc61.3.100 aac
[2025-07-31 11:05:12,352: WARNING/ForkPoolWorker-15] 
 44%|####4     | 111/252 [00:39<01:00,  2.33it/s]
[2025-07-31 11:05:12,352: WARNING/ForkPoolWorker-15] [A
[2025-07-31 11:05:12,398: WARNING/ForkPoolWorker-16] 
 28%|##7       | 70/252 [00:30<01:22,  2.21it/s]
[2025-07-31 11:05:12,399: WARNING/ForkPoolWorker-16] [A
frame=    0 fps=0.0 q=0.0 size=       0KiB time=N/A bitrate=N/A dup=0 drop=6 speed=N/A    
[2025-07-31 11:05:12,726: WARNING/ForkPoolWorker-15] 
 44%|####4     | 112/252 [00:39<00:57,  2.42it/s]
[2025-07-31 11:05:12,734: WARNING/ForkPoolWorker-15] [A
[2025-07-31 11:05:12,895: WARNING/ForkPoolWorker-16] 
 28%|##8       | 71/252 [00:31<01:24,  2.14it/s]
[2025-07-31 11:05:12,895: WARNING/ForkPoolWorker-16] [A
frame=    0 fps=0.0 q=0.0 size=       0KiB time=N/A bitrate=N/A dup=0 drop=9 speed=N/A    
[2025-07-31 11:05:13,031: WARNING/ForkPoolWorker-15] 
 45%|####4     | 113/252 [00:40<00:52,  2.63it/s]
[2025-07-31 11:05:13,031: WARNING/ForkPoolWorker-15] [A
[2025-07-31 11:05:13,188: WARNING/ForkPoolWorker-16] 
 29%|##8       | 72/252 [00:31<01:14,  2.41it/s]
[2025-07-31 11:05:13,198: WARNING/ForkPoolWorker-16] [A
[2025-07-31 11:05:13,396: WARNING/ForkPoolWorker-15] 
 45%|####5     | 114/252 [00:40<00:51,  2.66it/s]
[2025-07-31 11:05:13,402: WARNING/ForkPoolWorker-15] [A
frame=    0 fps=0.0 q=0.0 size=       0KiB time=N/A bitrate=N/A dup=0 drop=13 speed=N/A    
[2025-07-31 11:05:13,520: WARNING/ForkPoolWorker-16] 
 29%|##8       | 73/252 [00:31<01:09,  2.57it/s]
[2025-07-31 11:05:13,526: WARNING/ForkPoolWorker-16] [A
[2025-07-31 11:05:13,698: WARNING/ForkPoolWorker-15] 
 46%|####5     | 115/252 [00:40<00:48,  2.83it/s]
[2025-07-31 11:05:13,699: WARNING/ForkPoolWorker-15] [A
frame=    2 fps=0.8 q=28.0 size=       0KiB time=00:00:00.00 bitrate=N/A dup=0 drop=17 speed=   0x    
[2025-07-31 11:05:14,029: WARNING/ForkPoolWorker-16] 
 29%|##9       | 74/252 [00:32<01:15,  2.35it/s]
[2025-07-31 11:05:14,029: WARNING/ForkPoolWorker-16] [A
ffmpeg version 7.0.2-static https://johnvansickle.com/ffmpeg/  Copyright (c) 2000-2024 the FFmpeg developers
  built with gcc 8 (Debian 8.3.0-6)
  configuration: --enable-gpl --enable-version3 --enable-static --disable-debug --disable-ffplay --disable-indev=sndio --disable-outdev=sndio --cc=gcc --enable-fontconfig --enable-frei0r --enable-gnutls --enable-gmp --enable-libgme --enable-gray --enable-libaom --enable-libfribidi --enable-libass --enable-libvmaf --enable-libfreetype --enable-libmp3lame --enable-libopencore-amrnb --enable-libopencore-amrwb --enable-libopenjpeg --enable-librubberband --enable-libsoxr --enable-libspeex --enable-libsrt --enable-libvorbis --enable-libopus --enable-libtheora --enable-libvidstab --enable-libvo-amrwbenc --enable-libvpx --enable-libwebp --enable-libx264 --enable-libx265 --enable-libxml2 --enable-libdav1d --enable-libxvid --enable-libzvbi --enable-libzimg
  libavutil      59.  8.100 / 59.  8.100
  libavcodec     61.  3.100 / 61.  3.100
  libavformat    61.  1.100 / 61.  1.100
  libavdevice    61.  1.100 / 61.  1.100
  libavfilter    10.  1.100 / 10.  1.100
  libswscale      8.  1.100 /  8.  1.100
  libswresample   5.  1.100 /  5.  1.100
  libpostproc    58.  1.100 / 58.  1.100
Input #0, mov,mp4,m4a,3gp,3g2,mj2, from '/tmp/lstmsync_gpu0_hct5x_rq/input.mp4':
  Metadata:
    major_brand     : isom
    minor_version   : 512
    compatible_brands: isomiso2avc1mp41
    encoder         : Lavf58.20.100
  Duration: 00:00:10.01, start: 0.000000, bitrate: 1581 kb/s
  Stream #0:0[0x1](eng): Video: h264 (Main) (avc1 / 0x31637661), yuv420p(progressive), 1280x1280, 1386 kb/s, SAR 1:1 DAR 1:1, 30 fps, 30 tbr, 15360 tbn (default)
      Metadata:
        handler_name    : ?Mainconcept Video Media Handler
        vendor_id       : [0][0][0][0]
  Stream #0:1[0x2](eng): Audio: aac (LC) (mp4a / 0x6134706D), 48000 Hz, stereo, fltp, 187 kb/s (default)
      Metadata:
        handler_name    : #Mainconcept MP4 Sound Media Handler
        vendor_id       : [0][0][0][0]
[2025-07-31 11:05:14,241: WARNING/ForkPoolWorker-15] 
 46%|####6     | 116/252 [00:41<00:55,  2.44it/s]
[2025-07-31 11:05:14,246: WARNING/ForkPoolWorker-15] [A
Stream mapping:
  Stream #0:0 -> #0:0 (h264 (native) -> h264 (libx264))
  Stream #0:1 -> #0:1 (aac (native) -> aac (native))
Press [q] to stop, [?] for help
frame=   37 fps= 12 q=28.0 size=       0KiB time=00:00:01.40 bitrate=   0.3kbits/s dup=0 drop=24 speed=0.467x    
[2025-07-31 11:05:14,572: WARNING/ForkPoolWorker-16] 
 30%|##9       | 75/252 [00:32<01:21,  2.17it/s]
[2025-07-31 11:05:14,590: WARNING/ForkPoolWorker-16] [A
[libx264 @ 0x2f98fec0] using SAR=1/1
[libx264 @ 0x2f98fec0] using cpu capabilities: MMX2 SSE2Fast SSSE3 SSE4.2 AVX FMA3 BMI2 AVX2 AVX512
[2025-07-31 11:05:14,837: WARNING/ForkPoolWorker-15] 
 46%|####6     | 117/252 [00:41<01:02,  2.15it/s]
[2025-07-31 11:05:14,837: WARNING/ForkPoolWorker-15] [A
[libx264 @ 0x2f98fec0] profile High, level 4.0, 4:2:0, 8-bit
[libx264 @ 0x2f98fec0] 264 - core 164 r3191 4613ac3 - H.264/MPEG-4 AVC codec - Copyleft 2003-2024 - http://www.videolan.org/x264.html - options: cabac=1 ref=3 deblock=1:0:0 analyse=0x3:0x113 me=hex subme=7 psy=1 psy_rd=1.00:0.00 mixed_ref=1 me_range=16 chroma_me=1 trellis=1 8x8dct=1 cqm=0 deadzone=21,11 fast_pskip=1 chroma_qp_offset=-2 threads=40 lookahead_threads=6 sliced_threads=0 nr=0 decimate=1 interlaced=0 bluray_compat=0 constrained_intra=0 bframes=3 b_pyramid=2 b_adapt=1 b_bias=0 direct=1 weightb=1 open_gop=0 weightp=2 keyint=250 keyint_min=25 scenecut=40 intra_refresh=0 rc_lookahead=40 rc=crf mbtree=1 crf=23.0 qcomp=0.60 qpmin=0 qpmax=69 qpstep=4 ip_ratio=1.40 aq=1:1.00
Output #0, mp4, to '/tmp/lstmsync_gpu0_hct5x_rq/fps25_temp.mp4':
  Metadata:
    major_brand     : isom
    minor_version   : 512
    compatible_brands: isomiso2avc1mp41
    encoder         : Lavf61.1.100
  Stream #0:0(eng): Video: h264 (avc1 / 0x31637661), yuv420p(progressive), 1280x1280 [SAR 1:1 DAR 1:1], q=2-31, 25 fps, 12800 tbn (default)
      Metadata:
        handler_name    : ?Mainconcept Video Media Handler
        vendor_id       : [0][0][0][0]
        encoder         : Lavc61.3.100 libx264
      Side data:
        cpb: bitrate max/min/avg: 0/0/0 buffer size: 0 vbv_delay: N/A
  Stream #0:1(eng): Audio: aac (LC) (mp4a / 0x6134706D), 48000 Hz, stereo, fltp, 128 kb/s (default)
      Metadata:
        handler_name    : #Mainconcept MP4 Sound Media Handler
        vendor_id       : [0][0][0][0]
        encoder         : Lavc61.3.100 aac
frame=   56 fps= 16 q=28.0 size=     256KiB time=00:00:02.16 bitrate= 971.1kbits/s dup=0 drop=28 speed=0.617x    
[2025-07-31 11:05:14,919: WARNING/ForkPoolWorker-16] 
 30%|###       | 76/252 [00:33<01:15,  2.34it/s]
[2025-07-31 11:05:14,919: WARNING/ForkPoolWorker-16] [A
frame=    0 fps=0.0 q=0.0 size=       0KiB time=N/A bitrate=N/A dup=0 drop=6 speed=N/A    
[2025-07-31 11:05:15,325: WARNING/ForkPoolWorker-15] 
 47%|####6     | 118/252 [00:42<01:03,  2.12it/s]
[2025-07-31 11:05:15,325: WARNING/ForkPoolWorker-15] [A
frame=   71 fps= 18 q=28.0 size=     256KiB time=00:00:02.76 bitrate= 760.0kbits/s dup=0 drop=31 speed=0.69x    
[2025-07-31 11:05:15,578: WARNING/ForkPoolWorker-16] 
 31%|###       | 77/252 [00:33<01:26,  2.02it/s]
[2025-07-31 11:05:15,586: WARNING/ForkPoolWorker-16] [A
frame=    0 fps=0.0 q=0.0 size=       0KiB time=N/A bitrate=N/A dup=0 drop=9 speed=N/A    
[2025-07-31 11:05:15,893: WARNING/ForkPoolWorker-15] 
 47%|####7     | 119/252 [00:43<01:06,  1.99it/s]
[2025-07-31 11:05:15,893: WARNING/ForkPoolWorker-15] [A
frame=   92 fps= 20 q=28.0 size=     512KiB time=00:00:03.60 bitrate=1165.2kbits/s dup=0 drop=35 speed= 0.8x    
[2025-07-31 11:05:16,020: WARNING/ForkPoolWorker-16] 
 31%|###       | 78/252 [00:34<01:23,  2.08it/s]
[2025-07-31 11:05:16,020: WARNING/ForkPoolWorker-16] [A
frame=    0 fps=0.0 q=0.0 size=       0KiB time=N/A bitrate=N/A dup=0 drop=12 speed=N/A    
[2025-07-31 11:05:16,309: WARNING/ForkPoolWorker-15] 
 48%|####7     | 120/252 [00:43<01:02,  2.10it/s]
[2025-07-31 11:05:16,309: WARNING/ForkPoolWorker-15] [A
[2025-07-31 11:05:16,412: WARNING/ForkPoolWorker-16] 
 31%|###1      | 79/252 [00:34<01:18,  2.20it/s]
[2025-07-31 11:05:16,412: WARNING/ForkPoolWorker-16] [A
frame=  113 fps= 23 q=28.0 size=     512KiB time=00:00:04.44 bitrate= 944.7kbits/s dup=0 drop=39 speed=0.888x    
[2025-07-31 11:05:16,675: WARNING/ForkPoolWorker-15] 
 48%|####8     | 121/252 [00:43<00:58,  2.26it/s]
[2025-07-31 11:05:16,675: WARNING/ForkPoolWorker-15] [A
frame=    0 fps=0.0 q=0.0 size=       0KiB time=N/A bitrate=N/A dup=0 drop=13 speed=N/A    
[2025-07-31 11:05:16,872: WARNING/ForkPoolWorker-16] 
 32%|###1      | 80/252 [00:35<01:18,  2.19it/s]
[2025-07-31 11:05:16,873: WARNING/ForkPoolWorker-16] [A
frame=  140 fps= 25 q=28.0 size=     768KiB time=00:00:05.52 bitrate=1139.8kbits/s dup=0 drop=45 speed=   1x    
[2025-07-31 11:05:17,157: WARNING/ForkPoolWorker-15] 
 48%|####8     | 122/252 [00:44<00:59,  2.20it/s]
[2025-07-31 11:05:17,157: WARNING/ForkPoolWorker-15] [A
frame=    0 fps=0.0 q=0.0 size=       0KiB time=N/A bitrate=N/A dup=0 drop=17 speed=N/A    
[2025-07-31 11:05:17,273: WARNING/ForkPoolWorker-16] 
 32%|###2      | 81/252 [00:35<01:15,  2.28it/s]
[2025-07-31 11:05:17,273: WARNING/ForkPoolWorker-16] [A
frame=  153 fps= 25 q=28.0 size=     768KiB time=00:00:06.04 bitrate=1041.7kbits/s dup=0 drop=47 speed=1.01x    
[2025-07-31 11:05:17,605: WARNING/ForkPoolWorker-15] 
 49%|####8     | 123/252 [00:44<00:58,  2.21it/s]
[2025-07-31 11:05:17,605: WARNING/ForkPoolWorker-15] [A
frame=   27 fps=7.7 q=28.0 size=       0KiB time=00:00:01.00 bitrate=   0.4kbits/s dup=0 drop=22 speed=0.286x    
[2025-07-31 11:05:17,843: WARNING/ForkPoolWorker-16] 
 33%|###2      | 82/252 [00:36<01:21,  2.09it/s]
[2025-07-31 11:05:17,843: WARNING/ForkPoolWorker-16] [A
frame=  180 fps= 28 q=28.0 size=     768KiB time=N/A bitrate=N/A dup=0 drop=48 speed=N/A    
frame=   57 fps= 14 q=28.0 size=     256KiB time=00:00:02.20 bitrate= 953.4kbits/s dup=0 drop=28 speed=0.55x    
[2025-07-31 11:05:18,263: WARNING/ForkPoolWorker-15] 
 49%|####9     | 124/252 [00:45<01:05,  1.94it/s]
[2025-07-31 11:05:18,263: WARNING/ForkPoolWorker-15] [A
[2025-07-31 11:05:18,385: WARNING/ForkPoolWorker-16] 
 33%|###2      | 83/252 [00:36<01:24,  2.01it/s]
[2025-07-31 11:05:18,391: WARNING/ForkPoolWorker-16] [A
frame=  221 fps= 32 q=-1.0 size=    1024KiB time=N/A bitrate=N/A dup=0 drop=48 speed=N/A    
[2025-07-31 11:05:18,726: WARNING/ForkPoolWorker-15] 
 50%|####9     | 125/252 [00:45<01:03,  2.00it/s]
[2025-07-31 11:05:18,727: WARNING/ForkPoolWorker-15] [A
frame=   66 fps= 15 q=28.0 size=     256KiB time=00:00:02.56 bitrate= 819.4kbits/s dup=0 drop=30 speed=0.569x    
[2025-07-31 11:05:18,865: WARNING/ForkPoolWorker-16] 
 33%|###3      | 84/252 [00:37<01:22,  2.03it/s]
[2025-07-31 11:05:18,874: WARNING/ForkPoolWorker-16] [A
frame=  225 fps= 30 q=-1.0 size=    1024KiB time=N/A bitrate=N/A dup=0 drop=48 speed=N/A    
[2025-07-31 11:05:19,136: WARNING/ForkPoolWorker-15] 
 50%|#####     | 126/252 [00:46<00:59,  2.12it/s]
[2025-07-31 11:05:19,136: WARNING/ForkPoolWorker-15] [A
[out#0/mp4 @ 0x91e1d40] video:1153KiB audio:156KiB subtitle:0KiB other streams:0KiB global headers:0KiB muxing overhead: 0.675260%
frame=  252 fps= 32 q=-1.0 Lsize=    1318KiB time=00:00:10.00 bitrate=1079.8kbits/s dup=0 drop=48 speed=1.28x    
[libx264 @ 0x923cec0] frame I:2     Avg QP:16.64  size: 23511
[libx264 @ 0x923cec0] frame P:79    Avg QP:17.47  size:  9984
[libx264 @ 0x923cec0] frame B:171   Avg QP:20.85  size:  2012
[libx264 @ 0x923cec0] consecutive B-frames:  7.1%  4.8%  7.1% 81.0%
[libx264 @ 0x923cec0] mb I  I16..4: 33.0% 60.8%  6.2%
[libx264 @ 0x923cec0] mb P  I16..4:  3.2%  4.3%  0.3%  P16..4: 22.3%  4.6%  2.0%  0.0%  0.0%    skip:63.2%
[libx264 @ 0x923cec0] mb B  I16..4:  0.1%  0.1%  0.0%  B16..8: 18.3%  0.5%  0.0%  direct: 1.8%  skip:79.3%  L0:50.4% L1:48.4% BI: 1.2%
[libx264 @ 0x923cec0] 8x8 transform intra:56.1% inter:74.3%
[libx264 @ 0x923cec0] coded y,uvDC,uvAC intra: 16.1% 37.9% 5.4% inter: 2.5% 7.7% 0.0%
[libx264 @ 0x923cec0] i16 v,h,dc,p: 33% 35% 12% 20%
[libx264 @ 0x923cec0] i8 v,h,dc,ddl,ddr,vr,hd,vl,hu: 28% 19% 45%  1%  1%  1%  2%  1%  2%
[libx264 @ 0x923cec0] i4 v,h,dc,ddl,ddr,vr,hd,vl,hu: 36% 23% 26%  2%  3%  3%  3%  2%  1%
[libx264 @ 0x923cec0] i8c dc,h,v,p: 56% 21% 20%  2%
[libx264 @ 0x923cec0] Weighted P-Frames: Y:0.0% UV:0.0%
[libx264 @ 0x923cec0] ref P L0: 74.2%  6.1% 15.3%  4.5%
[libx264 @ 0x923cec0] ref B L0: 89.7%  9.0%  1.3%
[libx264 @ 0x923cec0] ref B L1: 97.0%  3.0%
[libx264 @ 0x923cec0] kb/s:936.37
[2025-07-31 11:05:19,214: WARNING/ForkPoolWorker-16] 
 34%|###3      | 85/252 [00:37<01:15,  2.23it/s]
[2025-07-31 11:05:19,214: WARNING/ForkPoolWorker-16] [A
[aac @ 0xa155d00] Qavg: 2474.159
frame=   79 fps= 16 q=28.0 size=     256KiB time=00:00:03.08 bitrate= 681.0kbits/s dup=0 drop=33 speed=0.616x    
[2025-07-31 11:05:19,353: WARNING/ForkPoolWorker-1] Reading video frames...
[2025-07-31 11:05:19,573: WARNING/ForkPoolWorker-15] 
 50%|#####     | 127/252 [00:46<00:57,  2.17it/s]
[2025-07-31 11:05:19,573: WARNING/ForkPoolWorker-15] [A
[2025-07-31 11:05:19,655: WARNING/ForkPoolWorker-16] 
 34%|###4      | 86/252 [00:38<01:14,  2.24it/s]
[2025-07-31 11:05:19,656: WARNING/ForkPoolWorker-16] [A
frame=   91 fps= 17 q=28.0 size=     512KiB time=00:00:03.56 bitrate=1178.3kbits/s dup=0 drop=34 speed=0.647x    
[2025-07-31 11:05:20,021: WARNING/ForkPoolWorker-16] 
 35%|###4      | 87/252 [00:38<01:09,  2.37it/s]
[2025-07-31 11:05:20,021: WARNING/ForkPoolWorker-16] [A
[2025-07-31 11:05:20,080: WARNING/ForkPoolWorker-15] 
 51%|#####     | 128/252 [00:47<00:58,  2.10it/s]
[2025-07-31 11:05:20,086: WARNING/ForkPoolWorker-15] [A
frame=  109 fps= 18 q=28.0 size=     512KiB time=00:00:04.28 bitrate= 980.1kbits/s dup=0 drop=39 speed=0.713x    
[2025-07-31 11:05:20,464: WARNING/ForkPoolWorker-16] 
 35%|###4      | 88/252 [00:38<01:10,  2.33it/s]
[2025-07-31 11:05:20,464: WARNING/ForkPoolWorker-16] [A
frame=  137 fps= 21 q=28.0 size=     768KiB time=00:00:05.40 bitrate=1165.2kbits/s dup=0 drop=44 speed=0.83x    
[2025-07-31 11:05:20,837: WARNING/ForkPoolWorker-16] 
 35%|###5      | 89/252 [00:39<01:07,  2.43it/s]
[2025-07-31 11:05:20,838: WARNING/ForkPoolWorker-16] [A
[2025-07-31 11:05:20,838: WARNING/ForkPoolWorker-15] 
 51%|#####1    | 129/252 [00:47<01:08,  1.79it/s]
[2025-07-31 11:05:20,838: WARNING/ForkPoolWorker-15] [A
[2025-07-31 11:05:21,185: WARNING/ForkPoolWorker-16] 
 36%|###5      | 90/252 [00:39<01:03,  2.55it/s]
[2025-07-31 11:05:21,203: WARNING/ForkPoolWorker-16] [A
frame=  164 fps= 23 q=28.0 size=     768KiB time=N/A bitrate=N/A dup=0 drop=48 speed=N/A    
[2025-07-31 11:05:21,416: WARNING/ForkPoolWorker-15] 
 52%|#####1    | 130/252 [00:48<01:08,  1.77it/s]
[2025-07-31 11:05:21,416: WARNING/ForkPoolWorker-15] [A
[2025-07-31 11:05:21,684: WARNING/ForkPoolWorker-16] 
 36%|###6      | 91/252 [00:40<01:08,  2.35it/s]
[2025-07-31 11:05:21,685: WARNING/ForkPoolWorker-16] [A
frame=  192 fps= 26 q=28.0 size=    1024KiB time=N/A bitrate=N/A dup=0 drop=48 speed=N/A    
[2025-07-31 11:05:21,842: WARNING/ForkPoolWorker-15] 
 52%|#####1    | 131/252 [00:49<01:03,  1.91it/s]
[2025-07-31 11:05:21,842: WARNING/ForkPoolWorker-15] [A
[2025-07-31 11:05:22,204: WARNING/ForkPoolWorker-15] 
 52%|#####2    | 132/252 [00:49<00:57,  2.11it/s]
[2025-07-31 11:05:22,204: WARNING/ForkPoolWorker-15] [A
frame=  225 fps= 28 q=-1.0 size=    1024KiB time=N/A bitrate=N/A dup=0 drop=48 speed=N/A    
[2025-07-31 11:05:22,333: WARNING/ForkPoolWorker-16] 
 37%|###6      | 92/252 [00:40<01:18,  2.03it/s]
[2025-07-31 11:05:22,333: WARNING/ForkPoolWorker-16] [A
[2025-07-31 11:05:22,610: WARNING/ForkPoolWorker-15] 
 53%|#####2    | 133/252 [00:49<00:54,  2.20it/s]
[2025-07-31 11:05:22,622: WARNING/ForkPoolWorker-15] [A
frame=  225 fps= 26 q=-1.0 size=    1024KiB time=N/A bitrate=N/A dup=0 drop=48 speed=N/A    
[2025-07-31 11:05:22,976: WARNING/ForkPoolWorker-16] 
 37%|###6      | 93/252 [00:41<01:25,  1.86it/s]
[2025-07-31 11:05:22,976: WARNING/ForkPoolWorker-16] [A
[2025-07-31 11:05:23,058: WARNING/ForkPoolWorker-15] 
 53%|#####3    | 134/252 [00:50<00:53,  2.21it/s]
[2025-07-31 11:05:23,058: WARNING/ForkPoolWorker-15] [A
frame=  237 fps= 26 q=-1.0 size=    1024KiB time=N/A bitrate=N/A dup=0 drop=48 speed=N/A    
ffmpeg version 7.0.2-static https://johnvansickle.com/ffmpeg/  Copyright (c) 2000-2024 the FFmpeg developers
  built with gcc 8 (Debian 8.3.0-6)
  configuration: --enable-gpl --enable-version3 --enable-static --disable-debug --disable-ffplay --disable-indev=sndio --disable-outdev=sndio --cc=gcc --enable-fontconfig --enable-frei0r --enable-gnutls --enable-gmp --enable-libgme --enable-gray --enable-libaom --enable-libfribidi --enable-libass --enable-libvmaf --enable-libfreetype --enable-libmp3lame --enable-libopencore-amrnb --enable-libopencore-amrwb --enable-libopenjpeg --enable-librubberband --enable-libsoxr --enable-libspeex --enable-libsrt --enable-libvorbis --enable-libopus --enable-libtheora --enable-libvidstab --enable-libvo-amrwbenc --enable-libvpx --enable-libwebp --enable-libx264 --enable-libx265 --enable-libxml2 --enable-libdav1d --enable-libxvid --enable-libzvbi --enable-libzimg
  libavutil      59.  8.100 / 59.  8.100
  libavcodec     61.  3.100 / 61.  3.100
  libavformat    61.  1.100 / 61.  1.100
  libavdevice    61.  1.100 / 61.  1.100
  libavfilter    10.  1.100 / 10.  1.100
  libswscale      8.  1.100 /  8.  1.100
  libswresample   5.  1.100 /  5.  1.100
  libpostproc    58.  1.100 / 58.  1.100
[mp3 @ 0xf5f6ec0] Estimating duration from bitrate, this may be inaccurate
Input #0, mp3, from '/tmp/lstmsync_gpu0_d4eghldb/input.wav':
  Metadata:
    encoder         : LAME3.101 (beta 2)
  Duration: 00:01:19.12, start: 0.000000, bitrate: 128 kb/s
  Stream #0:0: Audio: mp3 (mp3float), 44100 Hz, stereo, fltp, 128 kb/s
Stream mapping:
  Stream #0:0 -> #0:0 (mp3 (mp3float) -> pcm_s16le (native))
Press [q] to stop, [?] for help
[out#0/mp4 @ 0x2f934d40] video:1153KiB audio:156KiB subtitle:0KiB other streams:0KiB global headers:0KiB muxing overhead: 0.675260%
frame=  252 fps= 28 q=-1.0 Lsize=    1318KiB time=00:00:10.00 bitrate=1079.8kbits/s dup=0 drop=48 speed= 1.1x    
[libx264 @ 0x2f98fec0] frame I:2     Avg QP:16.64  size: 23511
[libx264 @ 0x2f98fec0] frame P:79    Avg QP:17.47  size:  9984
[libx264 @ 0x2f98fec0] frame B:171   Avg QP:20.85  size:  2012
[libx264 @ 0x2f98fec0] consecutive B-frames:  7.1%  4.8%  7.1% 81.0%
[libx264 @ 0x2f98fec0] mb I  I16..4: 33.0% 60.8%  6.2%
[libx264 @ 0x2f98fec0] mb P  I16..4:  3.2%  4.3%  0.3%  P16..4: 22.3%  4.6%  2.0%  0.0%  0.0%    skip:63.2%
[libx264 @ 0x2f98fec0] mb B  I16..4:  0.1%  0.1%  0.0%  B16..8: 18.3%  0.5%  0.0%  direct: 1.8%  skip:79.3%  L0:50.4% L1:48.4% BI: 1.2%
[libx264 @ 0x2f98fec0] 8x8 transform intra:56.1% inter:74.3%
[libx264 @ 0x2f98fec0] coded y,uvDC,uvAC intra: 16.1% 37.9% 5.4% inter: 2.5% 7.7% 0.0%
[libx264 @ 0x2f98fec0] i16 v,h,dc,p: 33% 35% 12% 20%
[libx264 @ 0x2f98fec0] i8 v,h,dc,ddl,ddr,vr,hd,vl,hu: 28% 19% 45%  1%  1%  1%  2%  1%  2%
[libx264 @ 0x2f98fec0] i4 v,h,dc,ddl,ddr,vr,hd,vl,hu: 36% 23% 26%  2%  3%  3%  3%  2%  1%
[libx264 @ 0x2f98fec0] i8c dc,h,v,p: 56% 21% 20%  2%
[libx264 @ 0x2f98fec0] Weighted P-Frames: Y:0.0% UV:0.0%
[libx264 @ 0x2f98fec0] ref P L0: 74.2%  6.1% 15.3%  4.5%
[libx264 @ 0x2f98fec0] ref B L0: 89.7%  9.0%  1.3%
[libx264 @ 0x2f98fec0] ref B L1: 97.0%  3.0%
[libx264 @ 0x2f98fec0] kb/s:936.37
[aac @ 0x308a8d00] Qavg: 2474.159
[2025-07-31 11:05:23,340: WARNING/ForkPoolWorker-16] 
 37%|###7      | 94/252 [00:41<01:16,  2.06it/s]
[2025-07-31 11:05:23,340: WARNING/ForkPoolWorker-16] [A
[2025-07-31 11:05:23,355: WARNING/ForkPoolWorker-15] 
 54%|#####3    | 135/252 [00:50<00:47,  2.46it/s]
[2025-07-31 11:05:23,388: WARNING/ForkPoolWorker-15] [A
[2025-07-31 11:05:23,393: WARNING/ForkPoolWorker-2] Reading video frames...
Output #0, wav, to '/tmp/lstmsync_gpu0_d4eghldb/temp.wav':
  Metadata:
    ISFT            : Lavf61.1.100
  Stream #0:0: Audio: pcm_s16le ([1][0][0][0] / 0x0001), 16000 Hz, mono, s16, 256 kb/s
      Metadata:
        encoder         : Lavc61.3.100 pcm_s16le
[2025-07-31 11:05:23,763: WARNING/ForkPoolWorker-16] 
 38%|###7      | 95/252 [00:42<01:13,  2.14it/s]
[2025-07-31 11:05:23,774: WARNING/ForkPoolWorker-16] [A
[2025-07-31 11:05:23,777: WARNING/ForkPoolWorker-15] 
 54%|#####3    | 136/252 [00:50<00:47,  2.44it/s]
[2025-07-31 11:05:23,786: WARNING/ForkPoolWorker-15] [A
size=     768KiB time=00:00:26.06 bitrate= 241.3kbits/s speed=52.1x    
[out#0/wav @ 0xf5fb200] video:0KiB audio:2473KiB subtitle:0KiB other streams:0KiB global headers:0KiB muxing overhead: 0.003081%
size=    2473KiB time=00:01:19.12 bitrate= 256.0kbits/s speed=84.9x    
[2025-07-31 11:05:24,239: WARNING/ForkPoolWorker-15] 
 54%|#####4    | 137/252 [00:51<00:48,  2.35it/s]
[2025-07-31 11:05:24,242: WARNING/ForkPoolWorker-15] [A
[2025-07-31 11:05:24,269: WARNING/ForkPoolWorker-16] 
 38%|###8      | 96/252 [00:42<01:14,  2.09it/s]
[2025-07-31 11:05:24,269: WARNING/ForkPoolWorker-16] [A
[2025-07-31 11:05:24,686: WARNING/ForkPoolWorker-15] 
 55%|#####4    | 138/252 [00:51<00:49,  2.31it/s]
[2025-07-31 11:05:24,695: WARNING/ForkPoolWorker-15] [A
[2025-07-31 11:05:24,755: WARNING/ForkPoolWorker-16] 
 38%|###8      | 97/252 [00:43<01:14,  2.08it/s]
[2025-07-31 11:05:24,756: WARNING/ForkPoolWorker-16] [A
Some weights of the model checkpoint at ./checkpoints/chinese-hubert-large were not used when initializing HubertModel: ['hubert.feature_extractor.conv_layers.0.conv.bias', 'hubert.feature_extractor.conv_layers.1.conv.bias', 'hubert.feature_extractor.conv_layers.2.conv.bias', 'hubert.feature_extractor.conv_layers.3.conv.bias', 'hubert.feature_extractor.conv_layers.4.conv.bias', 'hubert.feature_extractor.conv_layers.5.conv.bias', 'hubert.feature_extractor.conv_layers.6.conv.bias', 'lm_head.bias', 'lm_head.weight']
- This IS expected if you are initializing HubertModel from the checkpoint of a model trained on another task or with another architecture (e.g. initializing a BertForSequenceClassification model from a BertForPreTraining model).
- This IS NOT expected if you are initializing HubertModel from the checkpoint of a model that you expect to be exactly identical (initializing a BertForSequenceClassification model from a BertForSequenceClassification model).
[2025-07-31 11:05:25,139: WARNING/ForkPoolWorker-15] 
 55%|#####5    | 139/252 [00:52<00:49,  2.28it/s]
[2025-07-31 11:05:25,139: WARNING/ForkPoolWorker-15] [A
[2025-07-31 11:05:25,304: WARNING/ForkPoolWorker-16] 
 39%|###8      | 98/252 [00:43<01:17,  1.99it/s]
[2025-07-31 11:05:25,305: WARNING/ForkPoolWorker-16] [A
[2025-07-31 11:05:25,648: WARNING/ForkPoolWorker-16] 
 39%|###9      | 99/252 [00:44<01:09,  2.20it/s]
[2025-07-31 11:05:25,650: WARNING/ForkPoolWorker-16] [A
[2025-07-31 11:05:25,833: WARNING/ForkPoolWorker-15] 
 56%|#####5    | 140/252 [00:52<00:57,  1.94it/s]
[2025-07-31 11:05:25,833: WARNING/ForkPoolWorker-15] [A
[2025-07-31 11:05:26,160: WARNING/ForkPoolWorker-16] 
 40%|###9      | 100/252 [00:44<01:11,  2.12it/s]
[2025-07-31 11:05:26,166: WARNING/ForkPoolWorker-16] [A
[2025-07-31 11:05:26,210: WARNING/ForkPoolWorker-15] 
 56%|#####5    | 141/252 [00:53<00:52,  2.11it/s]
[2025-07-31 11:05:26,210: WARNING/ForkPoolWorker-15] [A
[2025-07-31 11:05:26,511: WARNING/ForkPoolWorker-16] 
 40%|####      | 101/252 [00:44<01:05,  2.30it/s]
[2025-07-31 11:05:26,511: WARNING/ForkPoolWorker-16] [A
[2025-07-31 11:05:26,662: WARNING/ForkPoolWorker-15] 
 56%|#####6    | 142/252 [00:53<00:51,  2.14it/s]
[2025-07-31 11:05:26,662: WARNING/ForkPoolWorker-15] [A
[2025-07-31 11:05:26,904: WARNING/ForkPoolWorker-16] 
 40%|####      | 102/252 [00:45<01:03,  2.37it/s]
[2025-07-31 11:05:26,904: WARNING/ForkPoolWorker-16] [A
[2025-07-31 11:05:27,167: WARNING/ForkPoolWorker-15] 
 57%|#####6    | 143/252 [00:54<00:52,  2.09it/s]
[2025-07-31 11:05:27,168: WARNING/ForkPoolWorker-15] [A
[2025-07-31 11:05:27,288: WARNING/ForkPoolWorker-16] 
 41%|####      | 103/252 [00:45<01:01,  2.43it/s]
[2025-07-31 11:05:27,288: WARNING/ForkPoolWorker-16] [A
[2025-07-31 11:05:27,453: WARNING/ForkPoolWorker-15] 
 57%|#####7    | 144/252 [00:54<00:45,  2.38it/s]
[2025-07-31 11:05:27,453: WARNING/ForkPoolWorker-15] [A
[2025-07-31 11:05:27,657: WARNING/ForkPoolWorker-16] 
 41%|####1     | 104/252 [00:46<00:58,  2.51it/s]
[2025-07-31 11:05:27,657: WARNING/ForkPoolWorker-16] [A
[2025-07-31 11:05:27,771: WARNING/ForkPoolWorker-15] 
 58%|#####7    | 145/252 [00:54<00:41,  2.56it/s]
[2025-07-31 11:05:27,771: WARNING/ForkPoolWorker-15] [A
ffmpeg version 7.0.2-static https://johnvansickle.com/ffmpeg/  Copyright (c) 2000-2024 the FFmpeg developers
  built with gcc 8 (Debian 8.3.0-6)
  configuration: --enable-gpl --enable-version3 --enable-static --disable-debug --disable-ffplay --disable-indev=sndio --disable-outdev=sndio --cc=gcc --enable-fontconfig --enable-frei0r --enable-gnutls --enable-gmp --enable-libgme --enable-gray --enable-libaom --enable-libfribidi --enable-libass --enable-libvmaf --enable-libfreetype --enable-libmp3lame --enable-libopencore-amrnb --enable-libopencore-amrwb --enable-libopenjpeg --enable-librubberband --enable-libsoxr --enable-libspeex --enable-libsrt --enable-libvorbis --enable-libopus --enable-libtheora --enable-libvidstab --enable-libvo-amrwbenc --enable-libvpx --enable-libwebp --enable-libx264 --enable-libx265 --enable-libxml2 --enable-libdav1d --enable-libxvid --enable-libzvbi --enable-libzimg
  libavutil      59.  8.100 / 59.  8.100
  libavcodec     61.  3.100 / 61.  3.100
  libavformat    61.  1.100 / 61.  1.100
  libavdevice    61.  1.100 / 61.  1.100
  libavfilter    10.  1.100 / 10.  1.100
  libswscale      8.  1.100 /  8.  1.100
  libswresample   5.  1.100 /  5.  1.100
  libpostproc    58.  1.100 / 58.  1.100
[mp3 @ 0x274d5ec0] Estimating duration from bitrate, this may be inaccurate
Input #0, mp3, from '/tmp/lstmsync_gpu0_hct5x_rq/input.wav':
  Metadata:
    encoder         : LAME3.101 (beta 2)
  Duration: 00:01:19.12, start: 0.000000, bitrate: 128 kb/s
  Stream #0:0: Audio: mp3 (mp3float), 44100 Hz, stereo, fltp, 128 kb/s
Stream mapping:
  Stream #0:0 -> #0:0 (mp3 (mp3float) -> pcm_s16le (native))
Press [q] to stop, [?] for help
[2025-07-31 11:05:28,109: WARNING/ForkPoolWorker-15] 
 58%|#####7    | 146/252 [00:55<00:39,  2.67it/s]
[2025-07-31 11:05:28,109: WARNING/ForkPoolWorker-15] [A
[2025-07-31 11:05:28,164: WARNING/ForkPoolWorker-16] 
 42%|####1     | 105/252 [00:46<01:03,  2.32it/s]
[2025-07-31 11:05:28,182: WARNING/ForkPoolWorker-16] [A
[2025-07-31 11:05:28,305: WARNING/ForkPoolWorker-1] Length of rep chunks: 1974
OpenCV: FFMPEG: tag 0x58564944/'DIVX' is not supported with codec id 12 and format 'mp4 / MP4 (MPEG-4 Part 14)'
OpenCV: FFMPEG: fallback to use tag 0x7634706d/'mp4v'
Output #0, wav, to '/tmp/lstmsync_gpu0_hct5x_rq/temp.wav':
  Metadata:
    ISFT            : Lavf61.1.100
  Stream #0:0: Audio: pcm_s16le ([1][0][0][0] / 0x0001), 16000 Hz, mono, s16, 256 kb/s
      Metadata:
        encoder         : Lavc61.3.100 pcm_s16le
[2025-07-31 11:05:28,326: WARNING/ForkPoolWorker-1] 
  0%|          | 0/494 [00:00<?, ?it/s]
[2025-07-31 11:05:28,326: WARNING/ForkPoolWorker-1] 
  0%|          | 0/252 [00:00<?, ?it/s]
[2025-07-31 11:05:28,327: WARNING/ForkPoolWorker-1] [A
[2025-07-31 11:05:28,355: WARNING/ForkPoolWorker-15] 
 58%|#####8    | 147/252 [00:55<00:35,  2.98it/s]
[2025-07-31 11:05:28,355: WARNING/ForkPoolWorker-15] [A
size=     512KiB time=00:00:19.06 bitrate= 220.0kbits/s speed=38.1x    
[2025-07-31 11:05:28,747: WARNING/ForkPoolWorker-16] 
 42%|####2     | 106/252 [00:47<01:09,  2.10it/s]
[2025-07-31 11:05:28,747: WARNING/ForkPoolWorker-16] [A
[2025-07-31 11:05:28,863: WARNING/ForkPoolWorker-15] 
 59%|#####8    | 148/252 [00:56<00:40,  2.58it/s]
[2025-07-31 11:05:28,863: WARNING/ForkPoolWorker-15] [A
size=    2048KiB time=00:01:09.64 bitrate= 240.9kbits/s speed=69.6x    
[2025-07-31 11:05:29,028: WARNING/ForkPoolWorker-16] 
 42%|####2     | 107/252 [00:47<01:00,  2.39it/s]
[2025-07-31 11:05:29,028: WARNING/ForkPoolWorker-16] [A
[out#0/wav @ 0x274da200] video:0KiB audio:2473KiB subtitle:0KiB other streams:0KiB global headers:0KiB muxing overhead: 0.003081%
size=    2473KiB time=00:01:19.12 bitrate= 256.0kbits/s speed=73.9x    
[2025-07-31 11:05:29,520: WARNING/ForkPoolWorker-16] 
 43%|####2     | 108/252 [00:47<01:03,  2.27it/s]
[2025-07-31 11:05:29,526: WARNING/ForkPoolWorker-16] [A
[2025-07-31 11:05:29,581: WARNING/ForkPoolWorker-15] 
 59%|#####9    | 149/252 [00:56<00:50,  2.06it/s]
[2025-07-31 11:05:29,581: WARNING/ForkPoolWorker-15] [A
[2025-07-31 11:05:29,835: WARNING/ForkPoolWorker-16] 
 43%|####3     | 109/252 [00:48<00:57,  2.48it/s]
[2025-07-31 11:05:29,835: WARNING/ForkPoolWorker-16] [A
[2025-07-31 11:05:29,894: WARNING/ForkPoolWorker-1] 
  0%|          | 1/252 [00:01<06:26,  1.54s/it]
[2025-07-31 11:05:29,894: WARNING/ForkPoolWorker-1] [A
Some weights of the model checkpoint at ./checkpoints/chinese-hubert-large were not used when initializing HubertModel: ['hubert.feature_extractor.conv_layers.0.conv.bias', 'hubert.feature_extractor.conv_layers.1.conv.bias', 'hubert.feature_extractor.conv_layers.2.conv.bias', 'hubert.feature_extractor.conv_layers.3.conv.bias', 'hubert.feature_extractor.conv_layers.4.conv.bias', 'hubert.feature_extractor.conv_layers.5.conv.bias', 'hubert.feature_extractor.conv_layers.6.conv.bias', 'lm_head.bias', 'lm_head.weight']
- This IS expected if you are initializing HubertModel from the checkpoint of a model trained on another task or with another architecture (e.g. initializing a BertForSequenceClassification model from a BertForPreTraining model).
- This IS NOT expected if you are initializing HubertModel from the checkpoint of a model that you expect to be exactly identical (initializing a BertForSequenceClassification model from a BertForSequenceClassification model).
[2025-07-31 11:05:30,200: WARNING/ForkPoolWorker-16] 
 44%|####3     | 110/252 [00:48<00:55,  2.56it/s]
[2025-07-31 11:05:30,201: WARNING/ForkPoolWorker-16] [A
[2025-07-31 11:05:30,244: WARNING/ForkPoolWorker-15] 
 60%|#####9    | 150/252 [00:57<00:55,  1.85it/s]
[2025-07-31 11:05:30,244: WARNING/ForkPoolWorker-15] [A
[2025-07-31 11:05:30,349: WARNING/ForkPoolWorker-1] 
  1%|          | 2/252 [00:02<03:49,  1.09it/s]
[2025-07-31 11:05:30,349: WARNING/ForkPoolWorker-1] [A
[2025-07-31 11:05:30,732: WARNING/ForkPoolWorker-16] 
 44%|####4     | 111/252 [00:49<01:01,  2.31it/s]
[2025-07-31 11:05:30,734: WARNING/ForkPoolWorker-16] [A
[2025-07-31 11:05:30,784: WARNING/ForkPoolWorker-1] 
  1%|1         | 3/252 [00:02<02:53,  1.43it/s]
[2025-07-31 11:05:30,784: WARNING/ForkPoolWorker-1] [A
[2025-07-31 11:05:30,788: WARNING/ForkPoolWorker-15] 
 60%|#####9    | 151/252 [00:57<00:54,  1.85it/s]
[2025-07-31 11:05:30,788: WARNING/ForkPoolWorker-15] [A
[2025-07-31 11:05:31,047: WARNING/ForkPoolWorker-15] 
 60%|######    | 152/252 [00:58<00:45,  2.19it/s]
[2025-07-31 11:05:31,054: WARNING/ForkPoolWorker-15] [A
[2025-07-31 11:05:31,176: WARNING/ForkPoolWorker-1] 
  2%|1         | 4/252 [00:02<02:23,  1.73it/s]
[2025-07-31 11:05:31,186: WARNING/ForkPoolWorker-1] [A
[2025-07-31 11:05:31,548: WARNING/ForkPoolWorker-16] 
 44%|####4     | 112/252 [00:49<01:16,  1.82it/s]
[2025-07-31 11:05:31,549: WARNING/ForkPoolWorker-16] [A
[2025-07-31 11:05:31,612: WARNING/ForkPoolWorker-15] 
 61%|######    | 153/252 [00:58<00:48,  2.05it/s]
[2025-07-31 11:05:31,612: WARNING/ForkPoolWorker-15] [A
[2025-07-31 11:05:31,641: WARNING/ForkPoolWorker-1] 
  2%|1         | 5/252 [00:03<02:12,  1.86it/s]
[2025-07-31 11:05:31,641: WARNING/ForkPoolWorker-1] [A
[2025-07-31 11:05:31,916: WARNING/ForkPoolWorker-16] 
 45%|####4     | 113/252 [00:50<01:08,  2.02it/s]
[2025-07-31 11:05:31,926: WARNING/ForkPoolWorker-16] [A
[2025-07-31 11:05:32,216: WARNING/ForkPoolWorker-15] 
 61%|######1   | 154/252 [00:59<00:51,  1.91it/s]
[2025-07-31 11:05:32,230: WARNING/ForkPoolWorker-15] [A
[2025-07-31 11:05:32,300: WARNING/ForkPoolWorker-1] 
  2%|2         | 6/252 [00:03<02:22,  1.73it/s]
[2025-07-31 11:05:32,301: WARNING/ForkPoolWorker-1] [A
[2025-07-31 11:05:32,421: WARNING/ForkPoolWorker-16] 
 45%|####5     | 114/252 [00:50<01:08,  2.01it/s]
[2025-07-31 11:05:32,421: WARNING/ForkPoolWorker-16] [A
[2025-07-31 11:05:32,694: WARNING/ForkPoolWorker-15] 
 62%|######1   | 155/252 [00:59<00:47,  2.06it/s]
[2025-07-31 11:05:32,695: WARNING/ForkPoolWorker-15] [A
[2025-07-31 11:05:32,749: WARNING/ForkPoolWorker-1] 
  3%|2         | 7/252 [00:04<02:11,  1.87it/s]
[2025-07-31 11:05:32,774: WARNING/ForkPoolWorker-1] [A
[2025-07-31 11:05:33,052: WARNING/ForkPoolWorker-16] 
 46%|####5     | 115/252 [00:51<01:13,  1.86it/s]
[2025-07-31 11:05:33,052: WARNING/ForkPoolWorker-16] [A
[2025-07-31 11:05:33,124: WARNING/ForkPoolWorker-15] 
 62%|######1   | 156/252 [01:00<00:47,  2.03it/s]
[2025-07-31 11:05:33,125: WARNING/ForkPoolWorker-15] [A
[2025-07-31 11:05:33,276: WARNING/ForkPoolWorker-1] 
  3%|3         | 8/252 [00:04<02:10,  1.88it/s]
[2025-07-31 11:05:33,290: WARNING/ForkPoolWorker-1] [A
[2025-07-31 11:05:33,423: WARNING/ForkPoolWorker-16] 
 46%|####6     | 116/252 [00:51<01:06,  2.05it/s]
[2025-07-31 11:05:33,428: WARNING/ForkPoolWorker-16] [A
[2025-07-31 11:05:33,522: WARNING/ForkPoolWorker-15] 
 62%|######2   | 157/252 [01:00<00:44,  2.15it/s]
[2025-07-31 11:05:33,522: WARNING/ForkPoolWorker-15] [A
[2025-07-31 11:05:33,716: WARNING/ForkPoolWorker-15] 
 63%|######2   | 158/252 [01:00<00:36,  2.61it/s]
[2025-07-31 11:05:33,722: WARNING/ForkPoolWorker-15] [A
[2025-07-31 11:05:33,839: WARNING/ForkPoolWorker-1] 
  4%|3         | 9/252 [00:05<02:11,  1.84it/s]
[2025-07-31 11:05:33,839: WARNING/ForkPoolWorker-1] [A
[2025-07-31 11:05:33,876: WARNING/ForkPoolWorker-2] Length of rep chunks: 1974
OpenCV: FFMPEG: tag 0x58564944/'DIVX' is not supported with codec id 12 and format 'mp4 / MP4 (MPEG-4 Part 14)'
OpenCV: FFMPEG: fallback to use tag 0x7634706d/'mp4v'
[2025-07-31 11:05:33,983: WARNING/ForkPoolWorker-2] 
  0%|          | 0/494 [00:00<?, ?it/s]
[2025-07-31 11:05:33,983: WARNING/ForkPoolWorker-2] 
  0%|          | 0/252 [00:00<?, ?it/s]
[2025-07-31 11:05:33,983: WARNING/ForkPoolWorker-2] [A
[2025-07-31 11:05:34,034: WARNING/ForkPoolWorker-16] 
 46%|####6     | 117/252 [00:52<01:10,  1.91it/s]
[2025-07-31 11:05:34,034: WARNING/ForkPoolWorker-16] [A
[2025-07-31 11:05:34,320: WARNING/ForkPoolWorker-1] 
  4%|3         | 10/252 [00:05<02:06,  1.91it/s]
[2025-07-31 11:05:34,330: WARNING/ForkPoolWorker-1] [A
[2025-07-31 11:05:34,582: WARNING/ForkPoolWorker-15] 
 63%|######3   | 159/252 [01:01<00:49,  1.89it/s]
[2025-07-31 11:05:34,582: WARNING/ForkPoolWorker-15] [A
[2025-07-31 11:05:34,946: WARNING/ForkPoolWorker-16] 
 47%|####6     | 118/252 [00:53<01:25,  1.56it/s]
[2025-07-31 11:05:34,954: WARNING/ForkPoolWorker-16] [A
[2025-07-31 11:05:35,128: WARNING/ForkPoolWorker-15] 
 63%|######3   | 160/252 [01:02<00:49,  1.87it/s]
[2025-07-31 11:05:35,128: WARNING/ForkPoolWorker-15] [A
[2025-07-31 11:05:35,276: WARNING/ForkPoolWorker-1] 
  4%|4         | 11/252 [00:06<02:38,  1.53it/s]
[2025-07-31 11:05:35,276: WARNING/ForkPoolWorker-1] [A
[2025-07-31 11:05:35,472: WARNING/ForkPoolWorker-16] 
 47%|####7     | 119/252 [00:53<01:20,  1.65it/s]
[2025-07-31 11:05:35,472: WARNING/ForkPoolWorker-16] [A
[2025-07-31 11:05:35,757: WARNING/ForkPoolWorker-1] 
  5%|4         | 12/252 [00:07<02:24,  1.66it/s]
[2025-07-31 11:05:35,757: WARNING/ForkPoolWorker-1] [A
[2025-07-31 11:05:35,857: WARNING/ForkPoolWorker-15] 
 64%|######3   | 161/252 [01:03<00:53,  1.69it/s]
[2025-07-31 11:05:35,870: WARNING/ForkPoolWorker-15] [A
[2025-07-31 11:05:36,165: WARNING/ForkPoolWorker-16] 
 48%|####7     | 120/252 [00:54<01:23,  1.58it/s]
[2025-07-31 11:05:36,165: WARNING/ForkPoolWorker-16] [A
[2025-07-31 11:05:36,261: WARNING/ForkPoolWorker-2] 
  0%|          | 1/252 [00:02<09:31,  2.28s/it]
[2025-07-31 11:05:36,261: WARNING/ForkPoolWorker-2] [A
[2025-07-31 11:05:36,432: WARNING/ForkPoolWorker-1] 
  5%|5         | 13/252 [00:08<02:29,  1.60it/s]
[2025-07-31 11:05:36,432: WARNING/ForkPoolWorker-1] [A
[2025-07-31 11:05:36,952: WARNING/ForkPoolWorker-16] 
 48%|####8     | 121/252 [00:55<01:28,  1.47it/s]
[2025-07-31 11:05:36,982: WARNING/ForkPoolWorker-16] [A
[2025-07-31 11:05:36,991: WARNING/ForkPoolWorker-2] 
  1%|          | 2/252 [00:03<05:41,  1.37s/it]
[2025-07-31 11:05:36,991: WARNING/ForkPoolWorker-2] [A
[2025-07-31 11:05:36,978: WARNING/ForkPoolWorker-15] 
 64%|######4   | 162/252 [01:04<01:07,  1.33it/s]
[2025-07-31 11:05:36,994: WARNING/ForkPoolWorker-15] [A
[2025-07-31 11:05:37,096: WARNING/ForkPoolWorker-1] 
  6%|5         | 14/252 [00:08<02:31,  1.57it/s]
[2025-07-31 11:05:37,096: WARNING/ForkPoolWorker-1] [A
[2025-07-31 11:05:37,594: WARNING/ForkPoolWorker-2] 
  1%|1         | 3/252 [00:03<04:13,  1.02s/it]
[2025-07-31 11:05:37,594: WARNING/ForkPoolWorker-2] [A
[2025-07-31 11:05:37,707: WARNING/ForkPoolWorker-16] 
 48%|####8     | 122/252 [00:56<01:31,  1.43it/s]
[2025-07-31 11:05:37,708: WARNING/ForkPoolWorker-16] [A
[2025-07-31 11:05:37,884: WARNING/ForkPoolWorker-2] 
  2%|1         | 4/252 [00:03<03:01,  1.37it/s]
[2025-07-31 11:05:37,884: WARNING/ForkPoolWorker-2] [A
[2025-07-31 11:05:37,896: WARNING/ForkPoolWorker-15] 
 65%|######4   | 163/252 [01:05<01:11,  1.25it/s]
[2025-07-31 11:05:37,897: WARNING/ForkPoolWorker-15] [A
[2025-07-31 11:05:37,940: WARNING/ForkPoolWorker-1] 
  6%|5         | 15/252 [00:09<02:45,  1.43it/s]
[2025-07-31 11:05:37,958: WARNING/ForkPoolWorker-1] [A
[2025-07-31 11:05:38,482: WARNING/ForkPoolWorker-1] 
  6%|6         | 16/252 [00:10<02:33,  1.53it/s]
[2025-07-31 11:05:38,482: WARNING/ForkPoolWorker-1] [A
[2025-07-31 11:05:38,560: WARNING/ForkPoolWorker-16] 
 49%|####8     | 123/252 [00:56<01:36,  1.34it/s]
[2025-07-31 11:05:38,561: WARNING/ForkPoolWorker-16] [A
[2025-07-31 11:05:38,786: WARNING/ForkPoolWorker-15] 
 65%|######5   | 164/252 [01:05<01:12,  1.21it/s]
[2025-07-31 11:05:38,786: WARNING/ForkPoolWorker-15] [A
[2025-07-31 11:05:39,078: WARNING/ForkPoolWorker-1] 
  7%|6         | 17/252 [00:10<02:29,  1.57it/s]
[2025-07-31 11:05:39,078: WARNING/ForkPoolWorker-1] [A
[2025-07-31 11:05:39,118: WARNING/ForkPoolWorker-15] 
 65%|######5   | 165/252 [01:06<00:59,  1.47it/s]
[2025-07-31 11:05:39,138: WARNING/ForkPoolWorker-15] [A
[2025-07-31 11:05:39,141: WARNING/ForkPoolWorker-2] 
  2%|1         | 5/252 [00:05<03:47,  1.09it/s]
[2025-07-31 11:05:39,141: WARNING/ForkPoolWorker-2] [A
[2025-07-31 11:05:39,472: WARNING/ForkPoolWorker-16] 
 49%|####9     | 124/252 [00:57<01:41,  1.26it/s]
[2025-07-31 11:05:39,472: WARNING/ForkPoolWorker-16] [A
[2025-07-31 11:05:39,783: WARNING/ForkPoolWorker-1] 
  7%|7         | 18/252 [00:11<02:33,  1.52it/s]
[2025-07-31 11:05:39,783: WARNING/ForkPoolWorker-1] [A
[2025-07-31 11:05:39,838: WARNING/ForkPoolWorker-15] 
 66%|######5   | 166/252 [01:06<00:59,  1.45it/s]
[2025-07-31 11:05:39,866: WARNING/ForkPoolWorker-15] [A
[2025-07-31 11:05:40,037: WARNING/ForkPoolWorker-2] 
  2%|2         | 6/252 [00:06<03:44,  1.10it/s]
[2025-07-31 11:05:40,038: WARNING/ForkPoolWorker-2] [A
[2025-07-31 11:05:40,272: WARNING/ForkPoolWorker-16] 
 50%|####9     | 125/252 [00:58<01:41,  1.25it/s]
[2025-07-31 11:05:40,272: WARNING/ForkPoolWorker-16] [A
[2025-07-31 11:05:40,314: WARNING/ForkPoolWorker-2] 
  3%|2         | 7/252 [00:06<02:51,  1.43it/s]
[2025-07-31 11:05:40,314: WARNING/ForkPoolWorker-2] [A
[2025-07-31 11:05:40,376: WARNING/ForkPoolWorker-15] 
 66%|######6   | 167/252 [01:07<00:54,  1.55it/s]
[2025-07-31 11:05:40,379: WARNING/ForkPoolWorker-15] [A
[2025-07-31 11:05:40,648: WARNING/ForkPoolWorker-1] 
  8%|7         | 19/252 [00:12<02:47,  1.39it/s]
[2025-07-31 11:05:40,648: WARNING/ForkPoolWorker-1] [A
[2025-07-31 11:05:41,068: WARNING/ForkPoolWorker-16] 
 50%|#####     | 126/252 [00:59<01:40,  1.25it/s]
[2025-07-31 11:05:41,068: WARNING/ForkPoolWorker-16] [A
[2025-07-31 11:05:41,108: WARNING/ForkPoolWorker-2] 
  3%|3         | 8/252 [00:07<02:59,  1.36it/s]
[2025-07-31 11:05:41,118: WARNING/ForkPoolWorker-2] [A
[2025-07-31 11:05:41,155: WARNING/ForkPoolWorker-15] 
 67%|######6   | 168/252 [01:08<00:57,  1.46it/s]
[2025-07-31 11:05:41,155: WARNING/ForkPoolWorker-15] [A
[2025-07-31 11:05:41,468: WARNING/ForkPoolWorker-1] 
  8%|7         | 20/252 [00:13<02:53,  1.33it/s]
[2025-07-31 11:05:41,468: WARNING/ForkPoolWorker-1] [A
[2025-07-31 11:05:41,730: WARNING/ForkPoolWorker-2] 
  4%|3         | 9/252 [00:07<02:49,  1.43it/s]
[2025-07-31 11:05:41,749: WARNING/ForkPoolWorker-2] [A
[2025-07-31 11:05:41,790: WARNING/ForkPoolWorker-15] 
 67%|######7   | 169/252 [01:08<00:55,  1.49it/s]
[2025-07-31 11:05:41,790: WARNING/ForkPoolWorker-15] [A
[2025-07-31 11:05:41,841: WARNING/ForkPoolWorker-16] 
 50%|#####     | 127/252 [01:00<01:38,  1.27it/s]
[2025-07-31 11:05:41,841: WARNING/ForkPoolWorker-16] [A
[2025-07-31 11:05:42,144: WARNING/ForkPoolWorker-1] 
  8%|8         | 21/252 [00:13<02:47,  1.38it/s]
[2025-07-31 11:05:42,144: WARNING/ForkPoolWorker-1] [A
[2025-07-31 11:05:42,424: WARNING/ForkPoolWorker-16] 
 51%|#####     | 128/252 [01:00<01:30,  1.37it/s]
[2025-07-31 11:05:42,425: WARNING/ForkPoolWorker-16] [A
[2025-07-31 11:05:42,507: WARNING/ForkPoolWorker-15] 
 67%|######7   | 170/252 [01:09<00:56,  1.46it/s]
[2025-07-31 11:05:42,508: WARNING/ForkPoolWorker-15] [A
[2025-07-31 11:05:42,814: WARNING/ForkPoolWorker-15] 
 68%|######7   | 171/252 [01:09<00:46,  1.75it/s]
[2025-07-31 11:05:42,926: WARNING/ForkPoolWorker-15] [A
[2025-07-31 11:05:42,931: WARNING/ForkPoolWorker-2] 
  4%|3         | 10/252 [00:08<03:26,  1.17it/s]
[2025-07-31 11:05:42,931: WARNING/ForkPoolWorker-2] [A
[2025-07-31 11:05:42,940: WARNING/ForkPoolWorker-1] 
  9%|8         | 22/252 [00:14<02:51,  1.34it/s]
[2025-07-31 11:05:42,974: WARNING/ForkPoolWorker-1] [A
[2025-07-31 11:05:43,357: WARNING/ForkPoolWorker-16] 
 51%|#####1    | 129/252 [01:01<01:37,  1.27it/s]
[2025-07-31 11:05:43,376: WARNING/ForkPoolWorker-16] [A
[2025-07-31 11:05:43,524: WARNING/ForkPoolWorker-15] 
 68%|######8   | 172/252 [01:10<00:49,  1.63it/s]
[2025-07-31 11:05:43,558: WARNING/ForkPoolWorker-15] [A
[2025-07-31 11:05:43,697: WARNING/ForkPoolWorker-1] 
  9%|9         | 23/252 [00:15<02:51,  1.33it/s]
[2025-07-31 11:05:43,697: WARNING/ForkPoolWorker-1] [A
[2025-07-31 11:05:43,854: WARNING/ForkPoolWorker-2] 
  4%|4         | 11/252 [00:09<03:27,  1.16it/s]
[2025-07-31 11:05:43,854: WARNING/ForkPoolWorker-2] [A
[2025-07-31 11:05:44,225: WARNING/ForkPoolWorker-16] 
 52%|#####1    | 130/252 [01:02<01:39,  1.23it/s]
[2025-07-31 11:05:44,225: WARNING/ForkPoolWorker-16] [A
[2025-07-31 11:05:44,352: WARNING/ForkPoolWorker-15] 
 69%|######8   | 173/252 [01:11<00:53,  1.48it/s]
[2025-07-31 11:05:44,352: WARNING/ForkPoolWorker-15] [A
[2025-07-31 11:05:44,528: WARNING/ForkPoolWorker-1] 
 10%|9         | 24/252 [00:16<02:56,  1.29it/s]
[2025-07-31 11:05:44,528: WARNING/ForkPoolWorker-1] [A
[2025-07-31 11:05:44,539: WARNING/ForkPoolWorker-2] 
  5%|4         | 12/252 [00:10<03:17,  1.22it/s]
[2025-07-31 11:05:44,539: WARNING/ForkPoolWorker-2] [A
[2025-07-31 11:05:45,136: WARNING/ForkPoolWorker-1] 
 10%|9         | 25/252 [00:16<02:44,  1.38it/s]
[2025-07-31 11:05:45,136: WARNING/ForkPoolWorker-1] [A
[2025-07-31 11:05:45,178: WARNING/ForkPoolWorker-2] 
  5%|5         | 13/252 [00:11<03:03,  1.31it/s]
[2025-07-31 11:05:45,182: WARNING/ForkPoolWorker-2] [A
[2025-07-31 11:05:45,205: WARNING/ForkPoolWorker-16] 
 52%|#####1    | 131/252 [01:03<01:44,  1.16it/s]
[2025-07-31 11:05:45,205: WARNING/ForkPoolWorker-16] [A
[2025-07-31 11:05:45,307: WARNING/ForkPoolWorker-15] 
 69%|######9   | 174/252 [01:12<00:59,  1.31it/s]
[2025-07-31 11:05:45,307: WARNING/ForkPoolWorker-15] [A
[2025-07-31 11:05:45,971: WARNING/ForkPoolWorker-1] 
 10%|#         | 26/252 [00:17<02:51,  1.32it/s]
[2025-07-31 11:05:45,971: WARNING/ForkPoolWorker-1] [A
[2025-07-31 11:05:46,139: WARNING/ForkPoolWorker-16] 
 52%|#####2    | 132/252 [01:04<01:46,  1.13it/s]
[2025-07-31 11:05:46,140: WARNING/ForkPoolWorker-16] [A
[2025-07-31 11:05:46,144: WARNING/ForkPoolWorker-15] 
 69%|######9   | 175/252 [01:13<01:00,  1.28it/s]
[2025-07-31 11:05:46,145: WARNING/ForkPoolWorker-15] [A
[2025-07-31 11:05:46,146: WARNING/ForkPoolWorker-2] 
  6%|5         | 14/252 [00:12<03:16,  1.21it/s]
[2025-07-31 11:05:46,146: WARNING/ForkPoolWorker-2] [A
[2025-07-31 11:05:46,737: WARNING/ForkPoolWorker-1] 
 11%|#         | 27/252 [00:18<02:51,  1.32it/s]
[2025-07-31 11:05:46,740: WARNING/ForkPoolWorker-1] [A
[2025-07-31 11:05:46,860: WARNING/ForkPoolWorker-2] 
  6%|5         | 15/252 [00:12<03:07,  1.26it/s]
[2025-07-31 11:05:46,866: WARNING/ForkPoolWorker-2] [A
[2025-07-31 11:05:46,929: WARNING/ForkPoolWorker-16] 
 53%|#####2    | 133/252 [01:05<01:41,  1.17it/s]
[2025-07-31 11:05:46,946: WARNING/ForkPoolWorker-16] [A
[2025-07-31 11:05:47,065: WARNING/ForkPoolWorker-15] 
 70%|######9   | 176/252 [01:14<01:02,  1.21it/s]
[2025-07-31 11:05:47,074: WARNING/ForkPoolWorker-15] [A
[2025-07-31 11:05:47,624: WARNING/ForkPoolWorker-16] 
 53%|#####3    | 134/252 [01:06<01:35,  1.24it/s]
[2025-07-31 11:05:47,630: WARNING/ForkPoolWorker-16] [A
[2025-07-31 11:05:47,683: WARNING/ForkPoolWorker-1] 
 11%|#1        | 28/252 [00:19<03:02,  1.23it/s]
[2025-07-31 11:05:47,683: WARNING/ForkPoolWorker-1] [A
[2025-07-31 11:05:47,696: WARNING/ForkPoolWorker-15] 
 70%|#######   | 177/252 [01:14<00:57,  1.30it/s]
[2025-07-31 11:05:47,696: WARNING/ForkPoolWorker-15] [A
[2025-07-31 11:05:47,785: WARNING/ForkPoolWorker-2] 
  6%|6         | 16/252 [00:13<03:16,  1.20it/s]
[2025-07-31 11:05:47,786: WARNING/ForkPoolWorker-2] [A
[2025-07-31 11:05:48,411: WARNING/ForkPoolWorker-16] 
 54%|#####3    | 135/252 [01:06<01:31,  1.27it/s]
[2025-07-31 11:05:48,411: WARNING/ForkPoolWorker-16] [A
[2025-07-31 11:05:48,568: WARNING/ForkPoolWorker-15] 
 71%|#######   | 178/252 [01:15<00:59,  1.25it/s]
[2025-07-31 11:05:48,568: WARNING/ForkPoolWorker-15] [A
[2025-07-31 11:05:48,589: WARNING/ForkPoolWorker-2] 
  7%|6         | 17/252 [00:14<03:13,  1.21it/s]
[2025-07-31 11:05:48,590: WARNING/ForkPoolWorker-2] [A
[2025-07-31 11:05:48,738: WARNING/ForkPoolWorker-1] 
 12%|#1        | 29/252 [00:20<03:17,  1.13it/s]
[2025-07-31 11:05:48,739: WARNING/ForkPoolWorker-1] [A
[2025-07-31 11:05:49,380: WARNING/ForkPoolWorker-16] 
 54%|#####3    | 136/252 [01:07<01:39,  1.17it/s]
[2025-07-31 11:05:49,390: WARNING/ForkPoolWorker-16] [A
[2025-07-31 11:05:49,404: WARNING/ForkPoolWorker-2] 
  7%|7         | 18/252 [00:15<03:12,  1.22it/s]
[2025-07-31 11:05:49,404: WARNING/ForkPoolWorker-2] [A
[2025-07-31 11:05:49,576: WARNING/ForkPoolWorker-1] 
 12%|#1        | 30/252 [00:21<03:13,  1.15it/s]
[2025-07-31 11:05:49,576: WARNING/ForkPoolWorker-1] [A
[2025-07-31 11:05:49,720: WARNING/ForkPoolWorker-15] 
 71%|#######1  | 179/252 [01:16<01:06,  1.11it/s]
[2025-07-31 11:05:49,720: WARNING/ForkPoolWorker-15] [A
[2025-07-31 11:05:50,214: WARNING/ForkPoolWorker-2] 
  8%|7         | 19/252 [00:16<03:10,  1.22it/s]
[2025-07-31 11:05:50,214: WARNING/ForkPoolWorker-2] [A
[2025-07-31 11:05:50,272: WARNING/ForkPoolWorker-16] 
 54%|#####4    | 137/252 [01:08<01:39,  1.15it/s]
[2025-07-31 11:05:50,272: WARNING/ForkPoolWorker-16] [A
[2025-07-31 11:05:50,516: WARNING/ForkPoolWorker-1] 
 12%|#2        | 31/252 [00:22<03:17,  1.12it/s]
[2025-07-31 11:05:50,531: WARNING/ForkPoolWorker-1] [A
[2025-07-31 11:05:50,552: WARNING/ForkPoolWorker-15] 
 71%|#######1  | 180/252 [01:17<01:03,  1.13it/s]
[2025-07-31 11:05:50,558: WARNING/ForkPoolWorker-15] [A
[2025-07-31 11:05:50,696: WARNING/ForkPoolWorker-2] 
  8%|7         | 20/252 [00:16<02:46,  1.39it/s]
[2025-07-31 11:05:50,735: WARNING/ForkPoolWorker-2] [A
[2025-07-31 11:05:50,884: WARNING/ForkPoolWorker-16] 
 55%|#####4    | 138/252 [01:09<01:30,  1.26it/s]
[2025-07-31 11:05:50,884: WARNING/ForkPoolWorker-16] [A
[2025-07-31 11:05:51,289: WARNING/ForkPoolWorker-15] 
 72%|#######1  | 181/252 [01:18<00:59,  1.19it/s]
[2025-07-31 11:05:51,289: WARNING/ForkPoolWorker-15] [A
[2025-07-31 11:05:51,458: WARNING/ForkPoolWorker-2] 
  8%|8         | 21/252 [00:17<02:48,  1.37it/s]
[2025-07-31 11:05:51,462: WARNING/ForkPoolWorker-2] [A
[2025-07-31 11:05:51,463: WARNING/ForkPoolWorker-1] 
 13%|#2        | 32/252 [00:23<03:20,  1.10it/s]
[2025-07-31 11:05:51,464: WARNING/ForkPoolWorker-1] [A
[2025-07-31 11:05:51,761: WARNING/ForkPoolWorker-16] 
 55%|#####5    | 139/252 [01:10<01:32,  1.22it/s]
[2025-07-31 11:05:51,761: WARNING/ForkPoolWorker-16] [A
[2025-07-31 11:05:52,096: WARNING/ForkPoolWorker-15] 
 72%|#######2  | 182/252 [01:19<00:58,  1.21it/s]
[2025-07-31 11:05:52,138: WARNING/ForkPoolWorker-15] [A
[2025-07-31 11:05:52,484: WARNING/ForkPoolWorker-2] 
  9%|8         | 22/252 [00:18<03:08,  1.22it/s]
[2025-07-31 11:05:52,484: WARNING/ForkPoolWorker-2] [A
[2025-07-31 11:05:52,600: WARNING/ForkPoolWorker-1] 
 13%|#3        | 33/252 [00:24<03:34,  1.02it/s]
[2025-07-31 11:05:52,600: WARNING/ForkPoolWorker-1] [A
[2025-07-31 11:05:52,680: WARNING/ForkPoolWorker-15] 
 73%|#######2  | 183/252 [01:19<00:52,  1.32it/s]
[2025-07-31 11:05:52,681: WARNING/ForkPoolWorker-15] [A
[2025-07-31 11:05:53,142: WARNING/ForkPoolWorker-16] 
 56%|#####5    | 140/252 [01:11<01:50,  1.01it/s]
[2025-07-31 11:05:53,162: WARNING/ForkPoolWorker-16] [A
[2025-07-31 11:05:53,632: WARNING/ForkPoolWorker-15] 
 73%|#######3  | 184/252 [01:20<00:55,  1.23it/s]
[2025-07-31 11:05:53,632: WARNING/ForkPoolWorker-15] [A
[2025-07-31 11:05:53,788: WARNING/ForkPoolWorker-1] 
 13%|#3        | 34/252 [00:25<03:46,  1.04s/it]
[2025-07-31 11:05:53,794: WARNING/ForkPoolWorker-1] [A
[2025-07-31 11:05:53,803: WARNING/ForkPoolWorker-2] 
  9%|9         | 23/252 [00:19<03:41,  1.03it/s]
[2025-07-31 11:05:53,858: WARNING/ForkPoolWorker-2] [A
[2025-07-31 11:05:53,968: WARNING/ForkPoolWorker-16] 
 56%|#####5    | 141/252 [01:12<01:44,  1.07it/s]
[2025-07-31 11:05:53,968: WARNING/ForkPoolWorker-16] [A
[2025-07-31 11:05:54,360: WARNING/ForkPoolWorker-2] 
 10%|9         | 24/252 [00:20<03:12,  1.18it/s]
[2025-07-31 11:05:54,370: WARNING/ForkPoolWorker-2] [A
[2025-07-31 11:05:54,644: WARNING/ForkPoolWorker-1] 
 14%|#3        | 35/252 [00:26<03:33,  1.02it/s]
[2025-07-31 11:05:54,644: WARNING/ForkPoolWorker-1] [A
[2025-07-31 11:05:54,735: WARNING/ForkPoolWorker-16] 
 56%|#####6    | 142/252 [01:13<01:37,  1.13it/s]
[2025-07-31 11:05:54,735: WARNING/ForkPoolWorker-16] [A
[2025-07-31 11:05:54,764: WARNING/ForkPoolWorker-15] 
 73%|#######3  | 185/252 [01:21<01:00,  1.10it/s]
[2025-07-31 11:05:54,765: WARNING/ForkPoolWorker-15] [A
[2025-07-31 11:05:55,352: WARNING/ForkPoolWorker-2] 
 10%|9         | 25/252 [00:21<03:21,  1.12it/s]
[2025-07-31 11:05:55,353: WARNING/ForkPoolWorker-2] [A
[2025-07-31 11:05:55,453: WARNING/ForkPoolWorker-1] 
 14%|#4        | 36/252 [00:27<03:21,  1.07it/s]
[2025-07-31 11:05:55,453: WARNING/ForkPoolWorker-1] [A
[2025-07-31 11:05:55,592: WARNING/ForkPoolWorker-16] 
 57%|#####6    | 143/252 [01:13<01:35,  1.14it/s]
[2025-07-31 11:05:55,596: WARNING/ForkPoolWorker-16] [A
[2025-07-31 11:05:55,684: WARNING/ForkPoolWorker-15] 
 74%|#######3  | 186/252 [01:22<01:00,  1.10it/s]
[2025-07-31 11:05:55,685: WARNING/ForkPoolWorker-15] [A
[2025-07-31 11:05:56,148: WARNING/ForkPoolWorker-2] 
 10%|#         | 26/252 [00:22<03:14,  1.16it/s]
[2025-07-31 11:05:56,148: WARNING/ForkPoolWorker-2] [A
[2025-07-31 11:05:56,278: WARNING/ForkPoolWorker-1] 
 15%|#4        | 37/252 [00:27<03:09,  1.13it/s]
[2025-07-31 11:05:56,278: WARNING/ForkPoolWorker-1] [A
[2025-07-31 11:05:56,464: WARNING/ForkPoolWorker-16] 
 57%|#####7    | 144/252 [01:14<01:34,  1.14it/s]
[2025-07-31 11:05:56,470: WARNING/ForkPoolWorker-16] [A
[2025-07-31 11:05:56,555: WARNING/ForkPoolWorker-15] 
 74%|#######4  | 187/252 [01:23<00:58,  1.11it/s]
[2025-07-31 11:05:56,581: WARNING/ForkPoolWorker-15] [A
[2025-07-31 11:05:57,128: WARNING/ForkPoolWorker-2] 
 11%|#         | 27/252 [00:23<03:21,  1.11it/s]
[2025-07-31 11:05:57,128: WARNING/ForkPoolWorker-2] [A
[2025-07-31 11:05:57,152: WARNING/ForkPoolWorker-1] 
 15%|#5        | 38/252 [00:28<03:12,  1.11it/s]
[2025-07-31 11:05:57,152: WARNING/ForkPoolWorker-1] [A
[2025-07-31 11:05:57,161: WARNING/ForkPoolWorker-16] 
 58%|#####7    | 145/252 [01:15<01:27,  1.22it/s]
[2025-07-31 11:05:57,161: WARNING/ForkPoolWorker-16] [A
[2025-07-31 11:05:57,688: WARNING/ForkPoolWorker-2] 
 11%|#1        | 28/252 [00:23<02:58,  1.26it/s]
[2025-07-31 11:05:57,694: WARNING/ForkPoolWorker-2] [A
[2025-07-31 11:05:57,768: WARNING/ForkPoolWorker-16] 
 58%|#####7    | 146/252 [01:16<01:20,  1.32it/s]
[2025-07-31 11:05:57,781: WARNING/ForkPoolWorker-16] [A
[2025-07-31 11:05:57,786: WARNING/ForkPoolWorker-15] 
 75%|#######4  | 188/252 [01:24<01:03,  1.00it/s]
[2025-07-31 11:05:57,786: WARNING/ForkPoolWorker-15] [A
[2025-07-31 11:05:57,880: WARNING/ForkPoolWorker-1] 
 15%|#5        | 39/252 [00:29<03:00,  1.18it/s]
[2025-07-31 11:05:57,880: WARNING/ForkPoolWorker-1] [A
[2025-07-31 11:05:58,354: WARNING/ForkPoolWorker-15] 
 75%|#######5  | 189/252 [01:25<00:54,  1.15it/s]
[2025-07-31 11:05:58,354: WARNING/ForkPoolWorker-15] [A
[2025-07-31 11:05:58,368: WARNING/ForkPoolWorker-16] 
 58%|#####8    | 147/252 [01:16<01:14,  1.41it/s]
[2025-07-31 11:05:58,368: WARNING/ForkPoolWorker-16] [A
[2025-07-31 11:05:58,392: WARNING/ForkPoolWorker-2] 
 12%|#1        | 29/252 [00:24<02:51,  1.30it/s]
[2025-07-31 11:05:58,393: WARNING/ForkPoolWorker-2] [A
[2025-07-31 11:05:58,724: WARNING/ForkPoolWorker-1] 
 16%|#5        | 40/252 [00:30<02:59,  1.18it/s]
[2025-07-31 11:05:58,724: WARNING/ForkPoolWorker-1] [A
[2025-07-31 11:05:59,192: WARNING/ForkPoolWorker-16] 
 59%|#####8    | 148/252 [01:17<01:17,  1.34it/s]
[2025-07-31 11:05:59,192: WARNING/ForkPoolWorker-16] [A
[2025-07-31 11:05:59,232: WARNING/ForkPoolWorker-15] 
 75%|#######5  | 190/252 [01:26<00:54,  1.15it/s]
[2025-07-31 11:05:59,232: WARNING/ForkPoolWorker-15] [A
[2025-07-31 11:05:59,390: WARNING/ForkPoolWorker-1] 
 16%|#6        | 41/252 [00:31<02:47,  1.26it/s]
[2025-07-31 11:05:59,390: WARNING/ForkPoolWorker-1] [A
[2025-07-31 11:05:59,409: WARNING/ForkPoolWorker-2] 
 12%|#1        | 30/252 [00:25<03:07,  1.19it/s]
[2025-07-31 11:05:59,422: WARNING/ForkPoolWorker-2] [A
[2025-07-31 11:05:59,984: WARNING/ForkPoolWorker-16] 
 59%|#####9    | 149/252 [01:18<01:18,  1.32it/s]
[2025-07-31 11:05:59,984: WARNING/ForkPoolWorker-16] [A
[2025-07-31 11:06:00,098: WARNING/ForkPoolWorker-15] 
 76%|#######5  | 191/252 [01:27<00:53,  1.15it/s]
[2025-07-31 11:06:00,098: WARNING/ForkPoolWorker-15] [A
[2025-07-31 11:06:00,328: WARNING/ForkPoolWorker-2] 
 12%|#2        | 31/252 [00:26<03:11,  1.15it/s]
[2025-07-31 11:06:00,328: WARNING/ForkPoolWorker-2] [A
[2025-07-31 11:06:00,448: WARNING/ForkPoolWorker-1] 
 17%|#6        | 42/252 [00:32<03:03,  1.15it/s]
[2025-07-31 11:06:00,448: WARNING/ForkPoolWorker-1] [A
[2025-07-31 11:06:00,648: WARNING/ForkPoolWorker-16] 
 60%|#####9    | 150/252 [01:19<01:14,  1.37it/s]
[2025-07-31 11:06:00,648: WARNING/ForkPoolWorker-16] [A
[2025-07-31 11:06:00,815: WARNING/ForkPoolWorker-15] 
 76%|#######6  | 192/252 [01:27<00:49,  1.21it/s]
[2025-07-31 11:06:00,815: WARNING/ForkPoolWorker-15] [A
[2025-07-31 11:06:01,100: WARNING/ForkPoolWorker-2] 
 13%|#2        | 32/252 [00:27<03:04,  1.19it/s]
[2025-07-31 11:06:01,100: WARNING/ForkPoolWorker-2] [A
[2025-07-31 11:06:01,405: WARNING/ForkPoolWorker-16] 
 60%|#####9    | 151/252 [01:19<01:14,  1.35it/s]
[2025-07-31 11:06:01,418: WARNING/ForkPoolWorker-16] [A
[2025-07-31 11:06:01,519: WARNING/ForkPoolWorker-1] 
 17%|#7        | 43/252 [00:33<03:14,  1.07it/s]
[2025-07-31 11:06:01,520: WARNING/ForkPoolWorker-1] [A
[2025-07-31 11:06:01,596: WARNING/ForkPoolWorker-2] 
 13%|#3        | 33/252 [00:27<02:40,  1.36it/s]
[2025-07-31 11:06:01,596: WARNING/ForkPoolWorker-2] [A
[2025-07-31 11:06:01,774: WARNING/ForkPoolWorker-15] 
 77%|#######6  | 193/252 [01:28<00:51,  1.16it/s]
[2025-07-31 11:06:01,774: WARNING/ForkPoolWorker-15] [A
[2025-07-31 11:06:01,968: WARNING/ForkPoolWorker-1] 
 17%|#7        | 44/252 [00:33<02:43,  1.27it/s]
[2025-07-31 11:06:01,968: WARNING/ForkPoolWorker-1] [A
[2025-07-31 11:06:02,097: WARNING/ForkPoolWorker-15] 
 77%|#######6  | 194/252 [01:29<00:40,  1.42it/s]
[2025-07-31 11:06:02,142: WARNING/ForkPoolWorker-15] [A
[2025-07-31 11:06:02,323: WARNING/ForkPoolWorker-16] 
 60%|######    | 152/252 [01:20<01:19,  1.26it/s]
[2025-07-31 11:06:02,323: WARNING/ForkPoolWorker-16] [A
[2025-07-31 11:06:02,498: WARNING/ForkPoolWorker-2] 
 13%|#3        | 34/252 [00:28<02:51,  1.27it/s]
[2025-07-31 11:06:02,499: WARNING/ForkPoolWorker-2] [A
[2025-07-31 11:06:02,691: WARNING/ForkPoolWorker-2] 
 14%|#3        | 35/252 [00:28<02:11,  1.65it/s]
[2025-07-31 11:06:02,692: WARNING/ForkPoolWorker-2] [A
[2025-07-31 11:06:02,781: WARNING/ForkPoolWorker-16] 
 61%|######    | 153/252 [01:21<01:08,  1.45it/s]
[2025-07-31 11:06:02,794: WARNING/ForkPoolWorker-16] [A
[2025-07-31 11:06:02,808: WARNING/ForkPoolWorker-1] 
 18%|#7        | 45/252 [00:34<02:46,  1.25it/s]
[2025-07-31 11:06:02,826: WARNING/ForkPoolWorker-1] [A
[2025-07-31 11:06:02,976: WARNING/ForkPoolWorker-15] 
 77%|#######7  | 195/252 [01:30<00:43,  1.32it/s]
[2025-07-31 11:06:03,002: WARNING/ForkPoolWorker-15] [A
[2025-07-31 11:06:03,452: WARNING/ForkPoolWorker-16] 
 61%|######1   | 154/252 [01:21<01:07,  1.46it/s]
[2025-07-31 11:06:03,452: WARNING/ForkPoolWorker-16] [A
[2025-07-31 11:06:03,513: WARNING/ForkPoolWorker-1] 
 18%|#8        | 46/252 [00:35<02:39,  1.29it/s]
[2025-07-31 11:06:03,513: WARNING/ForkPoolWorker-1] [A
[2025-07-31 11:06:03,681: WARNING/ForkPoolWorker-15] 
 78%|#######7  | 196/252 [01:30<00:41,  1.35it/s]
[2025-07-31 11:06:03,681: WARNING/ForkPoolWorker-15] [A
[2025-07-31 11:06:03,877: WARNING/ForkPoolWorker-2] 
 14%|#4        | 36/252 [00:29<02:48,  1.28it/s]
[2025-07-31 11:06:03,886: WARNING/ForkPoolWorker-2] [A
[2025-07-31 11:06:04,220: WARNING/ForkPoolWorker-16] 
 62%|######1   | 155/252 [01:22<01:08,  1.41it/s]
[2025-07-31 11:06:04,220: WARNING/ForkPoolWorker-16] [A
[2025-07-31 11:06:04,249: WARNING/ForkPoolWorker-1] 
 19%|#8        | 47/252 [00:35<02:36,  1.31it/s]
[2025-07-31 11:06:04,250: WARNING/ForkPoolWorker-1] [A
[2025-07-31 11:06:04,412: WARNING/ForkPoolWorker-15] 
 78%|#######8  | 197/252 [01:31<00:40,  1.36it/s]
[2025-07-31 11:06:04,438: WARNING/ForkPoolWorker-15] [A
[2025-07-31 11:06:04,745: WARNING/ForkPoolWorker-2] 
 15%|#4        | 37/252 [00:30<02:53,  1.24it/s]
[2025-07-31 11:06:04,746: WARNING/ForkPoolWorker-2] [A
[2025-07-31 11:06:05,028: WARNING/ForkPoolWorker-1] 
 19%|#9        | 48/252 [00:36<02:36,  1.30it/s]
[2025-07-31 11:06:05,028: WARNING/ForkPoolWorker-1] [A
[2025-07-31 11:06:05,150: WARNING/ForkPoolWorker-16] 
 62%|######1   | 156/252 [01:23<01:14,  1.29it/s]
[2025-07-31 11:06:05,150: WARNING/ForkPoolWorker-16] [A
[2025-07-31 11:06:05,230: WARNING/ForkPoolWorker-15] 
 79%|#######8  | 198/252 [01:32<00:41,  1.31it/s]
[2025-07-31 11:06:05,230: WARNING/ForkPoolWorker-15] [A
[2025-07-31 11:06:05,467: WARNING/ForkPoolWorker-2] 
 15%|#5        | 38/252 [00:31<02:47,  1.28it/s]
[2025-07-31 11:06:05,474: WARNING/ForkPoolWorker-2] [A
[2025-07-31 11:06:05,682: WARNING/ForkPoolWorker-1] 
 19%|#9        | 49/252 [00:37<02:28,  1.36it/s]
[2025-07-31 11:06:05,682: WARNING/ForkPoolWorker-1] [A
[2025-07-31 11:06:05,744: WARNING/ForkPoolWorker-15] 
 79%|#######8  | 199/252 [01:32<00:36,  1.45it/s]
[2025-07-31 11:06:05,744: WARNING/ForkPoolWorker-15] [A
[2025-07-31 11:06:06,048: WARNING/ForkPoolWorker-16] 
 62%|######2   | 157/252 [01:24<01:17,  1.23it/s]
[2025-07-31 11:06:06,048: WARNING/ForkPoolWorker-16] [A
[2025-07-31 11:06:06,065: WARNING/ForkPoolWorker-2] 
 15%|#5        | 39/252 [00:32<02:34,  1.38it/s]
[2025-07-31 11:06:06,065: WARNING/ForkPoolWorker-2] [A
[2025-07-31 11:06:06,333: WARNING/ForkPoolWorker-15] 
 79%|#######9  | 200/252 [01:33<00:34,  1.52it/s]
[2025-07-31 11:06:06,333: WARNING/ForkPoolWorker-15] [A
[2025-07-31 11:06:06,428: WARNING/ForkPoolWorker-1] 
 20%|#9        | 50/252 [00:38<02:28,  1.36it/s]
[2025-07-31 11:06:06,429: WARNING/ForkPoolWorker-1] [A
[2025-07-31 11:06:06,548: WARNING/ForkPoolWorker-16] 
 63%|######2   | 158/252 [01:24<01:07,  1.39it/s]
[2025-07-31 11:06:06,562: WARNING/ForkPoolWorker-16] [A
[2025-07-31 11:06:06,850: WARNING/ForkPoolWorker-2] 
 16%|#5        | 40/252 [00:32<02:37,  1.34it/s]
[2025-07-31 11:06:06,850: WARNING/ForkPoolWorker-2] [A
[2025-07-31 11:06:07,105: WARNING/ForkPoolWorker-1] 
 20%|##        | 51/252 [00:38<02:24,  1.39it/s]
[2025-07-31 11:06:07,105: WARNING/ForkPoolWorker-1] [A
[2025-07-31 11:06:07,226: WARNING/ForkPoolWorker-15] 
 80%|#######9  | 201/252 [01:34<00:37,  1.37it/s]
[2025-07-31 11:06:07,226: WARNING/ForkPoolWorker-15] [A
[2025-07-31 11:06:07,716: WARNING/ForkPoolWorker-1] 
 21%|##        | 52/252 [00:39<02:17,  1.46it/s]
[2025-07-31 11:06:07,716: WARNING/ForkPoolWorker-1] [A
[2025-07-31 11:06:07,836: WARNING/ForkPoolWorker-2] 
 16%|#6        | 41/252 [00:33<02:52,  1.22it/s]
[2025-07-31 11:06:07,836: WARNING/ForkPoolWorker-2] [A
[2025-07-31 11:06:07,882: WARNING/ForkPoolWorker-15] 
 80%|########  | 202/252 [01:35<00:35,  1.42it/s]
[2025-07-31 11:06:07,882: WARNING/ForkPoolWorker-15] [A
[2025-07-31 11:06:08,516: WARNING/ForkPoolWorker-16] 
 63%|######3   | 159/252 [01:26<01:41,  1.09s/it]
[2025-07-31 11:06:08,516: WARNING/ForkPoolWorker-16] [A
[2025-07-31 11:06:08,601: WARNING/ForkPoolWorker-2] 
 17%|#6        | 42/252 [00:34<02:48,  1.25it/s]
[2025-07-31 11:06:08,638: WARNING/ForkPoolWorker-2] [A
[2025-07-31 11:06:08,696: WARNING/ForkPoolWorker-1] 
 21%|##1       | 53/252 [00:40<02:34,  1.29it/s]
[2025-07-31 11:06:08,696: WARNING/ForkPoolWorker-1] [A
[2025-07-31 11:06:08,832: WARNING/ForkPoolWorker-15] 
 81%|########  | 203/252 [01:35<00:38,  1.28it/s]
[2025-07-31 11:06:08,838: WARNING/ForkPoolWorker-15] [A
[2025-07-31 11:06:09,368: WARNING/ForkPoolWorker-15] 
 81%|########  | 204/252 [01:36<00:33,  1.42it/s]
[2025-07-31 11:06:09,386: WARNING/ForkPoolWorker-15] [A
[2025-07-31 11:06:09,412: WARNING/ForkPoolWorker-1] 
 21%|##1       | 54/252 [00:41<02:29,  1.32it/s]
[2025-07-31 11:06:09,414: WARNING/ForkPoolWorker-1] [A
[2025-07-31 11:06:09,472: WARNING/ForkPoolWorker-2] 
 17%|#7        | 43/252 [00:35<02:51,  1.22it/s]
[2025-07-31 11:06:09,472: WARNING/ForkPoolWorker-2] [A
[2025-07-31 11:06:09,955: WARNING/ForkPoolWorker-2] 
 17%|#7        | 44/252 [00:35<02:29,  1.39it/s]
[2025-07-31 11:06:09,958: WARNING/ForkPoolWorker-2] [A
[2025-07-31 11:06:10,156: WARNING/ForkPoolWorker-15] 
 81%|########1 | 205/252 [01:37<00:34,  1.37it/s]
[2025-07-31 11:06:10,162: WARNING/ForkPoolWorker-15] [A
[2025-07-31 11:06:10,332: WARNING/ForkPoolWorker-1] 
 22%|##1       | 55/252 [00:42<02:38,  1.24it/s]
[2025-07-31 11:06:10,333: WARNING/ForkPoolWorker-1] [A
[2025-07-31 11:06:10,630: WARNING/ForkPoolWorker-2] 
 18%|#7        | 45/252 [00:36<02:26,  1.41it/s]
[2025-07-31 11:06:10,650: WARNING/ForkPoolWorker-2] [A
[2025-07-31 11:06:10,643: WARNING/ForkPoolWorker-16] 
 63%|######3   | 160/252 [01:29<02:09,  1.40s/it]
[2025-07-31 11:06:10,651: WARNING/ForkPoolWorker-16] [A
[2025-07-31 11:06:11,094: WARNING/ForkPoolWorker-15] 
 82%|########1 | 206/252 [01:38<00:36,  1.27it/s]
[2025-07-31 11:06:11,094: WARNING/ForkPoolWorker-15] [A
[2025-07-31 11:06:11,164: WARNING/ForkPoolWorker-1] 
 22%|##2       | 56/252 [00:42<02:39,  1.23it/s]
[2025-07-31 11:06:11,174: WARNING/ForkPoolWorker-1] [A
[2025-07-31 11:06:11,268: WARNING/ForkPoolWorker-2] 
 18%|#8        | 46/252 [00:37<02:21,  1.46it/s]
[2025-07-31 11:06:11,268: WARNING/ForkPoolWorker-2] [A
[2025-07-31 11:06:11,432: WARNING/ForkPoolWorker-16] 
 64%|######3   | 161/252 [01:29<01:50,  1.22s/it]
[2025-07-31 11:06:11,454: WARNING/ForkPoolWorker-16] [A
[2025-07-31 11:06:11,800: WARNING/ForkPoolWorker-1] 
 23%|##2       | 57/252 [00:43<02:28,  1.32it/s]
[2025-07-31 11:06:11,818: WARNING/ForkPoolWorker-1] [A
[2025-07-31 11:06:12,000: WARNING/ForkPoolWorker-2] 
 19%|#8        | 47/252 [00:38<02:23,  1.43it/s]
[2025-07-31 11:06:12,000: WARNING/ForkPoolWorker-2] [A
[2025-07-31 11:06:12,088: WARNING/ForkPoolWorker-15] 
 82%|########2 | 207/252 [01:39<00:38,  1.17it/s]
[2025-07-31 11:06:12,088: WARNING/ForkPoolWorker-15] [A
[2025-07-31 11:06:12,220: WARNING/ForkPoolWorker-16] 
 64%|######4   | 162/252 [01:30<01:38,  1.09s/it]
[2025-07-31 11:06:12,221: WARNING/ForkPoolWorker-16] [A
[2025-07-31 11:06:12,512: WARNING/ForkPoolWorker-1] 
 23%|##3       | 58/252 [00:44<02:24,  1.34it/s]
[2025-07-31 11:06:12,522: WARNING/ForkPoolWorker-1] [A
[2025-07-31 11:06:12,760: WARNING/ForkPoolWorker-2] 
 19%|#9        | 48/252 [00:38<02:26,  1.39it/s]
[2025-07-31 11:06:12,760: WARNING/ForkPoolWorker-2] [A
[2025-07-31 11:06:12,906: WARNING/ForkPoolWorker-15] 
 83%|########2 | 208/252 [01:40<00:37,  1.18it/s]
[2025-07-31 11:06:12,906: WARNING/ForkPoolWorker-15] [A
[2025-07-31 11:06:13,195: WARNING/ForkPoolWorker-1] 
 23%|##3       | 59/252 [00:44<02:20,  1.38it/s]
[2025-07-31 11:06:13,195: WARNING/ForkPoolWorker-1] [A
[2025-07-31 11:06:13,348: WARNING/ForkPoolWorker-16] 
 65%|######4   | 163/252 [01:31<01:38,  1.10s/it]
[2025-07-31 11:06:13,349: WARNING/ForkPoolWorker-16] [A
[2025-07-31 11:06:13,559: WARNING/ForkPoolWorker-1] 
 24%|##3       | 60/252 [00:45<01:58,  1.62it/s]
[2025-07-31 11:06:13,560: WARNING/ForkPoolWorker-1] [A
[2025-07-31 11:06:13,567: WARNING/ForkPoolWorker-2] 
 19%|#9        | 49/252 [00:39<02:31,  1.34it/s]
[2025-07-31 11:06:13,568: WARNING/ForkPoolWorker-2] [A
[2025-07-31 11:06:13,614: WARNING/ForkPoolWorker-15] 
 83%|########2 | 209/252 [01:40<00:34,  1.24it/s]
[2025-07-31 11:06:13,615: WARNING/ForkPoolWorker-15] [A
[2025-07-31 11:06:14,212: WARNING/ForkPoolWorker-2] 
 20%|#9        | 50/252 [00:40<02:24,  1.40it/s]
[2025-07-31 11:06:14,212: WARNING/ForkPoolWorker-2] [A
[2025-07-31 11:06:14,228: WARNING/ForkPoolWorker-16] 
 65%|######5   | 164/252 [01:32<01:31,  1.03s/it]
[2025-07-31 11:06:14,228: WARNING/ForkPoolWorker-16] [A
[2025-07-31 11:06:14,252: WARNING/ForkPoolWorker-1] 
 24%|##4       | 61/252 [00:45<02:02,  1.56it/s]
[2025-07-31 11:06:14,252: WARNING/ForkPoolWorker-1] [A
[2025-07-31 11:06:14,316: WARNING/ForkPoolWorker-15] 
 83%|########3 | 210/252 [01:41<00:32,  1.29it/s]
[2025-07-31 11:06:14,316: WARNING/ForkPoolWorker-15] [A
[2025-07-31 11:06:14,864: WARNING/ForkPoolWorker-16] 
 65%|######5   | 165/252 [01:33<01:19,  1.09it/s]
[2025-07-31 11:06:14,864: WARNING/ForkPoolWorker-16] [A
[2025-07-31 11:06:15,052: WARNING/ForkPoolWorker-1] 
 25%|##4       | 62/252 [00:46<02:10,  1.45it/s]
[2025-07-31 11:06:15,052: WARNING/ForkPoolWorker-1] [A
[2025-07-31 11:06:15,083: WARNING/ForkPoolWorker-2] 
 20%|##        | 51/252 [00:41<02:33,  1.31it/s]
[2025-07-31 11:06:15,083: WARNING/ForkPoolWorker-2] [A
[2025-07-31 11:06:15,152: WARNING/ForkPoolWorker-15] 
 84%|########3 | 211/252 [01:42<00:32,  1.26it/s]
[2025-07-31 11:06:15,152: WARNING/ForkPoolWorker-15] [A
[2025-07-31 11:06:15,431: WARNING/ForkPoolWorker-16] 
 66%|######5   | 166/252 [01:33<01:09,  1.23it/s]
[2025-07-31 11:06:15,431: WARNING/ForkPoolWorker-16] [A
[2025-07-31 11:06:15,715: WARNING/ForkPoolWorker-2] 
 21%|##        | 52/252 [00:41<02:24,  1.38it/s]
[2025-07-31 11:06:15,715: WARNING/ForkPoolWorker-2] [A
[2025-07-31 11:06:15,942: WARNING/ForkPoolWorker-15] 
 84%|########4 | 212/252 [01:43<00:31,  1.26it/s]
[2025-07-31 11:06:15,942: WARNING/ForkPoolWorker-15] [A
[2025-07-31 11:06:15,966: WARNING/ForkPoolWorker-1] 
 25%|##5       | 63/252 [00:47<02:22,  1.32it/s]
[2025-07-31 11:06:15,966: WARNING/ForkPoolWorker-1] [A
[2025-07-31 11:06:16,246: WARNING/ForkPoolWorker-16] 
 66%|######6   | 167/252 [01:34<01:09,  1.23it/s]
[2025-07-31 11:06:16,246: WARNING/ForkPoolWorker-16] [A
[2025-07-31 11:06:16,283: WARNING/ForkPoolWorker-2] 
 21%|##1       | 53/252 [00:42<02:14,  1.48it/s]
[2025-07-31 11:06:16,284: WARNING/ForkPoolWorker-2] [A
[2025-07-31 11:06:16,677: WARNING/ForkPoolWorker-1] 
 25%|##5       | 64/252 [00:48<02:19,  1.35it/s]
[2025-07-31 11:06:16,677: WARNING/ForkPoolWorker-1] [A
[2025-07-31 11:06:16,689: WARNING/ForkPoolWorker-15] 
 85%|########4 | 213/252 [01:43<00:30,  1.29it/s]
[2025-07-31 11:06:16,689: WARNING/ForkPoolWorker-15] [A
[2025-07-31 11:06:16,860: WARNING/ForkPoolWorker-2] 
 21%|##1       | 54/252 [00:42<02:07,  1.55it/s]
[2025-07-31 11:06:16,874: WARNING/ForkPoolWorker-2] [A
[2025-07-31 11:06:16,976: WARNING/ForkPoolWorker-16] 
 67%|######6   | 168/252 [01:35<01:06,  1.27it/s]
[2025-07-31 11:06:16,976: WARNING/ForkPoolWorker-16] [A
[2025-07-31 11:06:17,516: WARNING/ForkPoolWorker-1] 
 26%|##5       | 65/252 [00:49<02:24,  1.30it/s]
[2025-07-31 11:06:17,517: WARNING/ForkPoolWorker-1] [A
[2025-07-31 11:06:17,567: WARNING/ForkPoolWorker-16] 
 67%|######7   | 169/252 [01:35<01:00,  1.37it/s]
[2025-07-31 11:06:17,574: WARNING/ForkPoolWorker-16] [A
[2025-07-31 11:06:17,601: WARNING/ForkPoolWorker-15] 
 85%|########4 | 214/252 [01:44<00:31,  1.22it/s]
[2025-07-31 11:06:17,601: WARNING/ForkPoolWorker-15] [A
[2025-07-31 11:06:17,601: WARNING/ForkPoolWorker-2] 
 22%|##1       | 55/252 [00:43<02:12,  1.48it/s]
[2025-07-31 11:06:17,602: WARNING/ForkPoolWorker-2] [A
[2025-07-31 11:06:18,133: WARNING/ForkPoolWorker-16] 
 67%|######7   | 170/252 [01:36<00:55,  1.47it/s]
[2025-07-31 11:06:18,133: WARNING/ForkPoolWorker-16] [A
[2025-07-31 11:06:18,183: WARNING/ForkPoolWorker-2] 
 22%|##2       | 56/252 [00:44<02:06,  1.55it/s]
[2025-07-31 11:06:18,184: WARNING/ForkPoolWorker-2] [A
[2025-07-31 11:06:18,257: WARNING/ForkPoolWorker-15] 
 85%|########5 | 215/252 [01:45<00:28,  1.30it/s]
[2025-07-31 11:06:18,257: WARNING/ForkPoolWorker-15] [A
[2025-07-31 11:06:18,315: WARNING/ForkPoolWorker-1] 
 26%|##6       | 66/252 [00:49<02:25,  1.28it/s]
[2025-07-31 11:06:18,322: WARNING/ForkPoolWorker-1] [A
[2025-07-31 11:06:18,859: WARNING/ForkPoolWorker-1] 
 27%|##6       | 67/252 [00:50<02:09,  1.43it/s]
[2025-07-31 11:06:18,859: WARNING/ForkPoolWorker-1] [A
[2025-07-31 11:06:18,883: WARNING/ForkPoolWorker-16] 
 68%|######7   | 171/252 [01:37<00:56,  1.43it/s]
[2025-07-31 11:06:18,883: WARNING/ForkPoolWorker-16] [A
[2025-07-31 11:06:18,972: WARNING/ForkPoolWorker-15] 
 86%|########5 | 216/252 [01:46<00:27,  1.33it/s]
[2025-07-31 11:06:18,973: WARNING/ForkPoolWorker-15] [A
[2025-07-31 11:06:19,166: WARNING/ForkPoolWorker-2] 
 23%|##2       | 57/252 [00:45<02:25,  1.34it/s]
[2025-07-31 11:06:19,166: WARNING/ForkPoolWorker-2] [A
[2025-07-31 11:06:19,324: WARNING/ForkPoolWorker-1] 
 27%|##6       | 68/252 [00:50<01:57,  1.57it/s]
[2025-07-31 11:06:19,324: WARNING/ForkPoolWorker-1] [A
[2025-07-31 11:06:19,380: WARNING/ForkPoolWorker-15] 
 86%|########6 | 217/252 [01:46<00:22,  1.54it/s]
[2025-07-31 11:06:19,380: WARNING/ForkPoolWorker-15] [A
[2025-07-31 11:06:19,700: WARNING/ForkPoolWorker-2] 
 23%|##3       | 58/252 [00:45<02:12,  1.46it/s]
[2025-07-31 11:06:19,700: WARNING/ForkPoolWorker-2] [A
[2025-07-31 11:06:19,787: WARNING/ForkPoolWorker-16] 
 68%|######8   | 172/252 [01:38<01:00,  1.31it/s]
[2025-07-31 11:06:19,787: WARNING/ForkPoolWorker-16] [A
[2025-07-31 11:06:20,015: WARNING/ForkPoolWorker-15] 
 87%|########6 | 218/252 [01:47<00:21,  1.58it/s]
[2025-07-31 11:06:20,015: WARNING/ForkPoolWorker-15] [A
[2025-07-31 11:06:20,169: WARNING/ForkPoolWorker-1] 
 27%|##7       | 69/252 [00:51<02:08,  1.43it/s]
[2025-07-31 11:06:20,169: WARNING/ForkPoolWorker-1] [A
[2025-07-31 11:06:20,444: WARNING/ForkPoolWorker-16] 
 69%|######8   | 173/252 [01:38<00:57,  1.37it/s]
[2025-07-31 11:06:20,444: WARNING/ForkPoolWorker-16] [A
[2025-07-31 11:06:20,529: WARNING/ForkPoolWorker-2] 
 23%|##3       | 59/252 [00:46<02:20,  1.38it/s]
[2025-07-31 11:06:20,529: WARNING/ForkPoolWorker-2] [A
[2025-07-31 11:06:20,549: WARNING/ForkPoolWorker-15] 
 87%|########6 | 219/252 [01:47<00:20,  1.62it/s]
[2025-07-31 11:06:20,550: WARNING/ForkPoolWorker-15] [A
[2025-07-31 11:06:21,008: WARNING/ForkPoolWorker-1] 
 28%|##7       | 70/252 [00:52<02:15,  1.35it/s]
[2025-07-31 11:06:21,008: WARNING/ForkPoolWorker-1] [A
[2025-07-31 11:06:21,191: WARNING/ForkPoolWorker-16] 
 69%|######9   | 174/252 [01:39<00:57,  1.36it/s]
[2025-07-31 11:06:21,191: WARNING/ForkPoolWorker-16] [A
[2025-07-31 11:06:21,295: WARNING/ForkPoolWorker-15] 
 87%|########7 | 220/252 [01:48<00:20,  1.53it/s]
[2025-07-31 11:06:21,295: WARNING/ForkPoolWorker-15] [A
[2025-07-31 11:06:21,327: WARNING/ForkPoolWorker-2] 
 24%|##3       | 60/252 [00:47<02:23,  1.34it/s]
[2025-07-31 11:06:21,334: WARNING/ForkPoolWorker-2] [A
[2025-07-31 11:06:21,808: WARNING/ForkPoolWorker-15] 
 88%|########7 | 221/252 [01:48<00:18,  1.63it/s]
[2025-07-31 11:06:21,808: WARNING/ForkPoolWorker-15] [A
[2025-07-31 11:06:21,903: WARNING/ForkPoolWorker-1] 
 28%|##8       | 71/252 [00:53<02:22,  1.27it/s]
[2025-07-31 11:06:21,903: WARNING/ForkPoolWorker-1] [A
[2025-07-31 11:06:22,004: WARNING/ForkPoolWorker-16] 
 69%|######9   | 175/252 [01:40<00:58,  1.32it/s]
[2025-07-31 11:06:22,004: WARNING/ForkPoolWorker-16] [A
[2025-07-31 11:06:22,069: WARNING/ForkPoolWorker-2] 
 24%|##4       | 61/252 [00:48<02:22,  1.34it/s]
[2025-07-31 11:06:22,078: WARNING/ForkPoolWorker-2] [A
[2025-07-31 11:06:22,548: WARNING/ForkPoolWorker-1] 
 29%|##8       | 72/252 [00:54<02:14,  1.34it/s]
[2025-07-31 11:06:22,556: WARNING/ForkPoolWorker-15] 
 88%|########8 | 222/252 [01:49<00:19,  1.53it/s]
[2025-07-31 11:06:22,618: WARNING/ForkPoolWorker-1] [A
[2025-07-31 11:06:22,618: WARNING/ForkPoolWorker-15] [A
[2025-07-31 11:06:22,608: WARNING/ForkPoolWorker-16] 
 70%|######9   | 176/252 [01:41<00:54,  1.40it/s]
[2025-07-31 11:06:22,658: WARNING/ForkPoolWorker-16] [A
[2025-07-31 11:06:22,832: WARNING/ForkPoolWorker-2] 
 25%|##4       | 62/252 [00:48<02:22,  1.33it/s]
[2025-07-31 11:06:22,832: WARNING/ForkPoolWorker-2] [A
[2025-07-31 11:06:23,280: WARNING/ForkPoolWorker-16] 
 70%|#######   | 177/252 [01:41<00:52,  1.43it/s]
[2025-07-31 11:06:23,280: WARNING/ForkPoolWorker-16] [A
[2025-07-31 11:06:23,348: WARNING/ForkPoolWorker-1] 
 29%|##8       | 73/252 [00:55<02:16,  1.31it/s]
[2025-07-31 11:06:23,348: WARNING/ForkPoolWorker-1] [A
[2025-07-31 11:06:23,481: WARNING/ForkPoolWorker-15] 
 88%|########8 | 223/252 [01:50<00:21,  1.36it/s]
[2025-07-31 11:06:23,490: WARNING/ForkPoolWorker-15] [A
[2025-07-31 11:06:23,602: WARNING/ForkPoolWorker-2] 
 25%|##5       | 63/252 [00:49<02:23,  1.32it/s]
[2025-07-31 11:06:23,618: WARNING/ForkPoolWorker-2] [A
[2025-07-31 11:06:24,047: WARNING/ForkPoolWorker-1] 
 29%|##9       | 74/252 [00:55<02:12,  1.35it/s]
[2025-07-31 11:06:24,047: WARNING/ForkPoolWorker-1] [A
[2025-07-31 11:06:24,098: WARNING/ForkPoolWorker-2] 
 25%|##5       | 64/252 [00:50<02:07,  1.47it/s]
[2025-07-31 11:06:24,098: WARNING/ForkPoolWorker-2] [A
[2025-07-31 11:06:24,240: WARNING/ForkPoolWorker-15] 
 89%|########8 | 224/252 [01:51<00:20,  1.35it/s]
[2025-07-31 11:06:24,240: WARNING/ForkPoolWorker-15] [A
[2025-07-31 11:06:24,287: WARNING/ForkPoolWorker-16] 
 71%|#######   | 178/252 [01:42<00:58,  1.26it/s]
[2025-07-31 11:06:24,288: WARNING/ForkPoolWorker-16] [A
[2025-07-31 11:06:24,635: WARNING/ForkPoolWorker-16] 
 71%|#######1  | 179/252 [01:43<00:48,  1.52it/s]
[2025-07-31 11:06:24,645: WARNING/ForkPoolWorker-16] [A
[2025-07-31 11:06:24,724: WARNING/ForkPoolWorker-1] 
 30%|##9       | 75/252 [00:56<02:07,  1.38it/s]
[2025-07-31 11:06:24,724: WARNING/ForkPoolWorker-1] [A
[2025-07-31 11:06:24,913: WARNING/ForkPoolWorker-16] 
 71%|#######1  | 180/252 [01:43<00:39,  1.84it/s]
[2025-07-31 11:06:24,960: WARNING/ForkPoolWorker-16] [A
[2025-07-31 11:06:24,960: WARNING/ForkPoolWorker-2] 
 26%|##5       | 65/252 [00:50<02:17,  1.36it/s]
[2025-07-31 11:06:24,970: WARNING/ForkPoolWorker-2] [A
[2025-07-31 11:06:25,068: WARNING/ForkPoolWorker-15] 
 89%|########9 | 225/252 [01:52<00:20,  1.30it/s]
[2025-07-31 11:06:25,068: WARNING/ForkPoolWorker-15] [A
[2025-07-31 11:06:25,517: WARNING/ForkPoolWorker-1] 
 30%|###       | 76/252 [00:57<02:10,  1.34it/s]
[2025-07-31 11:06:25,542: WARNING/ForkPoolWorker-1] [A
[2025-07-31 11:06:25,717: WARNING/ForkPoolWorker-16] 
 72%|#######1  | 181/252 [01:44<00:44,  1.61it/s]
[2025-07-31 11:06:25,717: WARNING/ForkPoolWorker-16] [A
[2025-07-31 11:06:25,796: WARNING/ForkPoolWorker-2] 
 26%|##6       | 66/252 [00:51<02:22,  1.31it/s]
[2025-07-31 11:06:25,797: WARNING/ForkPoolWorker-2] [A
[2025-07-31 11:06:25,937: WARNING/ForkPoolWorker-15] 
 90%|########9 | 226/252 [01:53<00:20,  1.25it/s]
[2025-07-31 11:06:25,958: WARNING/ForkPoolWorker-15] [A
[2025-07-31 11:06:26,137: WARNING/ForkPoolWorker-1] 
 31%|###       | 77/252 [00:57<02:03,  1.41it/s]
[2025-07-31 11:06:26,174: WARNING/ForkPoolWorker-1] [A
[2025-07-31 11:06:26,391: WARNING/ForkPoolWorker-2] 
 27%|##6       | 67/252 [00:52<02:12,  1.40it/s]
[2025-07-31 11:06:26,392: WARNING/ForkPoolWorker-2] [A
[2025-07-31 11:06:26,496: WARNING/ForkPoolWorker-15] 
 90%|######### | 227/252 [01:53<00:18,  1.38it/s]
[2025-07-31 11:06:26,496: WARNING/ForkPoolWorker-15] [A
[2025-07-31 11:06:26,748: WARNING/ForkPoolWorker-16] 
 72%|#######2  | 182/252 [01:45<00:52,  1.34it/s]
[2025-07-31 11:06:26,748: WARNING/ForkPoolWorker-16] [A
[2025-07-31 11:06:26,868: WARNING/ForkPoolWorker-1] 
 31%|###       | 78/252 [00:58<02:04,  1.40it/s]
[2025-07-31 11:06:26,878: WARNING/ForkPoolWorker-1] [A
[2025-07-31 11:06:27,241: WARNING/ForkPoolWorker-15] 
 90%|######### | 228/252 [01:54<00:17,  1.37it/s]
[2025-07-31 11:06:27,282: WARNING/ForkPoolWorker-15] [A
[2025-07-31 11:06:27,276: WARNING/ForkPoolWorker-16] 
 73%|#######2  | 183/252 [01:45<00:46,  1.47it/s]
[2025-07-31 11:06:27,286: WARNING/ForkPoolWorker-16] [A
[2025-07-31 11:06:27,368: WARNING/ForkPoolWorker-2] 
 27%|##6       | 68/252 [00:53<02:25,  1.26it/s]
[2025-07-31 11:06:27,390: WARNING/ForkPoolWorker-2] [A
[2025-07-31 11:06:27,481: WARNING/ForkPoolWorker-1] 
 31%|###1      | 79/252 [00:59<01:58,  1.46it/s]
[2025-07-31 11:06:27,494: WARNING/ForkPoolWorker-1] [A
[2025-07-31 11:06:27,885: WARNING/ForkPoolWorker-15] 
 91%|######### | 229/252 [01:55<00:16,  1.42it/s]
[2025-07-31 11:06:27,885: WARNING/ForkPoolWorker-15] [A
[2025-07-31 11:06:28,069: WARNING/ForkPoolWorker-2] 
 27%|##7       | 69/252 [00:54<02:20,  1.31it/s]
[2025-07-31 11:06:28,069: WARNING/ForkPoolWorker-2] [A
[2025-07-31 11:06:28,164: WARNING/ForkPoolWorker-16] 
 73%|#######3  | 184/252 [01:46<00:50,  1.35it/s]
[2025-07-31 11:06:28,164: WARNING/ForkPoolWorker-16] [A
[2025-07-31 11:06:28,532: WARNING/ForkPoolWorker-1] 
 32%|###1      | 80/252 [01:00<02:16,  1.26it/s]
[2025-07-31 11:06:28,562: WARNING/ForkPoolWorker-1] [A
[2025-07-31 11:06:28,552: WARNING/ForkPoolWorker-15] 
 91%|#########1| 230/252 [01:55<00:15,  1.44it/s]
[2025-07-31 11:06:28,570: WARNING/ForkPoolWorker-15] [A
[2025-07-31 11:06:28,978: WARNING/ForkPoolWorker-2] 
 28%|##7       | 70/252 [00:54<02:27,  1.24it/s]
[2025-07-31 11:06:28,994: WARNING/ForkPoolWorker-2] [A
[2025-07-31 11:06:29,192: WARNING/ForkPoolWorker-1] 
 32%|###2      | 81/252 [01:00<02:08,  1.33it/s]
[2025-07-31 11:06:29,201: WARNING/ForkPoolWorker-1] [A
[2025-07-31 11:06:29,311: WARNING/ForkPoolWorker-16] 
 73%|#######3  | 185/252 [01:47<00:57,  1.16it/s]
[2025-07-31 11:06:29,312: WARNING/ForkPoolWorker-16] [A
[2025-07-31 11:06:29,505: WARNING/ForkPoolWorker-15] 
 92%|#########1| 231/252 [01:56<00:16,  1.30it/s]
[2025-07-31 11:06:29,505: WARNING/ForkPoolWorker-15] [A
[2025-07-31 11:06:29,685: WARNING/ForkPoolWorker-1] 
 33%|###2      | 82/252 [01:01<01:54,  1.48it/s]
[2025-07-31 11:06:29,685: WARNING/ForkPoolWorker-1] [A
[2025-07-31 11:06:29,777: WARNING/ForkPoolWorker-2] 
 28%|##8       | 71/252 [00:55<02:25,  1.24it/s]
[2025-07-31 11:06:29,777: WARNING/ForkPoolWorker-2] [A
[2025-07-31 11:06:30,000: WARNING/ForkPoolWorker-16] 
 74%|#######3  | 186/252 [01:48<00:53,  1.24it/s]
[2025-07-31 11:06:30,000: WARNING/ForkPoolWorker-16] [A
[2025-07-31 11:06:30,357: WARNING/ForkPoolWorker-1] 
 33%|###2      | 83/252 [01:02<01:54,  1.48it/s]
[2025-07-31 11:06:30,357: WARNING/ForkPoolWorker-1] [A
[2025-07-31 11:06:30,444: WARNING/ForkPoolWorker-15] 
 92%|#########2| 232/252 [01:57<00:16,  1.22it/s]
[2025-07-31 11:06:30,445: WARNING/ForkPoolWorker-15] [A
[2025-07-31 11:06:30,591: WARNING/ForkPoolWorker-2] 
 29%|##8       | 72/252 [00:56<02:25,  1.24it/s]
[2025-07-31 11:06:30,592: WARNING/ForkPoolWorker-2] [A
[2025-07-31 11:06:30,840: WARNING/ForkPoolWorker-1] 
 33%|###3      | 84/252 [01:02<01:43,  1.62it/s]
[2025-07-31 11:06:30,847: WARNING/ForkPoolWorker-1] [A
[2025-07-31 11:06:30,901: WARNING/ForkPoolWorker-16] 
 74%|#######4  | 187/252 [01:49<00:54,  1.19it/s]
[2025-07-31 11:06:30,902: WARNING/ForkPoolWorker-16] [A
[2025-07-31 11:06:31,236: WARNING/ForkPoolWorker-15] 
 92%|#########2| 233/252 [01:58<00:15,  1.23it/s]
[2025-07-31 11:06:31,236: WARNING/ForkPoolWorker-15] [A
[2025-07-31 11:06:31,376: WARNING/ForkPoolWorker-2] 
 29%|##8       | 73/252 [00:57<02:23,  1.25it/s]
[2025-07-31 11:06:31,376: WARNING/ForkPoolWorker-2] [A
[2025-07-31 11:06:31,437: WARNING/ForkPoolWorker-1] 
 34%|###3      | 85/252 [01:03<01:42,  1.64it/s]
[2025-07-31 11:06:31,437: WARNING/ForkPoolWorker-1] [A
[2025-07-31 11:06:31,440: WARNING/ForkPoolWorker-16] 
 75%|#######4  | 188/252 [01:49<00:48,  1.33it/s]
[2025-07-31 11:06:31,440: WARNING/ForkPoolWorker-16] [A
[2025-07-31 11:06:32,099: WARNING/ForkPoolWorker-16] 
 75%|#######5  | 189/252 [01:50<00:45,  1.38it/s]
[2025-07-31 11:06:32,100: WARNING/ForkPoolWorker-16] [A
[2025-07-31 11:06:32,148: WARNING/ForkPoolWorker-15] 
 93%|#########2| 234/252 [01:59<00:15,  1.19it/s]
[2025-07-31 11:06:32,148: WARNING/ForkPoolWorker-15] [A
[2025-07-31 11:06:32,240: WARNING/ForkPoolWorker-1] 
 34%|###4      | 86/252 [01:03<01:50,  1.50it/s]
[2025-07-31 11:06:32,240: WARNING/ForkPoolWorker-1] [A
[2025-07-31 11:06:32,250: WARNING/ForkPoolWorker-2] 
 29%|##9       | 74/252 [00:58<02:26,  1.22it/s]
[2025-07-31 11:06:32,250: WARNING/ForkPoolWorker-2] [A
[2025-07-31 11:06:32,588: WARNING/ForkPoolWorker-16] 
 75%|#######5  | 190/252 [01:50<00:40,  1.53it/s]
[2025-07-31 11:06:32,588: WARNING/ForkPoolWorker-16] [A
[2025-07-31 11:06:32,880: WARNING/ForkPoolWorker-1] 
 35%|###4      | 87/252 [01:04<01:48,  1.52it/s]
[2025-07-31 11:06:32,880: WARNING/ForkPoolWorker-1] [A
[2025-07-31 11:06:33,149: WARNING/ForkPoolWorker-2] 
 30%|##9       | 75/252 [00:59<02:29,  1.18it/s]
[2025-07-31 11:06:33,149: WARNING/ForkPoolWorker-2] [A
[2025-07-31 11:06:33,220: WARNING/ForkPoolWorker-15] 
 93%|#########3| 235/252 [02:00<00:15,  1.10it/s]
[2025-07-31 11:06:33,221: WARNING/ForkPoolWorker-15] [A
[2025-07-31 11:06:33,416: WARNING/ForkPoolWorker-16] 
 76%|#######5  | 191/252 [01:51<00:43,  1.42it/s]
[2025-07-31 11:06:33,416: WARNING/ForkPoolWorker-16] [A
[2025-07-31 11:06:33,522: WARNING/ForkPoolWorker-1] 
 35%|###4      | 88/252 [01:05<01:47,  1.53it/s]
[2025-07-31 11:06:33,530: WARNING/ForkPoolWorker-1] [A
[2025-07-31 11:06:33,717: WARNING/ForkPoolWorker-2] 
 30%|###       | 76/252 [00:59<02:14,  1.31it/s]
[2025-07-31 11:06:33,717: WARNING/ForkPoolWorker-2] [A
[2025-07-31 11:06:33,886: WARNING/ForkPoolWorker-16] 
 76%|#######6  | 192/252 [01:52<00:38,  1.58it/s]
[2025-07-31 11:06:33,914: WARNING/ForkPoolWorker-16] [A
[2025-07-31 11:06:34,045: WARNING/ForkPoolWorker-15] 
 94%|#########3| 236/252 [02:01<00:14,  1.13it/s]
[2025-07-31 11:06:34,045: WARNING/ForkPoolWorker-15] [A
[2025-07-31 11:06:34,080: WARNING/ForkPoolWorker-1] 
 35%|###5      | 89/252 [01:05<01:41,  1.60it/s]
[2025-07-31 11:06:34,080: WARNING/ForkPoolWorker-1] [A
[2025-07-31 11:06:34,308: WARNING/ForkPoolWorker-2] 
 31%|###       | 77/252 [01:00<02:04,  1.41it/s]
[2025-07-31 11:06:34,308: WARNING/ForkPoolWorker-2] [A
[2025-07-31 11:06:34,488: WARNING/ForkPoolWorker-16] 
 77%|#######6  | 193/252 [01:52<00:36,  1.60it/s]
[2025-07-31 11:06:34,488: WARNING/ForkPoolWorker-16] [A
[2025-07-31 11:06:34,927: WARNING/ForkPoolWorker-15] 
 94%|#########4| 237/252 [02:02<00:13,  1.13it/s]
[2025-07-31 11:06:34,928: WARNING/ForkPoolWorker-15] [A
[2025-07-31 11:06:34,960: WARNING/ForkPoolWorker-1] 
 36%|###5      | 90/252 [01:06<01:53,  1.42it/s]
[2025-07-31 11:06:34,960: WARNING/ForkPoolWorker-1] [A
[2025-07-31 11:06:35,168: WARNING/ForkPoolWorker-16] 
 77%|#######6  | 194/252 [01:53<00:37,  1.56it/s]
[2025-07-31 11:06:35,186: WARNING/ForkPoolWorker-16] [A
[2025-07-31 11:06:35,276: WARNING/ForkPoolWorker-2] 
 31%|###       | 78/252 [01:01<02:17,  1.27it/s]
[2025-07-31 11:06:35,277: WARNING/ForkPoolWorker-2] [A
[2025-07-31 11:06:35,524: WARNING/ForkPoolWorker-15] 
 94%|#########4| 238/252 [02:02<00:11,  1.25it/s]
[2025-07-31 11:06:35,524: WARNING/ForkPoolWorker-15] [A
[2025-07-31 11:06:35,778: WARNING/ForkPoolWorker-1] 
 36%|###6      | 91/252 [01:07<01:57,  1.36it/s]
[2025-07-31 11:06:35,778: WARNING/ForkPoolWorker-1] [A
[2025-07-31 11:06:35,975: WARNING/ForkPoolWorker-2] 
 31%|###1      | 79/252 [01:01<02:11,  1.31it/s]
[2025-07-31 11:06:35,982: WARNING/ForkPoolWorker-2] [A
[2025-07-31 11:06:36,062: WARNING/ForkPoolWorker-16] 
 77%|#######7  | 195/252 [01:54<00:39,  1.43it/s]
[2025-07-31 11:06:36,062: WARNING/ForkPoolWorker-16] [A
[2025-07-31 11:06:36,176: WARNING/ForkPoolWorker-15] 
 95%|#########4| 239/252 [02:03<00:09,  1.33it/s]
[2025-07-31 11:06:36,176: WARNING/ForkPoolWorker-15] [A
[2025-07-31 11:06:36,292: WARNING/ForkPoolWorker-1] 
 37%|###6      | 92/252 [01:07<01:47,  1.49it/s]
[2025-07-31 11:06:36,298: WARNING/ForkPoolWorker-1] [A
[2025-07-31 11:06:36,716: WARNING/ForkPoolWorker-1] 
 37%|###6      | 93/252 [01:08<01:34,  1.68it/s]
[2025-07-31 11:06:36,734: WARNING/ForkPoolWorker-1] [A
[2025-07-31 11:06:36,720: WARNING/ForkPoolWorker-16] 
 78%|#######7  | 196/252 [01:55<00:39,  1.42it/s]
[2025-07-31 11:06:36,736: WARNING/ForkPoolWorker-16] [A
[2025-07-31 11:06:36,720: WARNING/ForkPoolWorker-15] 
 95%|#########5| 240/252 [02:03<00:08,  1.45it/s]
[2025-07-31 11:06:36,738: WARNING/ForkPoolWorker-15] [A
[2025-07-31 11:06:36,869: WARNING/ForkPoolWorker-2] 
 32%|###1      | 80/252 [01:02<02:17,  1.25it/s]
[2025-07-31 11:06:36,869: WARNING/ForkPoolWorker-2] [A
[2025-07-31 11:06:37,436: WARNING/ForkPoolWorker-16] 
 78%|#######8  | 197/252 [01:55<00:38,  1.41it/s]
[2025-07-31 11:06:37,436: WARNING/ForkPoolWorker-16] [A
[2025-07-31 11:06:37,512: WARNING/ForkPoolWorker-1] 
 37%|###7      | 94/252 [01:09<01:43,  1.52it/s]
[2025-07-31 11:06:37,512: WARNING/ForkPoolWorker-1] [A
[2025-07-31 11:06:37,526: WARNING/ForkPoolWorker-15] 
 96%|#########5| 241/252 [02:04<00:07,  1.38it/s]
[2025-07-31 11:06:37,534: WARNING/ForkPoolWorker-15] [A
[2025-07-31 11:06:37,726: WARNING/ForkPoolWorker-2] 
 32%|###2      | 81/252 [01:03<02:19,  1.22it/s]
[2025-07-31 11:06:37,726: WARNING/ForkPoolWorker-2] [A
[2025-07-31 11:06:38,014: WARNING/ForkPoolWorker-16] 
 79%|#######8  | 198/252 [01:56<00:36,  1.50it/s]
[2025-07-31 11:06:38,042: WARNING/ForkPoolWorker-16] [A
[2025-07-31 11:06:38,208: WARNING/ForkPoolWorker-15] 
 96%|#########6| 242/252 [02:05<00:07,  1.40it/s]
[2025-07-31 11:06:38,208: WARNING/ForkPoolWorker-15] [A
[2025-07-31 11:06:38,388: WARNING/ForkPoolWorker-1] 
 38%|###7      | 95/252 [01:10<01:53,  1.38it/s]
[2025-07-31 11:06:38,388: WARNING/ForkPoolWorker-1] [A
[2025-07-31 11:06:38,555: WARNING/ForkPoolWorker-2] 
 33%|###2      | 82/252 [01:04<02:19,  1.22it/s]
[2025-07-31 11:06:38,555: WARNING/ForkPoolWorker-2] [A
[2025-07-31 11:06:38,704: WARNING/ForkPoolWorker-16] 
 79%|#######8  | 199/252 [01:57<00:35,  1.48it/s]
[2025-07-31 11:06:38,704: WARNING/ForkPoolWorker-16] [A
[2025-07-31 11:06:38,896: WARNING/ForkPoolWorker-15] 
 96%|#########6| 243/252 [02:06<00:06,  1.42it/s]
[2025-07-31 11:06:38,896: WARNING/ForkPoolWorker-15] [A
[2025-07-31 11:06:39,256: WARNING/ForkPoolWorker-2] 
 33%|###2      | 83/252 [01:05<02:12,  1.27it/s]
[2025-07-31 11:06:39,256: WARNING/ForkPoolWorker-2] [A
[2025-07-31 11:06:39,348: WARNING/ForkPoolWorker-15] 
 97%|#########6| 244/252 [02:06<00:05,  1.59it/s]
[2025-07-31 11:06:39,348: WARNING/ForkPoolWorker-15] [A
[2025-07-31 11:06:39,368: WARNING/ForkPoolWorker-16] 
 79%|#######9  | 200/252 [01:57<00:34,  1.49it/s]
[2025-07-31 11:06:39,368: WARNING/ForkPoolWorker-16] [A
[2025-07-31 11:06:39,399: WARNING/ForkPoolWorker-1] 
 38%|###8      | 96/252 [01:11<02:06,  1.24it/s]
[2025-07-31 11:06:39,400: WARNING/ForkPoolWorker-1] [A
[2025-07-31 11:06:40,048: WARNING/ForkPoolWorker-15] 
 97%|#########7| 245/252 [02:07<00:04,  1.54it/s]
[2025-07-31 11:06:40,048: WARNING/ForkPoolWorker-15] [A
[2025-07-31 11:06:40,168: WARNING/ForkPoolWorker-2] 
 33%|###3      | 84/252 [01:06<02:18,  1.21it/s]
[2025-07-31 11:06:40,169: WARNING/ForkPoolWorker-2] [A
[2025-07-31 11:06:40,303: WARNING/ForkPoolWorker-16] 
 80%|#######9  | 201/252 [01:58<00:38,  1.33it/s]
[2025-07-31 11:06:40,310: WARNING/ForkPoolWorker-16] [A
[2025-07-31 11:06:40,513: WARNING/ForkPoolWorker-1] 
 38%|###8      | 97/252 [01:12<02:19,  1.11it/s]
[2025-07-31 11:06:40,513: WARNING/ForkPoolWorker-1] [A
[2025-07-31 11:06:40,893: WARNING/ForkPoolWorker-2] 
 34%|###3      | 85/252 [01:06<02:12,  1.26it/s]
[2025-07-31 11:06:40,893: WARNING/ForkPoolWorker-2] [A
[2025-07-31 11:06:40,900: WARNING/ForkPoolWorker-15] 
 98%|#########7| 246/252 [02:08<00:04,  1.41it/s]
[2025-07-31 11:06:40,900: WARNING/ForkPoolWorker-15] [A
[2025-07-31 11:06:41,058: WARNING/ForkPoolWorker-16] 
 80%|########  | 202/252 [01:59<00:36,  1.36it/s]
[2025-07-31 11:06:41,058: WARNING/ForkPoolWorker-16] [A
[2025-07-31 11:06:41,219: WARNING/ForkPoolWorker-1] 
 39%|###8      | 98/252 [01:12<02:09,  1.19it/s]
[2025-07-31 11:06:41,220: WARNING/ForkPoolWorker-1] [A
[2025-07-31 11:06:41,582: WARNING/ForkPoolWorker-15] 
 98%|#########8| 247/252 [02:08<00:03,  1.42it/s]
[2025-07-31 11:06:41,586: WARNING/ForkPoolWorker-15] [A
[2025-07-31 11:06:41,640: WARNING/ForkPoolWorker-2] 
 34%|###4      | 86/252 [01:07<02:09,  1.28it/s]
[2025-07-31 11:06:41,646: WARNING/ForkPoolWorker-2] [A
[2025-07-31 11:06:41,731: WARNING/ForkPoolWorker-16] 
 81%|########  | 203/252 [02:00<00:35,  1.36it/s]
[2025-07-31 11:06:41,731: WARNING/ForkPoolWorker-16] [A
[2025-07-31 11:06:41,884: WARNING/ForkPoolWorker-1] 
 39%|###9      | 99/252 [01:13<02:00,  1.27it/s]
[2025-07-31 11:06:41,885: WARNING/ForkPoolWorker-1] [A
[2025-07-31 11:06:42,025: WARNING/ForkPoolWorker-15] 
 98%|#########8| 248/252 [02:09<00:02,  1.60it/s]
[2025-07-31 11:06:42,026: WARNING/ForkPoolWorker-15] [A
[2025-07-31 11:06:42,328: WARNING/ForkPoolWorker-15] 
 99%|#########8| 249/252 [02:09<00:01,  1.89it/s]
[2025-07-31 11:06:42,328: WARNING/ForkPoolWorker-15] [A
[2025-07-31 11:06:42,337: WARNING/ForkPoolWorker-16] 
 81%|########  | 204/252 [02:00<00:33,  1.44it/s]
[2025-07-31 11:06:42,337: WARNING/ForkPoolWorker-16] [A
[2025-07-31 11:06:42,356: WARNING/ForkPoolWorker-2] 
 35%|###4      | 87/252 [01:08<02:05,  1.31it/s]
[2025-07-31 11:06:42,370: WARNING/ForkPoolWorker-2] [A
[2025-07-31 11:06:42,557: WARNING/ForkPoolWorker-1] 
 40%|###9      | 100/252 [01:14<01:54,  1.33it/s]
[2025-07-31 11:06:42,557: WARNING/ForkPoolWorker-1] [A
[2025-07-31 11:06:43,118: WARNING/ForkPoolWorker-15] 
 99%|#########9| 250/252 [02:10<00:01,  1.65it/s]
[2025-07-31 11:06:43,118: WARNING/ForkPoolWorker-15] [A
[2025-07-31 11:06:43,163: WARNING/ForkPoolWorker-2] 
 35%|###4      | 88/252 [01:09<02:07,  1.29it/s]
[2025-07-31 11:06:43,163: WARNING/ForkPoolWorker-2] [A
[2025-07-31 11:06:43,290: WARNING/ForkPoolWorker-16] 
 81%|########1 | 205/252 [02:01<00:36,  1.29it/s]
[2025-07-31 11:06:43,326: WARNING/ForkPoolWorker-16] [A
[2025-07-31 11:06:43,559: WARNING/ForkPoolWorker-1] 
 40%|####      | 101/252 [01:15<02:05,  1.21it/s]
[2025-07-31 11:06:43,559: WARNING/ForkPoolWorker-1] [A
[2025-07-31 11:06:43,929: WARNING/ForkPoolWorker-2] 
 35%|###5      | 89/252 [01:09<02:05,  1.30it/s]
[2025-07-31 11:06:43,966: WARNING/ForkPoolWorker-2] [A
[2025-07-31 11:06:43,932: WARNING/ForkPoolWorker-15] 
100%|#########9| 251/252 [02:11<00:00,  1.50it/s]
[2025-07-31 11:06:43,974: WARNING/ForkPoolWorker-15] [A
[2025-07-31 11:06:44,093: WARNING/ForkPoolWorker-16] 
 82%|########1 | 206/252 [02:02<00:35,  1.28it/s]
[2025-07-31 11:06:44,102: WARNING/ForkPoolWorker-16] [A
[2025-07-31 11:06:44,300: WARNING/ForkPoolWorker-1] 
 40%|####      | 102/252 [01:15<02:00,  1.25it/s]
[2025-07-31 11:06:44,310: WARNING/ForkPoolWorker-1] [A
[2025-07-31 11:06:44,982: WARNING/ForkPoolWorker-1] 
 41%|####      | 103/252 [01:16<01:54,  1.31it/s]
[2025-07-31 11:06:45,006: WARNING/ForkPoolWorker-1] [A
[2025-07-31 11:06:45,096: WARNING/ForkPoolWorker-15] 
100%|##########| 252/252 [02:12<00:00,  1.22it/s]
[2025-07-31 11:06:45,096: WARNING/ForkPoolWorker-15] [A
[2025-07-31 11:06:45,096: WARNING/ForkPoolWorker-15] 
100%|##########| 252/252 [02:12<00:00,  1.91it/s]
[2025-07-31 11:06:45,097: WARNING/ForkPoolWorker-2] 
 36%|###5      | 90/252 [01:11<02:24,  1.12it/s]
[2025-07-31 11:06:45,097: WARNING/ForkPoolWorker-2] [A
[2025-07-31 11:06:45,353: WARNING/ForkPoolWorker-1] 
 41%|####1     | 104/252 [01:17<01:35,  1.54it/s]
[2025-07-31 11:06:45,353: WARNING/ForkPoolWorker-1] [A
[2025-07-31 11:06:45,558: WARNING/ForkPoolWorker-16] 
 82%|########2 | 207/252 [02:03<00:44,  1.02it/s]
[2025-07-31 11:06:45,558: WARNING/ForkPoolWorker-16] [A
[2025-07-31 11:06:45,812: WARNING/ForkPoolWorker-1] 
 42%|####1     | 105/252 [01:17<01:26,  1.69it/s]
[2025-07-31 11:06:45,822: WARNING/ForkPoolWorker-1] [A
[2025-07-31 11:06:45,852: WARNING/ForkPoolWorker-2] 
 36%|###6      | 91/252 [01:11<02:16,  1.18it/s]
[2025-07-31 11:06:45,852: WARNING/ForkPoolWorker-2] [A
[2025-07-31 11:06:46,100: WARNING/ForkPoolWorker-16] 
 83%|########2 | 208/252 [02:04<00:37,  1.17it/s]
[2025-07-31 11:06:46,100: WARNING/ForkPoolWorker-16] [A
[2025-07-31 11:06:46,308: WARNING/ForkPoolWorker-2] 
 37%|###6      | 92/252 [01:12<01:57,  1.37it/s]
[2025-07-31 11:06:46,308: WARNING/ForkPoolWorker-2] [A
[2025-07-31 11:06:46,592: WARNING/ForkPoolWorker-16] 
 83%|########2 | 209/252 [02:04<00:32,  1.34it/s]
[2025-07-31 11:06:46,592: WARNING/ForkPoolWorker-16] [A
[2025-07-31 11:06:46,750: WARNING/ForkPoolWorker-1] 
 42%|####2     | 106/252 [01:18<01:41,  1.44it/s]
[2025-07-31 11:06:46,750: WARNING/ForkPoolWorker-1] [A
[2025-07-31 11:06:47,050: WARNING/ForkPoolWorker-1] 
 42%|####2     | 107/252 [01:18<01:23,  1.73it/s]
[2025-07-31 11:06:47,050: WARNING/ForkPoolWorker-1] [A
[2025-07-31 11:06:47,122: WARNING/ForkPoolWorker-2] 
 37%|###6      | 93/252 [01:13<02:00,  1.32it/s]
[2025-07-31 11:06:47,122: WARNING/ForkPoolWorker-2] [A
[2025-07-31 11:06:47,153: WARNING/ForkPoolWorker-16] 
 83%|########3 | 210/252 [02:05<00:28,  1.45it/s]
[2025-07-31 11:06:47,154: WARNING/ForkPoolWorker-16] [A
[2025-07-31 11:06:47,647: WARNING/ForkPoolWorker-1] 
 43%|####2     | 108/252 [01:19<01:23,  1.72it/s]
[2025-07-31 11:06:47,647: WARNING/ForkPoolWorker-1] [A
[2025-07-31 11:06:47,872: WARNING/ForkPoolWorker-16] 
 84%|########3 | 211/252 [02:06<00:28,  1.43it/s]
[2025-07-31 11:06:47,872: WARNING/ForkPoolWorker-16] [A
[2025-07-31 11:06:47,908: WARNING/ForkPoolWorker-2] 
 37%|###7      | 94/252 [01:13<02:00,  1.31it/s]
[2025-07-31 11:06:47,914: WARNING/ForkPoolWorker-2] [A
[2025-07-31 11:06:48,219: WARNING/ForkPoolWorker-1] 
 43%|####3     | 109/252 [01:19<01:22,  1.73it/s]
[2025-07-31 11:06:48,226: WARNING/ForkPoolWorker-1] [A
[2025-07-31 11:06:48,552: WARNING/ForkPoolWorker-2] 
 38%|###7      | 95/252 [01:14<01:54,  1.37it/s]
[2025-07-31 11:06:48,553: WARNING/ForkPoolWorker-2] [A
[2025-07-31 11:06:48,649: WARNING/ForkPoolWorker-16] 
 84%|########4 | 212/252 [02:07<00:28,  1.38it/s]
[2025-07-31 11:06:48,650: WARNING/ForkPoolWorker-16] [A
[2025-07-31 11:06:48,748: WARNING/ForkPoolWorker-1] 
 44%|####3     | 110/252 [01:20<01:20,  1.77it/s]
[2025-07-31 11:06:48,748: WARNING/ForkPoolWorker-1] [A
[2025-07-31 11:06:49,237: WARNING/ForkPoolWorker-2] 
 38%|###8      | 96/252 [01:15<01:51,  1.40it/s]
[2025-07-31 11:06:49,250: WARNING/ForkPoolWorker-2] [A
[2025-07-31 11:06:49,422: WARNING/ForkPoolWorker-1] 
 44%|####4     | 111/252 [01:21<01:24,  1.67it/s]
[2025-07-31 11:06:49,430: WARNING/ForkPoolWorker-1] [A
[2025-07-31 11:06:49,492: WARNING/ForkPoolWorker-16] 
 85%|########4 | 213/252 [02:07<00:29,  1.32it/s]
[2025-07-31 11:06:49,506: WARNING/ForkPoolWorker-16] [A
[2025-07-31 11:06:49,912: WARNING/ForkPoolWorker-1] 
 44%|####4     | 112/252 [01:21<01:19,  1.77it/s]
[2025-07-31 11:06:49,912: WARNING/ForkPoolWorker-1] [A
[2025-07-31 11:06:50,080: WARNING/ForkPoolWorker-16] 
 85%|########4 | 214/252 [02:08<00:26,  1.41it/s]
[2025-07-31 11:06:50,102: WARNING/ForkPoolWorker-16] [A
[2025-07-31 11:06:50,265: WARNING/ForkPoolWorker-2] 
 38%|###8      | 97/252 [01:16<02:05,  1.24it/s]
[2025-07-31 11:06:50,265: WARNING/ForkPoolWorker-2] [A
[2025-07-31 11:06:50,664: WARNING/ForkPoolWorker-1] 
 45%|####4     | 113/252 [01:22<01:26,  1.61it/s]
[2025-07-31 11:06:50,664: WARNING/ForkPoolWorker-1] [A
[2025-07-31 11:06:50,737: WARNING/ForkPoolWorker-16] 
 85%|########5 | 215/252 [02:09<00:25,  1.44it/s]
[2025-07-31 11:06:50,737: WARNING/ForkPoolWorker-16] [A
[2025-07-31 11:06:51,049: WARNING/ForkPoolWorker-2] 
 39%|###8      | 98/252 [01:17<02:03,  1.25it/s]
[2025-07-31 11:06:51,049: WARNING/ForkPoolWorker-2] [A
[2025-07-31 11:06:51,264: WARNING/ForkPoolWorker-1] 
 45%|####5     | 114/252 [01:22<01:24,  1.63it/s]
[2025-07-31 11:06:51,286: WARNING/ForkPoolWorker-1] [A
[2025-07-31 11:06:51,520: WARNING/ForkPoolWorker-16] 
 86%|########5 | 216/252 [02:09<00:25,  1.39it/s]
[2025-07-31 11:06:51,538: WARNING/ForkPoolWorker-16] [A
[2025-07-31 11:06:51,729: WARNING/ForkPoolWorker-2] 
 39%|###9      | 99/252 [01:17<01:57,  1.31it/s]
[2025-07-31 11:06:51,734: WARNING/ForkPoolWorker-2] [A
[2025-07-31 11:06:52,014: WARNING/ForkPoolWorker-1] 
 46%|####5     | 115/252 [01:23<01:29,  1.53it/s]
[2025-07-31 11:06:52,015: WARNING/ForkPoolWorker-1] [A
[2025-07-31 11:06:52,193: WARNING/ForkPoolWorker-16] 
 86%|########6 | 217/252 [02:10<00:24,  1.42it/s]
[2025-07-31 11:06:52,193: WARNING/ForkPoolWorker-16] [A
[2025-07-31 11:06:52,369: WARNING/ForkPoolWorker-2] 
 40%|###9      | 100/252 [01:18<01:50,  1.37it/s]
[2025-07-31 11:06:52,372: WARNING/ForkPoolWorker-2] [A
[2025-07-31 11:06:52,700: WARNING/ForkPoolWorker-1] 
 46%|####6     | 116/252 [01:24<01:30,  1.50it/s]
[2025-07-31 11:06:52,700: WARNING/ForkPoolWorker-1] [A
[2025-07-31 11:06:52,844: WARNING/ForkPoolWorker-16] 
 87%|########6 | 218/252 [02:11<00:23,  1.45it/s]
[2025-07-31 11:06:52,850: WARNING/ForkPoolWorker-16] [A
[2025-07-31 11:06:52,958: WARNING/ForkPoolWorker-2] 
 40%|####      | 101/252 [01:18<01:43,  1.46it/s]
[2025-07-31 11:06:52,986: WARNING/ForkPoolWorker-2] [A
[2025-07-31 11:06:53,268: WARNING/ForkPoolWorker-1] 
 46%|####6     | 117/252 [01:24<01:25,  1.57it/s]
[2025-07-31 11:06:53,284: WARNING/ForkPoolWorker-1] [A
[2025-07-31 11:06:53,390: WARNING/ForkPoolWorker-15] 
  0%|          | 1/494 [02:20<19:14:51, 140.55s/it]
[2025-07-31 11:06:53,600: WARNING/ForkPoolWorker-2] 
 40%|####      | 102/252 [01:19<01:40,  1.49it/s]
[2025-07-31 11:06:53,606: WARNING/ForkPoolWorker-2] [A
[2025-07-31 11:06:53,744: WARNING/ForkPoolWorker-16] 
 87%|########6 | 219/252 [02:12<00:24,  1.33it/s]
[2025-07-31 11:06:53,744: WARNING/ForkPoolWorker-16] [A
[2025-07-31 11:06:54,167: WARNING/ForkPoolWorker-1] 
 47%|####6     | 118/252 [01:25<01:35,  1.40it/s]
[2025-07-31 11:06:54,167: WARNING/ForkPoolWorker-1] [A
[2025-07-31 11:06:54,236: WARNING/ForkPoolWorker-2] 
 41%|####      | 103/252 [01:20<01:38,  1.51it/s]
[2025-07-31 11:06:54,237: WARNING/ForkPoolWorker-2] [A
[2025-07-31 11:06:54,640: WARNING/ForkPoolWorker-1] 
 47%|####7     | 119/252 [01:26<01:25,  1.56it/s]
[2025-07-31 11:06:54,640: WARNING/ForkPoolWorker-1] [A
[2025-07-31 11:06:54,800: WARNING/ForkPoolWorker-16] 
 87%|########7 | 220/252 [02:13<00:26,  1.19it/s]
[2025-07-31 11:06:54,800: WARNING/ForkPoolWorker-16] [A
[2025-07-31 11:06:54,992: WARNING/ForkPoolWorker-2] 
 41%|####1     | 104/252 [01:21<01:42,  1.45it/s]
[2025-07-31 11:06:55,006: WARNING/ForkPoolWorker-2] [A
[2025-07-31 11:06:55,444: WARNING/ForkPoolWorker-2] 
 42%|####1     | 105/252 [01:21<01:30,  1.62it/s]
[2025-07-31 11:06:55,444: WARNING/ForkPoolWorker-2] [A
[2025-07-31 11:06:55,489: WARNING/ForkPoolWorker-1] 
 48%|####7     | 120/252 [01:27<01:32,  1.42it/s]
[2025-07-31 11:06:55,489: WARNING/ForkPoolWorker-16] 
 88%|########7 | 221/252 [02:13<00:24,  1.25it/s]
[2025-07-31 11:06:55,490: WARNING/ForkPoolWorker-1] [A
[2025-07-31 11:06:55,490: WARNING/ForkPoolWorker-16] [A
[2025-07-31 11:06:55,807: WARNING/ForkPoolWorker-2] 
 42%|####2     | 106/252 [01:21<01:19,  1.84it/s]
[2025-07-31 11:06:55,808: WARNING/ForkPoolWorker-2] [A
[2025-07-31 11:06:56,153: WARNING/ForkPoolWorker-16] 
 88%|########8 | 222/252 [02:14<00:22,  1.32it/s]
[2025-07-31 11:06:56,154: WARNING/ForkPoolWorker-16] [A
[2025-07-31 11:06:56,156: WARNING/ForkPoolWorker-1] 
 48%|####8     | 121/252 [01:27<01:30,  1.44it/s]
[2025-07-31 11:06:56,156: WARNING/ForkPoolWorker-1] [A
[2025-07-31 11:06:56,292: WARNING/ForkPoolWorker-2] 
 42%|####2     | 107/252 [01:22<01:16,  1.91it/s]
[2025-07-31 11:06:56,293: WARNING/ForkPoolWorker-2] [A
[2025-07-31 11:06:56,748: WARNING/ForkPoolWorker-16] 
 88%|########8 | 223/252 [02:15<00:20,  1.41it/s]
[2025-07-31 11:06:56,748: WARNING/ForkPoolWorker-16] [A
[2025-07-31 11:06:56,896: WARNING/ForkPoolWorker-1] 
 48%|####8     | 122/252 [01:28<01:31,  1.41it/s]
[2025-07-31 11:06:56,896: WARNING/ForkPoolWorker-1] [A
[2025-07-31 11:06:56,968: WARNING/ForkPoolWorker-2] 
 43%|####2     | 108/252 [01:22<01:22,  1.75it/s]
[2025-07-31 11:06:56,968: WARNING/ForkPoolWorker-2] [A
[2025-07-31 11:06:57,474: WARNING/ForkPoolWorker-1] 
 49%|####8     | 123/252 [01:29<01:26,  1.50it/s]
[2025-07-31 11:06:57,475: WARNING/ForkPoolWorker-1] [A
[2025-07-31 11:06:57,613: WARNING/ForkPoolWorker-16] 
 89%|########8 | 224/252 [02:16<00:21,  1.32it/s]
[2025-07-31 11:06:57,613: WARNING/ForkPoolWorker-16] [A
[2025-07-31 11:06:57,804: WARNING/ForkPoolWorker-2] 
 43%|####3     | 109/252 [01:23<01:32,  1.54it/s]
[2025-07-31 11:06:57,822: WARNING/ForkPoolWorker-2] [A
[2025-07-31 11:06:58,123: WARNING/ForkPoolWorker-1] 
 49%|####9     | 124/252 [01:29<01:24,  1.51it/s]
[2025-07-31 11:06:58,123: WARNING/ForkPoolWorker-1] [A
[2025-07-31 11:06:58,551: WARNING/ForkPoolWorker-16] 
 89%|########9 | 225/252 [02:16<00:21,  1.23it/s]
[2025-07-31 11:06:58,552: WARNING/ForkPoolWorker-16] [A
[2025-07-31 11:06:58,616: WARNING/ForkPoolWorker-1] 
 50%|####9     | 125/252 [01:30<01:17,  1.63it/s]
[2025-07-31 11:06:58,616: WARNING/ForkPoolWorker-1] [A
[2025-07-31 11:06:58,673: WARNING/ForkPoolWorker-2] 
 44%|####3     | 110/252 [01:24<01:41,  1.40it/s]
[2025-07-31 11:06:58,674: WARNING/ForkPoolWorker-2] [A
[2025-07-31 11:06:58,968: WARNING/ForkPoolWorker-1] 
 50%|#####     | 126/252 [01:30<01:07,  1.87it/s]
[2025-07-31 11:06:58,968: WARNING/ForkPoolWorker-1] [A
[2025-07-31 11:06:59,136: WARNING/ForkPoolWorker-16] 
 90%|########9 | 226/252 [02:17<00:19,  1.35it/s]
[2025-07-31 11:06:59,136: WARNING/ForkPoolWorker-16] [A
[2025-07-31 11:06:59,215: WARNING/ForkPoolWorker-2] 
 44%|####4     | 111/252 [01:25<01:33,  1.51it/s]
[2025-07-31 11:06:59,215: WARNING/ForkPoolWorker-2] [A
[2025-07-31 11:06:59,879: WARNING/ForkPoolWorker-2] 
 44%|####4     | 112/252 [01:25<01:32,  1.51it/s]
[2025-07-31 11:06:59,879: WARNING/ForkPoolWorker-2] [A
[2025-07-31 11:06:59,880: WARNING/ForkPoolWorker-1] 
 50%|#####     | 127/252 [01:31<01:20,  1.55it/s]
[2025-07-31 11:06:59,880: WARNING/ForkPoolWorker-1] [A
[2025-07-31 11:07:00,019: WARNING/ForkPoolWorker-16] 
 90%|######### | 227/252 [02:18<00:19,  1.27it/s]
[2025-07-31 11:07:00,019: WARNING/ForkPoolWorker-16] [A
[2025-07-31 11:07:00,504: WARNING/ForkPoolWorker-1] 
 51%|#####     | 128/252 [01:32<01:19,  1.56it/s]
[2025-07-31 11:07:00,504: WARNING/ForkPoolWorker-1] [A
[2025-07-31 11:07:00,842: WARNING/ForkPoolWorker-15] 
  0%|          | 2/494 [02:28<8:30:30, 62.26s/it]  
[2025-07-31 11:07:00,842: WARNING/ForkPoolWorker-16] 
 90%|######### | 228/252 [02:19<00:19,  1.26it/s]
[2025-07-31 11:07:00,843: WARNING/ForkPoolWorker-16] [A
[2025-07-31 11:07:00,993: WARNING/ForkPoolWorker-2] 
 45%|####4     | 113/252 [01:27<01:51,  1.25it/s]
[2025-07-31 11:07:00,993: WARNING/ForkPoolWorker-2] [A
[2025-07-31 11:07:01,096: WARNING/ForkPoolWorker-1] 
 51%|#####1    | 129/252 [01:32<01:16,  1.60it/s]
[2025-07-31 11:07:01,097: WARNING/ForkPoolWorker-1] [A
[2025-07-31 11:07:01,224: WARNING/ForkPoolWorker-16] 
 91%|######### | 229/252 [02:19<00:15,  1.49it/s]
[2025-07-31 11:07:01,224: WARNING/ForkPoolWorker-16] [A
[2025-07-31 11:07:01,692: WARNING/ForkPoolWorker-2] 
 45%|####5     | 114/252 [01:27<01:46,  1.30it/s]
[2025-07-31 11:07:01,694: WARNING/ForkPoolWorker-2] [A
[2025-07-31 11:07:01,824: WARNING/ForkPoolWorker-1] 
 52%|#####1    | 130/252 [01:33<01:20,  1.52it/s]
[2025-07-31 11:07:01,824: WARNING/ForkPoolWorker-1] [A
[2025-07-31 11:07:01,872: WARNING/ForkPoolWorker-16] 
 91%|#########1| 230/252 [02:20<00:14,  1.50it/s]
[2025-07-31 11:07:01,885: WARNING/ForkPoolWorker-16] [A
[2025-07-31 11:07:02,295: WARNING/ForkPoolWorker-16] 
 92%|#########1| 231/252 [02:20<00:12,  1.69it/s]
[2025-07-31 11:07:02,295: WARNING/ForkPoolWorker-16] [A
[2025-07-31 11:07:02,391: WARNING/ForkPoolWorker-2] 
 46%|####5     | 115/252 [01:28<01:42,  1.34it/s]
[2025-07-31 11:07:02,391: WARNING/ForkPoolWorker-2] [A
[2025-07-31 11:07:02,465: WARNING/ForkPoolWorker-1] 
 52%|#####1    | 131/252 [01:34<01:18,  1.53it/s]
[2025-07-31 11:07:02,482: WARNING/ForkPoolWorker-1] [A
[2025-07-31 11:07:02,884: WARNING/ForkPoolWorker-16] 
 92%|#########2| 232/252 [02:21<00:11,  1.69it/s]
[2025-07-31 11:07:02,884: WARNING/ForkPoolWorker-16] [A
[2025-07-31 11:07:03,009: WARNING/ForkPoolWorker-2] 
 46%|####6     | 116/252 [01:29<01:36,  1.41it/s]
[2025-07-31 11:07:03,034: WARNING/ForkPoolWorker-2] [A
[2025-07-31 11:07:03,137: WARNING/ForkPoolWorker-1] 
 52%|#####2    | 132/252 [01:34<01:18,  1.52it/s]
[2025-07-31 11:07:03,137: WARNING/ForkPoolWorker-1] [A
[2025-07-31 11:07:03,645: WARNING/ForkPoolWorker-16] 
 92%|#########2| 233/252 [02:22<00:12,  1.56it/s]
[2025-07-31 11:07:03,645: WARNING/ForkPoolWorker-16] [A
[2025-07-31 11:07:03,722: WARNING/ForkPoolWorker-2] 
 46%|####6     | 117/252 [01:29<01:34,  1.42it/s]
[2025-07-31 11:07:03,722: WARNING/ForkPoolWorker-2] [A
[2025-07-31 11:07:03,808: WARNING/ForkPoolWorker-1] 
 53%|#####2    | 133/252 [01:35<01:18,  1.51it/s]
[2025-07-31 11:07:03,808: WARNING/ForkPoolWorker-1] [A
[2025-07-31 11:07:04,264: WARNING/ForkPoolWorker-2] 
 47%|####6     | 118/252 [01:30<01:28,  1.51it/s]
[2025-07-31 11:07:04,264: WARNING/ForkPoolWorker-2] [A
[2025-07-31 11:07:04,342: WARNING/ForkPoolWorker-16] 
 93%|#########2| 234/252 [02:22<00:11,  1.52it/s]
[2025-07-31 11:07:04,343: WARNING/ForkPoolWorker-16] [A
[2025-07-31 11:07:04,400: WARNING/ForkPoolWorker-1] 
 53%|#####3    | 134/252 [01:36<01:15,  1.56it/s]
[2025-07-31 11:07:04,400: WARNING/ForkPoolWorker-1] [A
[2025-07-31 11:07:04,889: WARNING/ForkPoolWorker-16] 
 93%|#########3| 235/252 [02:23<00:10,  1.60it/s]
[2025-07-31 11:07:04,889: WARNING/ForkPoolWorker-16] [A
[2025-07-31 11:07:05,144: WARNING/ForkPoolWorker-1] 
 54%|#####3    | 135/252 [01:36<01:18,  1.49it/s]
[2025-07-31 11:07:05,144: WARNING/ForkPoolWorker-1] [A
[2025-07-31 11:07:05,396: WARNING/ForkPoolWorker-2] 
 47%|####7     | 119/252 [01:31<01:46,  1.25it/s]
[2025-07-31 11:07:05,396: WARNING/ForkPoolWorker-2] [A
[2025-07-31 11:07:05,630: WARNING/ForkPoolWorker-16] 
 94%|#########3| 236/252 [02:24<00:10,  1.54it/s]
[2025-07-31 11:07:05,630: WARNING/ForkPoolWorker-16] [A
[2025-07-31 11:07:05,928: WARNING/ForkPoolWorker-1] 
 54%|#####3    | 136/252 [01:37<01:21,  1.42it/s]
[2025-07-31 11:07:05,928: WARNING/ForkPoolWorker-1] [A
[2025-07-31 11:07:06,024: WARNING/ForkPoolWorker-2] 
 48%|####7     | 120/252 [01:32<01:39,  1.33it/s]
[2025-07-31 11:07:06,024: WARNING/ForkPoolWorker-2] [A
[2025-07-31 11:07:06,384: WARNING/ForkPoolWorker-16] 
 94%|#########4| 237/252 [02:24<00:10,  1.45it/s]
[2025-07-31 11:07:06,384: WARNING/ForkPoolWorker-16] [A
[2025-07-31 11:07:06,532: WARNING/ForkPoolWorker-1] 
 54%|#####4    | 137/252 [01:38<01:17,  1.48it/s]
[2025-07-31 11:07:06,532: WARNING/ForkPoolWorker-1] [A
[2025-07-31 11:07:06,644: WARNING/ForkPoolWorker-2] 
 48%|####8     | 121/252 [01:32<01:33,  1.41it/s]
[2025-07-31 11:07:06,658: WARNING/ForkPoolWorker-2] [A
[2025-07-31 11:07:06,834: WARNING/ForkPoolWorker-16] 
 94%|#########4| 238/252 [02:25<00:08,  1.62it/s]
[2025-07-31 11:07:06,834: WARNING/ForkPoolWorker-16] [A
[2025-07-31 11:07:06,977: WARNING/ForkPoolWorker-1] 
 55%|#####4    | 138/252 [01:38<01:09,  1.65it/s]
[2025-07-31 11:07:06,994: WARNING/ForkPoolWorker-1] [A
[2025-07-31 11:07:07,380: WARNING/ForkPoolWorker-16] 
 95%|#########4| 239/252 [02:25<00:07,  1.68it/s]
[2025-07-31 11:07:07,384: WARNING/ForkPoolWorker-16] [A
[2025-07-31 11:07:07,488: WARNING/ForkPoolWorker-2] 
 48%|####8     | 122/252 [01:33<01:37,  1.33it/s]
[2025-07-31 11:07:07,488: WARNING/ForkPoolWorker-2] [A
[2025-07-31 11:07:07,648: WARNING/ForkPoolWorker-1] 
 55%|#####5    | 139/252 [01:39<01:10,  1.60it/s]
[2025-07-31 11:07:07,648: WARNING/ForkPoolWorker-1] [A
[2025-07-31 11:07:07,860: WARNING/ForkPoolWorker-16] 
 95%|#########5| 240/252 [02:26<00:06,  1.78it/s]
[2025-07-31 11:07:07,866: WARNING/ForkPoolWorker-16] [A
[2025-07-31 11:07:08,008: WARNING/ForkPoolWorker-2] 
 49%|####8     | 123/252 [01:34<01:27,  1.47it/s]
[2025-07-31 11:07:08,100: WARNING/ForkPoolWorker-2] [A
[2025-07-31 11:07:08,300: WARNING/ForkPoolWorker-1] 
 56%|#####5    | 140/252 [01:39<01:10,  1.58it/s]
[2025-07-31 11:07:08,300: WARNING/ForkPoolWorker-1] [A
[2025-07-31 11:07:08,434: WARNING/ForkPoolWorker-16] 
 96%|#########5| 241/252 [02:26<00:06,  1.77it/s]
[2025-07-31 11:07:08,434: WARNING/ForkPoolWorker-16] [A
[2025-07-31 11:07:08,580: WARNING/ForkPoolWorker-15] 
  1%|          | 3/494 [02:35<5:05:45, 37.36s/it]
[2025-07-31 11:07:08,887: WARNING/ForkPoolWorker-1] 
 56%|#####5    | 141/252 [01:40<01:08,  1.61it/s]
[2025-07-31 11:07:08,892: WARNING/ForkPoolWorker-16] 
 96%|#########6| 242/252 [02:27<00:05,  1.88it/s]
[2025-07-31 11:07:08,902: WARNING/ForkPoolWorker-1] [A
[2025-07-31 11:07:08,902: WARNING/ForkPoolWorker-16] [A
[2025-07-31 11:07:08,929: WARNING/ForkPoolWorker-2] 
 49%|####9     | 124/252 [01:34<01:36,  1.33it/s]
[2025-07-31 11:07:08,929: WARNING/ForkPoolWorker-2] [A
[2025-07-31 11:07:09,281: WARNING/ForkPoolWorker-16] 
 96%|#########6| 243/252 [02:27<00:04,  2.04it/s]
[2025-07-31 11:07:09,282: WARNING/ForkPoolWorker-16] [A
[2025-07-31 11:07:09,314: WARNING/ForkPoolWorker-1] 
 56%|#####6    | 142/252 [01:40<01:01,  1.78it/s]
[2025-07-31 11:07:09,334: WARNING/ForkPoolWorker-1] [A
[2025-07-31 11:07:09,564: WARNING/ForkPoolWorker-2] 
 50%|####9     | 125/252 [01:35<01:29,  1.41it/s]
[2025-07-31 11:07:09,564: WARNING/ForkPoolWorker-2] [A
[2025-07-31 11:07:09,721: WARNING/ForkPoolWorker-16] 
 97%|#########6| 244/252 [02:28<00:03,  2.11it/s]
[2025-07-31 11:07:09,721: WARNING/ForkPoolWorker-16] [A
[2025-07-31 11:07:09,907: WARNING/ForkPoolWorker-1] 
 57%|#####6    | 143/252 [01:41<01:02,  1.75it/s]
[2025-07-31 11:07:09,926: WARNING/ForkPoolWorker-1] [A
[2025-07-31 11:07:10,138: WARNING/ForkPoolWorker-2] 
 50%|#####     | 126/252 [01:36<01:25,  1.48it/s]
[2025-07-31 11:07:10,153: WARNING/ForkPoolWorker-2] [A
[2025-07-31 11:07:10,248: WARNING/ForkPoolWorker-16] 
 97%|#########7| 245/252 [02:28<00:03,  2.04it/s]
[2025-07-31 11:07:10,248: WARNING/ForkPoolWorker-16] [A
[2025-07-31 11:07:10,541: WARNING/ForkPoolWorker-1] 
 57%|#####7    | 144/252 [01:42<01:03,  1.70it/s]
[2025-07-31 11:07:10,541: WARNING/ForkPoolWorker-1] [A
[2025-07-31 11:07:10,652: WARNING/ForkPoolWorker-16] 
 98%|#########7| 246/252 [02:29<00:02,  2.15it/s]
[2025-07-31 11:07:10,674: WARNING/ForkPoolWorker-16] [A
[2025-07-31 11:07:10,898: WARNING/ForkPoolWorker-2] 
 50%|#####     | 127/252 [01:36<01:27,  1.42it/s]
[2025-07-31 11:07:10,898: WARNING/ForkPoolWorker-2] [A
[2025-07-31 11:07:10,913: WARNING/ForkPoolWorker-1] 
 58%|#####7    | 145/252 [01:42<00:56,  1.91it/s]
[2025-07-31 11:07:10,913: WARNING/ForkPoolWorker-1] [A
[2025-07-31 11:07:11,195: WARNING/ForkPoolWorker-16] 
 98%|#########8| 247/252 [02:29<00:02,  2.05it/s]
[2025-07-31 11:07:11,195: WARNING/ForkPoolWorker-16] [A
[2025-07-31 11:07:11,292: WARNING/ForkPoolWorker-2] 
 51%|#####     | 128/252 [01:37<01:15,  1.64it/s]
[2025-07-31 11:07:11,292: WARNING/ForkPoolWorker-2] [A
[2025-07-31 11:07:11,587: WARNING/ForkPoolWorker-1] 
 58%|#####7    | 146/252 [01:43<01:00,  1.76it/s]
[2025-07-31 11:07:11,594: WARNING/ForkPoolWorker-1] [A
[2025-07-31 11:07:11,630: WARNING/ForkPoolWorker-16] 
 98%|#########8| 248/252 [02:30<00:01,  2.12it/s]
[2025-07-31 11:07:11,630: WARNING/ForkPoolWorker-16] [A
[2025-07-31 11:07:11,680: WARNING/ForkPoolWorker-2] 
 51%|#####1    | 129/252 [01:37<01:06,  1.84it/s]
[2025-07-31 11:07:11,680: WARNING/ForkPoolWorker-2] [A
[2025-07-31 11:07:12,164: WARNING/ForkPoolWorker-16] 
 99%|#########8| 249/252 [02:30<00:01,  2.04it/s]
[2025-07-31 11:07:12,187: WARNING/ForkPoolWorker-16] [A
[2025-07-31 11:07:12,263: WARNING/ForkPoolWorker-2] 
 52%|#####1    | 130/252 [01:38<01:07,  1.80it/s]
[2025-07-31 11:07:12,266: WARNING/ForkPoolWorker-1] 
 58%|#####8    | 147/252 [01:43<01:03,  1.66it/s]
[2025-07-31 11:07:12,274: WARNING/ForkPoolWorker-1] [A
[2025-07-31 11:07:12,274: WARNING/ForkPoolWorker-2] [A
[2025-07-31 11:07:12,700: WARNING/ForkPoolWorker-1] 
 59%|#####8    | 148/252 [01:44<00:57,  1.81it/s]
[2025-07-31 11:07:12,706: WARNING/ForkPoolWorker-1] [A
[2025-07-31 11:07:12,840: WARNING/ForkPoolWorker-2] 
 52%|#####1    | 131/252 [01:38<01:07,  1.78it/s]
[2025-07-31 11:07:12,840: WARNING/ForkPoolWorker-2] [A
[2025-07-31 11:07:12,850: WARNING/ForkPoolWorker-16] 
 99%|#########9| 250/252 [02:31<00:01,  1.82it/s]
[2025-07-31 11:07:12,850: WARNING/ForkPoolWorker-16] [A
[2025-07-31 11:07:13,364: WARNING/ForkPoolWorker-1] 
 59%|#####9    | 149/252 [01:45<01:00,  1.71it/s]
[2025-07-31 11:07:13,364: WARNING/ForkPoolWorker-1] [A
[2025-07-31 11:07:13,493: WARNING/ForkPoolWorker-2] 
 52%|#####2    | 132/252 [01:39<01:10,  1.70it/s]
[2025-07-31 11:07:13,494: WARNING/ForkPoolWorker-2] [A
[2025-07-31 11:07:13,555: WARNING/ForkPoolWorker-16] 
100%|#########9| 251/252 [02:31<00:00,  1.68it/s]
[2025-07-31 11:07:13,555: WARNING/ForkPoolWorker-16] [A
[2025-07-31 11:07:13,709: WARNING/ForkPoolWorker-1] 
 60%|#####9    | 150/252 [01:45<00:52,  1.95it/s]
[2025-07-31 11:07:13,709: WARNING/ForkPoolWorker-1] [A
[2025-07-31 11:07:14,002: WARNING/ForkPoolWorker-16] 
100%|##########| 252/252 [02:32<00:00,  1.81it/s]
[2025-07-31 11:07:14,002: WARNING/ForkPoolWorker-16] [A
[2025-07-31 11:07:14,022: WARNING/ForkPoolWorker-16] 
100%|##########| 252/252 [02:32<00:00,  1.65it/s]
[2025-07-31 11:07:14,080: WARNING/ForkPoolWorker-1] 
 60%|#####9    | 151/252 [01:45<00:47,  2.12it/s]
[2025-07-31 11:07:14,098: WARNING/ForkPoolWorker-1] [A
[2025-07-31 11:07:14,386: WARNING/ForkPoolWorker-2] 
 53%|#####2    | 133/252 [01:40<01:20,  1.47it/s]
[2025-07-31 11:07:14,387: WARNING/ForkPoolWorker-2] [A
[2025-07-31 11:07:14,554: WARNING/ForkPoolWorker-1] 
 60%|######    | 152/252 [01:46<00:47,  2.12it/s]
[2025-07-31 11:07:14,554: WARNING/ForkPoolWorker-1] [A
[2025-07-31 11:07:14,787: WARNING/ForkPoolWorker-1] 
 61%|######    | 153/252 [01:46<00:39,  2.50it/s]
[2025-07-31 11:07:14,787: WARNING/ForkPoolWorker-1] [A
[2025-07-31 11:07:14,834: WARNING/ForkPoolWorker-2] 
 53%|#####3    | 134/252 [01:40<01:12,  1.64it/s]
[2025-07-31 11:07:14,834: WARNING/ForkPoolWorker-2] [A
[2025-07-31 11:07:15,355: WARNING/ForkPoolWorker-1] 
 61%|######1   | 154/252 [01:47<00:44,  2.22it/s]
[2025-07-31 11:07:15,355: WARNING/ForkPoolWorker-1] [A
[2025-07-31 11:07:15,464: WARNING/ForkPoolWorker-2] 
 54%|#####3    | 135/252 [01:41<01:12,  1.62it/s]
[2025-07-31 11:07:15,464: WARNING/ForkPoolWorker-2] [A
[2025-07-31 11:07:15,828: WARNING/ForkPoolWorker-1] 
 62%|######1   | 155/252 [01:47<00:44,  2.19it/s]
[2025-07-31 11:07:15,828: WARNING/ForkPoolWorker-1] [A
[2025-07-31 11:07:16,116: WARNING/ForkPoolWorker-15] 
  1%|          | 4/494 [02:43<3:28:57, 25.59s/it]
[2025-07-31 11:07:16,248: WARNING/ForkPoolWorker-2] 
 54%|#####3    | 136/252 [01:42<01:17,  1.50it/s]
[2025-07-31 11:07:16,254: WARNING/ForkPoolWorker-2] [A
[2025-07-31 11:07:16,272: WARNING/ForkPoolWorker-1] 
 62%|######1   | 156/252 [01:47<00:43,  2.21it/s]
[2025-07-31 11:07:16,272: WARNING/ForkPoolWorker-1] [A
[2025-07-31 11:07:16,671: WARNING/ForkPoolWorker-1] 
 62%|######2   | 157/252 [01:48<00:41,  2.29it/s]
[2025-07-31 11:07:16,702: WARNING/ForkPoolWorker-1] [A
[2025-07-31 11:07:17,000: WARNING/ForkPoolWorker-2] 
 54%|#####4    | 137/252 [01:43<01:19,  1.44it/s]
[2025-07-31 11:07:17,010: WARNING/ForkPoolWorker-2] [A
[2025-07-31 11:07:17,084: WARNING/ForkPoolWorker-1] 
 63%|######2   | 158/252 [01:48<00:40,  2.33it/s]
[2025-07-31 11:07:17,084: WARNING/ForkPoolWorker-1] [A
[2025-07-31 11:07:17,686: WARNING/ForkPoolWorker-1] 
 63%|######3   | 159/252 [01:49<00:44,  2.08it/s]
[2025-07-31 11:07:17,686: WARNING/ForkPoolWorker-1] [A
[2025-07-31 11:07:17,888: WARNING/ForkPoolWorker-2] 
 55%|#####4    | 138/252 [01:43<01:25,  1.33it/s]
[2025-07-31 11:07:17,906: WARNING/ForkPoolWorker-2] [A
[2025-07-31 11:07:18,096: WARNING/ForkPoolWorker-1] 
 63%|######3   | 160/252 [01:49<00:42,  2.17it/s]
[2025-07-31 11:07:18,102: WARNING/ForkPoolWorker-1] [A
[2025-07-31 11:07:18,688: WARNING/ForkPoolWorker-2] 
 55%|#####5    | 139/252 [01:44<01:26,  1.31it/s]
[2025-07-31 11:07:18,688: WARNING/ForkPoolWorker-2] [A
[2025-07-31 11:07:18,850: WARNING/ForkPoolWorker-1] 
 64%|######3   | 161/252 [01:50<00:49,  1.82it/s]
[2025-07-31 11:07:18,850: WARNING/ForkPoolWorker-1] [A
[2025-07-31 11:07:19,065: WARNING/ForkPoolWorker-16] 
  0%|          | 1/494 [02:37<21:33:50, 157.47s/it]
[2025-07-31 11:07:19,166: WARNING/ForkPoolWorker-2] 
 56%|#####5    | 140/252 [01:45<01:16,  1.47it/s]
[2025-07-31 11:07:19,166: WARNING/ForkPoolWorker-2] [A
[2025-07-31 11:07:19,299: WARNING/ForkPoolWorker-1] 
 64%|######4   | 162/252 [01:50<00:46,  1.93it/s]
[2025-07-31 11:07:19,299: WARNING/ForkPoolWorker-1] [A
[2025-07-31 11:07:19,670: WARNING/ForkPoolWorker-1] 
 65%|######4   | 163/252 [01:51<00:42,  2.11it/s]
[2025-07-31 11:07:19,675: WARNING/ForkPoolWorker-2] 
 56%|#####5    | 141/252 [01:45<01:09,  1.59it/s]
[2025-07-31 11:07:19,683: WARNING/ForkPoolWorker-1] [A
[2025-07-31 11:07:19,683: WARNING/ForkPoolWorker-2] [A
[2025-07-31 11:07:20,067: WARNING/ForkPoolWorker-2] 
 56%|#####6    | 142/252 [01:46<01:01,  1.79it/s]
[2025-07-31 11:07:20,067: WARNING/ForkPoolWorker-2] [A
[2025-07-31 11:07:20,352: WARNING/ForkPoolWorker-1] 
 65%|######5   | 164/252 [01:52<00:47,  1.86it/s]
[2025-07-31 11:07:20,358: WARNING/ForkPoolWorker-1] [A
[2025-07-31 11:07:20,735: WARNING/ForkPoolWorker-1] 
 65%|######5   | 165/252 [01:52<00:42,  2.04it/s]
[2025-07-31 11:07:20,735: WARNING/ForkPoolWorker-1] [A
[2025-07-31 11:07:20,832: WARNING/ForkPoolWorker-15] 
  1%|1         | 5/494 [02:47<2:27:11, 18.06s/it]
[2025-07-31 11:07:21,001: WARNING/ForkPoolWorker-2] 
 57%|#####6    | 143/252 [01:47<01:13,  1.49it/s]
[2025-07-31 11:07:21,002: WARNING/ForkPoolWorker-2] [A
[2025-07-31 11:07:21,151: WARNING/ForkPoolWorker-1] 
 66%|######5   | 166/252 [01:52<00:40,  2.14it/s]
[2025-07-31 11:07:21,152: WARNING/ForkPoolWorker-1] [A
[2025-07-31 11:07:21,564: WARNING/ForkPoolWorker-2] 
 57%|#####7    | 144/252 [01:47<01:08,  1.57it/s]
[2025-07-31 11:07:21,565: WARNING/ForkPoolWorker-2] [A
[2025-07-31 11:07:21,588: WARNING/ForkPoolWorker-1] 
 66%|######6   | 167/252 [01:53<00:39,  2.18it/s]
[2025-07-31 11:07:21,588: WARNING/ForkPoolWorker-1] [A
[2025-07-31 11:07:22,022: WARNING/ForkPoolWorker-2] 
 58%|#####7    | 145/252 [01:48<01:02,  1.71it/s]
[2025-07-31 11:07:22,022: WARNING/ForkPoolWorker-2] [A
[2025-07-31 11:07:22,032: WARNING/ForkPoolWorker-1] 
 67%|######6   | 168/252 [01:53<00:38,  2.20it/s]
[2025-07-31 11:07:22,032: WARNING/ForkPoolWorker-1] [A
[2025-07-31 11:07:22,564: WARNING/ForkPoolWorker-1] 
 67%|######7   | 169/252 [01:54<00:39,  2.09it/s]
[2025-07-31 11:07:22,564: WARNING/ForkPoolWorker-1] [A
[2025-07-31 11:07:22,844: WARNING/ForkPoolWorker-2] 
 58%|#####7    | 146/252 [01:48<01:09,  1.53it/s]
[2025-07-31 11:07:22,875: WARNING/ForkPoolWorker-2] [A
[2025-07-31 11:07:22,965: WARNING/ForkPoolWorker-1] 
 67%|######7   | 170/252 [01:54<00:37,  2.20it/s]
[2025-07-31 11:07:22,965: WARNING/ForkPoolWorker-1] [A
[2025-07-31 11:07:23,118: WARNING/ForkPoolWorker-16] 
  0%|          | 2/494 [02:41<9:11:13, 67.22s/it]  
[2025-07-31 11:07:23,312: WARNING/ForkPoolWorker-1] 
 68%|######7   | 171/252 [01:54<00:34,  2.37it/s]
[2025-07-31 11:07:23,313: WARNING/ForkPoolWorker-1] [A
[2025-07-31 11:07:23,501: WARNING/ForkPoolWorker-2] 
 58%|#####8    | 147/252 [01:49<01:08,  1.52it/s]
[2025-07-31 11:07:23,501: WARNING/ForkPoolWorker-2] [A
[2025-07-31 11:07:23,709: WARNING/ForkPoolWorker-1] 
 68%|######8   | 172/252 [01:55<00:33,  2.41it/s]
[2025-07-31 11:07:23,718: WARNING/ForkPoolWorker-1] [A
[2025-07-31 11:07:23,916: WARNING/ForkPoolWorker-2] 
 59%|#####8    | 148/252 [01:49<01:00,  1.71it/s]
[2025-07-31 11:07:23,916: WARNING/ForkPoolWorker-2] [A
[2025-07-31 11:07:24,112: WARNING/ForkPoolWorker-1] 
 69%|######8   | 173/252 [01:55<00:32,  2.43it/s]
[2025-07-31 11:07:24,112: WARNING/ForkPoolWorker-1] [A
[2025-07-31 11:07:24,348: WARNING/ForkPoolWorker-2] 
 59%|#####9    | 149/252 [01:50<00:55,  1.86it/s]
[2025-07-31 11:07:24,348: WARNING/ForkPoolWorker-2] [A
[2025-07-31 11:07:24,534: WARNING/ForkPoolWorker-1] 
 69%|######9   | 174/252 [01:56<00:32,  2.41it/s]
[2025-07-31 11:07:24,535: WARNING/ForkPoolWorker-1] [A
[2025-07-31 11:07:25,044: WARNING/ForkPoolWorker-2] 
 60%|#####9    | 150/252 [01:51<00:59,  1.71it/s]
[2025-07-31 11:07:25,044: WARNING/ForkPoolWorker-2] [A
[2025-07-31 11:07:25,078: WARNING/ForkPoolWorker-1] 
 69%|######9   | 175/252 [01:56<00:34,  2.24it/s]
[2025-07-31 11:07:25,078: WARNING/ForkPoolWorker-1] [A
[2025-07-31 11:07:25,408: WARNING/ForkPoolWorker-1] 
 70%|######9   | 176/252 [01:57<00:31,  2.39it/s]
[2025-07-31 11:07:25,408: WARNING/ForkPoolWorker-1] [A
[2025-07-31 11:07:25,541: WARNING/ForkPoolWorker-2] 
 60%|#####9    | 151/252 [01:51<00:56,  1.79it/s]
[2025-07-31 11:07:25,541: WARNING/ForkPoolWorker-2] [A
[2025-07-31 11:07:25,964: WARNING/ForkPoolWorker-1] 
 70%|#######   | 177/252 [01:57<00:34,  2.18it/s]
[2025-07-31 11:07:25,964: WARNING/ForkPoolWorker-1] [A
[2025-07-31 11:07:26,021: WARNING/ForkPoolWorker-2] 
 60%|######    | 152/252 [01:52<00:53,  1.87it/s]
[2025-07-31 11:07:26,021: WARNING/ForkPoolWorker-2] [A
[2025-07-31 11:07:26,115: WARNING/ForkPoolWorker-15] 
  1%|1         | 6/494 [02:53<1:51:33, 13.72s/it]
[2025-07-31 11:07:26,214: WARNING/ForkPoolWorker-2] 
 61%|######    | 153/252 [01:52<00:42,  2.31it/s]
[2025-07-31 11:07:26,214: WARNING/ForkPoolWorker-2] [A
[2025-07-31 11:07:26,517: WARNING/ForkPoolWorker-2] 
 61%|######1   | 154/252 [01:52<00:38,  2.54it/s]
[2025-07-31 11:07:26,517: WARNING/ForkPoolWorker-2] [A
[2025-07-31 11:07:26,519: WARNING/ForkPoolWorker-1] 
 71%|#######   | 178/252 [01:58<00:34,  2.12it/s]
[2025-07-31 11:07:26,519: WARNING/ForkPoolWorker-1] [A
[2025-07-31 11:07:26,836: WARNING/ForkPoolWorker-2] 
 62%|######1   | 155/252 [01:52<00:36,  2.69it/s]
[2025-07-31 11:07:26,837: WARNING/ForkPoolWorker-2] [A
[2025-07-31 11:07:27,007: WARNING/ForkPoolWorker-16] 
  1%|          | 3/494 [02:45<5:13:27, 38.30s/it]
[2025-07-31 11:07:27,085: WARNING/ForkPoolWorker-1] 
 71%|#######1  | 179/252 [01:58<00:37,  1.94it/s]
[2025-07-31 11:07:27,085: WARNING/ForkPoolWorker-1] [A
[2025-07-31 11:07:27,320: WARNING/ForkPoolWorker-2] 
 62%|######1   | 156/252 [01:53<00:38,  2.47it/s]
[2025-07-31 11:07:27,320: WARNING/ForkPoolWorker-2] [A
[2025-07-31 11:07:27,452: WARNING/ForkPoolWorker-1] 
 71%|#######1  | 180/252 [01:59<00:33,  2.12it/s]
[2025-07-31 11:07:27,452: WARNING/ForkPoolWorker-1] [A
[2025-07-31 11:07:27,808: WARNING/ForkPoolWorker-2] 
 62%|######2   | 157/252 [01:53<00:40,  2.33it/s]
[2025-07-31 11:07:27,808: WARNING/ForkPoolWorker-2] [A
[2025-07-31 11:07:28,017: WARNING/ForkPoolWorker-1] 
 72%|#######1  | 181/252 [01:59<00:35,  2.00it/s]
[2025-07-31 11:07:28,020: WARNING/ForkPoolWorker-1] [A
[2025-07-31 11:07:28,226: WARNING/ForkPoolWorker-2] 
 63%|######2   | 158/252 [01:54<00:40,  2.34it/s]
[2025-07-31 11:07:28,226: WARNING/ForkPoolWorker-2] [A
[2025-07-31 11:07:28,379: WARNING/ForkPoolWorker-1] 
 72%|#######2  | 182/252 [02:00<00:32,  2.18it/s]
[2025-07-31 11:07:28,382: WARNING/ForkPoolWorker-1] [A
[2025-07-31 11:07:28,756: WARNING/ForkPoolWorker-1] 
 73%|#######2  | 183/252 [02:00<00:29,  2.30it/s]
[2025-07-31 11:07:28,757: WARNING/ForkPoolWorker-1] [A
[2025-07-31 11:07:28,853: WARNING/ForkPoolWorker-2] 
 63%|######3   | 159/252 [01:54<00:45,  2.06it/s]
[2025-07-31 11:07:28,853: WARNING/ForkPoolWorker-2] [A
[2025-07-31 11:07:29,215: WARNING/ForkPoolWorker-2] 
 63%|######3   | 160/252 [01:55<00:41,  2.23it/s]
[2025-07-31 11:07:29,216: WARNING/ForkPoolWorker-2] [A
[2025-07-31 11:07:29,388: WARNING/ForkPoolWorker-1] 
 73%|#######3  | 184/252 [02:01<00:33,  2.03it/s]
[2025-07-31 11:07:29,388: WARNING/ForkPoolWorker-1] [A
[2025-07-31 11:07:29,715: WARNING/ForkPoolWorker-2] 
 64%|######3   | 161/252 [01:55<00:42,  2.15it/s]
[2025-07-31 11:07:29,723: WARNING/ForkPoolWorker-2] [A
[2025-07-31 11:07:29,939: WARNING/ForkPoolWorker-1] 
 73%|#######3  | 185/252 [02:01<00:34,  1.96it/s]
[2025-07-31 11:07:29,958: WARNING/ForkPoolWorker-1] [A
[2025-07-31 11:07:30,004: WARNING/ForkPoolWorker-2] 
 64%|######4   | 162/252 [01:56<00:37,  2.43it/s]
[2025-07-31 11:07:30,004: WARNING/ForkPoolWorker-2] [A
[2025-07-31 11:07:30,348: WARNING/ForkPoolWorker-1] 
 74%|#######3  | 186/252 [02:02<00:31,  2.08it/s]
[2025-07-31 11:07:30,348: WARNING/ForkPoolWorker-1] [A
[2025-07-31 11:07:30,439: WARNING/ForkPoolWorker-2] 
 65%|######4   | 163/252 [01:56<00:37,  2.39it/s]
[2025-07-31 11:07:30,446: WARNING/ForkPoolWorker-2] [A
[2025-07-31 11:07:30,450: WARNING/ForkPoolWorker-16] 
  1%|          | 4/494 [02:48<3:20:25, 24.54s/it]
[2025-07-31 11:07:30,707: WARNING/ForkPoolWorker-1] 
 74%|#######4  | 187/252 [02:02<00:28,  2.25it/s]
[2025-07-31 11:07:30,707: WARNING/ForkPoolWorker-1] [A
[2025-07-31 11:07:30,878: WARNING/ForkPoolWorker-2] 
 65%|######5   | 164/252 [01:56<00:37,  2.35it/s]
[2025-07-31 11:07:30,879: WARNING/ForkPoolWorker-2] [A
[2025-07-31 11:07:31,232: WARNING/ForkPoolWorker-2] 
 65%|######5   | 165/252 [01:57<00:35,  2.48it/s]
[2025-07-31 11:07:31,232: WARNING/ForkPoolWorker-2] [A
[2025-07-31 11:07:31,452: WARNING/ForkPoolWorker-1] 
 75%|#######4  | 188/252 [02:03<00:34,  1.87it/s]
[2025-07-31 11:07:31,452: WARNING/ForkPoolWorker-1] [A
[2025-07-31 11:07:31,690: WARNING/ForkPoolWorker-2] 
 66%|######5   | 166/252 [01:57<00:36,  2.38it/s]
[2025-07-31 11:07:31,691: WARNING/ForkPoolWorker-2] [A
[2025-07-31 11:07:31,775: WARNING/ForkPoolWorker-1] 
 75%|#######5  | 189/252 [02:03<00:29,  2.12it/s]
[2025-07-31 11:07:31,775: WARNING/ForkPoolWorker-1] [A
[2025-07-31 11:07:31,871: WARNING/ForkPoolWorker-15] 
  1%|1         | 7/494 [02:59<1:30:12, 11.11s/it]
[2025-07-31 11:07:31,980: WARNING/ForkPoolWorker-2] 
 66%|######6   | 167/252 [01:57<00:32,  2.63it/s]
[2025-07-31 11:07:31,990: WARNING/ForkPoolWorker-2] [A
[2025-07-31 11:07:32,332: WARNING/ForkPoolWorker-1] 
 75%|#######5  | 190/252 [02:04<00:30,  2.01it/s]
[2025-07-31 11:07:32,332: WARNING/ForkPoolWorker-1] [A
[2025-07-31 11:07:32,766: WARNING/ForkPoolWorker-1] 
 76%|#######5  | 191/252 [02:04<00:29,  2.09it/s]
[2025-07-31 11:07:32,766: WARNING/ForkPoolWorker-1] [A
[2025-07-31 11:07:32,791: WARNING/ForkPoolWorker-2] 
 67%|######6   | 168/252 [01:58<00:42,  1.96it/s]
[2025-07-31 11:07:32,791: WARNING/ForkPoolWorker-2] [A
[2025-07-31 11:07:33,234: WARNING/ForkPoolWorker-2] 
 67%|######7   | 169/252 [01:59<00:40,  2.04it/s]
[2025-07-31 11:07:33,235: WARNING/ForkPoolWorker-2] [A
[2025-07-31 11:07:33,614: WARNING/ForkPoolWorker-1] 
 76%|#######6  | 192/252 [02:05<00:35,  1.70it/s]
[2025-07-31 11:07:33,618: WARNING/ForkPoolWorker-1] [A
[2025-07-31 11:07:33,876: WARNING/ForkPoolWorker-2] 
 67%|######7   | 170/252 [01:59<00:43,  1.88it/s]
[2025-07-31 11:07:33,876: WARNING/ForkPoolWorker-2] [A
[2025-07-31 11:07:33,939: WARNING/ForkPoolWorker-1] 
 77%|#######6  | 193/252 [02:05<00:30,  1.96it/s]
[2025-07-31 11:07:33,939: WARNING/ForkPoolWorker-1] [A
[2025-07-31 11:07:34,426: WARNING/ForkPoolWorker-2] 
 68%|######7   | 171/252 [02:00<00:43,  1.85it/s]
[2025-07-31 11:07:34,430: WARNING/ForkPoolWorker-2] [A
[2025-07-31 11:07:34,548: WARNING/ForkPoolWorker-1] 
 77%|#######6  | 194/252 [02:06<00:31,  1.85it/s]
[2025-07-31 11:07:34,548: WARNING/ForkPoolWorker-1] [A
[2025-07-31 11:07:34,549: WARNING/ForkPoolWorker-16] 
  1%|1         | 5/494 [02:52<2:19:55, 17.17s/it]
[2025-07-31 11:07:35,068: WARNING/ForkPoolWorker-1] 
 77%|#######7  | 195/252 [02:06<00:30,  1.87it/s]
[2025-07-31 11:07:35,068: WARNING/ForkPoolWorker-1] [A
[2025-07-31 11:07:35,140: WARNING/ForkPoolWorker-2] 
 68%|######8   | 172/252 [02:01<00:47,  1.69it/s]
[2025-07-31 11:07:35,140: WARNING/ForkPoolWorker-2] [A
[2025-07-31 11:07:35,492: WARNING/ForkPoolWorker-1] 
 78%|#######7  | 196/252 [02:07<00:28,  2.00it/s]
[2025-07-31 11:07:35,492: WARNING/ForkPoolWorker-1] [A
[2025-07-31 11:07:35,577: WARNING/ForkPoolWorker-2] 
 69%|######8   | 173/252 [02:01<00:43,  1.83it/s]
[2025-07-31 11:07:35,577: WARNING/ForkPoolWorker-2] [A
[2025-07-31 11:07:35,940: WARNING/ForkPoolWorker-1] 
 78%|#######8  | 197/252 [02:07<00:26,  2.06it/s]
[2025-07-31 11:07:35,966: WARNING/ForkPoolWorker-1] [A
[2025-07-31 11:07:35,969: WARNING/ForkPoolWorker-2] 
 69%|######9   | 174/252 [02:01<00:39,  2.00it/s]
[2025-07-31 11:07:36,010: WARNING/ForkPoolWorker-2] [A
[2025-07-31 11:07:36,276: WARNING/ForkPoolWorker-1] 
 79%|#######8  | 198/252 [02:07<00:23,  2.27it/s]
[2025-07-31 11:07:36,276: WARNING/ForkPoolWorker-1] [A
[2025-07-31 11:07:36,480: WARNING/ForkPoolWorker-15] 
  2%|1         | 8/494 [03:03<1:13:15,  9.04s/it]
[2025-07-31 11:07:36,671: WARNING/ForkPoolWorker-1] 
 79%|#######8  | 199/252 [02:08<00:22,  2.34it/s]
[2025-07-31 11:07:36,671: WARNING/ForkPoolWorker-1] [A
[2025-07-31 11:07:36,672: WARNING/ForkPoolWorker-2] 
 69%|######9   | 175/252 [02:02<00:43,  1.78it/s]
[2025-07-31 11:07:36,673: WARNING/ForkPoolWorker-2] [A
[2025-07-31 11:07:37,097: WARNING/ForkPoolWorker-2] 
 70%|######9   | 176/252 [02:03<00:39,  1.92it/s]
[2025-07-31 11:07:37,098: WARNING/ForkPoolWorker-2] [A
[2025-07-31 11:07:37,142: WARNING/ForkPoolWorker-1] 
 79%|#######9  | 200/252 [02:08<00:22,  2.27it/s]
[2025-07-31 11:07:37,142: WARNING/ForkPoolWorker-1] [A
[2025-07-31 11:07:37,489: WARNING/ForkPoolWorker-2] 
 70%|#######   | 177/252 [02:03<00:36,  2.08it/s]
[2025-07-31 11:07:37,489: WARNING/ForkPoolWorker-2] [A
[2025-07-31 11:07:37,627: WARNING/ForkPoolWorker-1] 
 80%|#######9  | 201/252 [02:09<00:23,  2.20it/s]
[2025-07-31 11:07:37,634: WARNING/ForkPoolWorker-1] [A
[2025-07-31 11:07:37,924: WARNING/ForkPoolWorker-2] 
 71%|#######   | 178/252 [02:03<00:34,  2.14it/s]
[2025-07-31 11:07:37,924: WARNING/ForkPoolWorker-2] [A
[2025-07-31 11:07:38,077: WARNING/ForkPoolWorker-1] 
 80%|########  | 202/252 [02:09<00:22,  2.21it/s]
[2025-07-31 11:07:38,086: WARNING/ForkPoolWorker-1] [A
[2025-07-31 11:07:38,281: WARNING/ForkPoolWorker-2] 
 71%|#######1  | 179/252 [02:04<00:31,  2.30it/s]
[2025-07-31 11:07:38,281: WARNING/ForkPoolWorker-2] [A
[2025-07-31 11:07:38,555: WARNING/ForkPoolWorker-1] 
 81%|########  | 203/252 [02:10<00:22,  2.17it/s]
[2025-07-31 11:07:38,556: WARNING/ForkPoolWorker-1] [A
[2025-07-31 11:07:38,704: WARNING/ForkPoolWorker-2] 
 71%|#######1  | 180/252 [02:04<00:31,  2.32it/s]
[2025-07-31 11:07:38,710: WARNING/ForkPoolWorker-2] [A
[2025-07-31 11:07:39,001: WARNING/ForkPoolWorker-1] 
 81%|########  | 204/252 [02:10<00:21,  2.19it/s]
[2025-07-31 11:07:39,001: WARNING/ForkPoolWorker-1] [A
[2025-07-31 11:07:39,259: WARNING/ForkPoolWorker-16] 
  1%|1         | 6/494 [02:57<1:45:11, 12.93s/it]
[2025-07-31 11:07:39,289: WARNING/ForkPoolWorker-2] 
 72%|#######1  | 181/252 [02:05<00:33,  2.10it/s]
[2025-07-31 11:07:39,289: WARNING/ForkPoolWorker-2] [A
[2025-07-31 11:07:39,544: WARNING/ForkPoolWorker-1] 
 81%|########1 | 205/252 [02:11<00:22,  2.08it/s]
[2025-07-31 11:07:39,566: WARNING/ForkPoolWorker-1] [A
[2025-07-31 11:07:39,643: WARNING/ForkPoolWorker-2] 
 72%|#######2  | 182/252 [02:05<00:30,  2.27it/s]
[2025-07-31 11:07:39,644: WARNING/ForkPoolWorker-2] [A
[2025-07-31 11:07:39,956: WARNING/ForkPoolWorker-1] 
 82%|########1 | 206/252 [02:11<00:21,  2.17it/s]
[2025-07-31 11:07:39,956: WARNING/ForkPoolWorker-1] [A
[2025-07-31 11:07:40,131: WARNING/ForkPoolWorker-2] 
 73%|#######2  | 183/252 [02:06<00:31,  2.20it/s]
[2025-07-31 11:07:40,158: WARNING/ForkPoolWorker-2] [A
[2025-07-31 11:07:40,358: WARNING/ForkPoolWorker-1] 
 82%|########2 | 207/252 [02:12<00:19,  2.26it/s]
[2025-07-31 11:07:40,358: WARNING/ForkPoolWorker-1] [A
[2025-07-31 11:07:40,452: WARNING/ForkPoolWorker-2] 
 73%|#######3  | 184/252 [02:06<00:28,  2.41it/s]
[2025-07-31 11:07:40,452: WARNING/ForkPoolWorker-2] [A
[2025-07-31 11:07:40,740: WARNING/ForkPoolWorker-1] 
 83%|########2 | 208/252 [02:12<00:18,  2.35it/s]
[2025-07-31 11:07:40,758: WARNING/ForkPoolWorker-1] [A
[2025-07-31 11:07:40,980: WARNING/ForkPoolWorker-2] 
 73%|#######3  | 185/252 [02:06<00:30,  2.23it/s]
[2025-07-31 11:07:40,980: WARNING/ForkPoolWorker-2] [A
[2025-07-31 11:07:41,103: WARNING/ForkPoolWorker-1] 
 83%|########2 | 209/252 [02:12<00:17,  2.46it/s]
[2025-07-31 11:07:41,103: WARNING/ForkPoolWorker-1] [A
[2025-07-31 11:07:41,172: WARNING/ForkPoolWorker-15] 
  2%|1         | 9/494 [03:08<1:02:06,  7.68s/it]
[2025-07-31 11:07:41,384: WARNING/ForkPoolWorker-2] 
 74%|#######3  | 186/252 [02:07<00:28,  2.30it/s]
[2025-07-31 11:07:41,385: WARNING/ForkPoolWorker-2] [A
[2025-07-31 11:07:41,588: WARNING/ForkPoolWorker-1] 
 83%|########3 | 210/252 [02:13<00:18,  2.33it/s]
[2025-07-31 11:07:41,588: WARNING/ForkPoolWorker-1] [A
[2025-07-31 11:07:41,744: WARNING/ForkPoolWorker-2] 
 74%|#######4  | 187/252 [02:07<00:26,  2.42it/s]
[2025-07-31 11:07:41,744: WARNING/ForkPoolWorker-2] [A
[2025-07-31 11:07:42,127: WARNING/ForkPoolWorker-2] 
 75%|#######4  | 188/252 [02:08<00:25,  2.48it/s]
[2025-07-31 11:07:42,127: WARNING/ForkPoolWorker-2] [A
[2025-07-31 11:07:42,240: WARNING/ForkPoolWorker-1] 
 84%|########3 | 211/252 [02:13<00:20,  2.01it/s]
[2025-07-31 11:07:42,240: WARNING/ForkPoolWorker-1] [A
[2025-07-31 11:07:42,505: WARNING/ForkPoolWorker-2] 
 75%|#######5  | 189/252 [02:08<00:24,  2.53it/s]
[2025-07-31 11:07:42,505: WARNING/ForkPoolWorker-2] [A
[2025-07-31 11:07:42,876: WARNING/ForkPoolWorker-1] 
 84%|########4 | 212/252 [02:14<00:21,  1.86it/s]
[2025-07-31 11:07:42,876: WARNING/ForkPoolWorker-1] [A
[2025-07-31 11:07:42,934: WARNING/ForkPoolWorker-2] 
 75%|#######5  | 190/252 [02:08<00:25,  2.46it/s]
[2025-07-31 11:07:42,934: WARNING/ForkPoolWorker-2] [A
[2025-07-31 11:07:43,089: WARNING/ForkPoolWorker-16] 
  1%|1         | 7/494 [03:01<1:20:49,  9.96s/it]
[2025-07-31 11:07:43,238: WARNING/ForkPoolWorker-1] 
 85%|########4 | 213/252 [02:14<00:18,  2.06it/s]
[2025-07-31 11:07:43,246: WARNING/ForkPoolWorker-1] [A
[2025-07-31 11:07:43,352: WARNING/ForkPoolWorker-2] 
 76%|#######5  | 191/252 [02:09<00:24,  2.44it/s]
[2025-07-31 11:07:43,352: WARNING/ForkPoolWorker-2] [A
[2025-07-31 11:07:43,756: WARNING/ForkPoolWorker-1] 
 85%|########4 | 214/252 [02:15<00:18,  2.02it/s]
[2025-07-31 11:07:43,757: WARNING/ForkPoolWorker-1] [A
[2025-07-31 11:07:43,970: WARNING/ForkPoolWorker-2] 
 76%|#######6  | 192/252 [02:09<00:28,  2.12it/s]
[2025-07-31 11:07:43,971: WARNING/ForkPoolWorker-2] [A
[2025-07-31 11:07:44,132: WARNING/ForkPoolWorker-1] 
 85%|########5 | 215/252 [02:15<00:17,  2.18it/s]
[2025-07-31 11:07:44,132: WARNING/ForkPoolWorker-1] [A
[2025-07-31 11:07:44,460: WARNING/ForkPoolWorker-2] 
 77%|#######6  | 193/252 [02:10<00:28,  2.09it/s]
[2025-07-31 11:07:44,460: WARNING/ForkPoolWorker-2] [A
[2025-07-31 11:07:44,536: WARNING/ForkPoolWorker-1] 
 86%|########5 | 216/252 [02:16<00:15,  2.26it/s]
[2025-07-31 11:07:44,537: WARNING/ForkPoolWorker-1] [A
[2025-07-31 11:07:44,912: WARNING/ForkPoolWorker-2] 
 77%|#######6  | 194/252 [02:10<00:27,  2.13it/s]
[2025-07-31 11:07:44,912: WARNING/ForkPoolWorker-2] [A
[2025-07-31 11:07:45,218: WARNING/ForkPoolWorker-1] 
 86%|########6 | 217/252 [02:16<00:18,  1.94it/s]
[2025-07-31 11:07:45,219: WARNING/ForkPoolWorker-1] [A
[2025-07-31 11:07:45,376: WARNING/ForkPoolWorker-2] 
 77%|#######7  | 195/252 [02:11<00:26,  2.14it/s]
[2025-07-31 11:07:45,376: WARNING/ForkPoolWorker-2] [A
[2025-07-31 11:07:45,740: WARNING/ForkPoolWorker-2] 
 78%|#######7  | 196/252 [02:11<00:24,  2.29it/s]
[2025-07-31 11:07:45,741: WARNING/ForkPoolWorker-2] [A
[2025-07-31 11:07:45,761: WARNING/ForkPoolWorker-1] 
 87%|########6 | 218/252 [02:17<00:17,  1.91it/s]
[2025-07-31 11:07:45,761: WARNING/ForkPoolWorker-1] [A
[2025-07-31 11:07:46,132: WARNING/ForkPoolWorker-2] 
 78%|#######8  | 197/252 [02:12<00:23,  2.36it/s]
[2025-07-31 11:07:46,132: WARNING/ForkPoolWorker-2] [A
[2025-07-31 11:07:46,310: WARNING/ForkPoolWorker-1] 
 87%|########6 | 219/252 [02:17<00:17,  1.88it/s]
[2025-07-31 11:07:46,310: WARNING/ForkPoolWorker-1] [A
[2025-07-31 11:07:46,422: WARNING/ForkPoolWorker-15] 
  2%|2         | 10/494 [03:13<55:55,  6.93s/it] 
[2025-07-31 11:07:46,562: WARNING/ForkPoolWorker-2] 
 79%|#######8  | 198/252 [02:12<00:22,  2.35it/s]
[2025-07-31 11:07:46,562: WARNING/ForkPoolWorker-2] [A
[2025-07-31 11:07:46,767: WARNING/ForkPoolWorker-1] 
 87%|########7 | 220/252 [02:18<00:16,  1.97it/s]
[2025-07-31 11:07:46,767: WARNING/ForkPoolWorker-1] [A
[2025-07-31 11:07:47,045: WARNING/ForkPoolWorker-2] 
 79%|#######8  | 199/252 [02:13<00:23,  2.26it/s]
[2025-07-31 11:07:47,045: WARNING/ForkPoolWorker-2] [A
[2025-07-31 11:07:47,053: WARNING/ForkPoolWorker-16] 
  2%|1         | 8/494 [03:05<1:05:11,  8.05s/it]
[2025-07-31 11:07:47,188: WARNING/ForkPoolWorker-1] 
 88%|########7 | 221/252 [02:18<00:14,  2.07it/s]
[2025-07-31 11:07:47,188: WARNING/ForkPoolWorker-1] [A
[2025-07-31 11:07:47,665: WARNING/ForkPoolWorker-1] 
 88%|########8 | 222/252 [02:19<00:14,  2.08it/s]
[2025-07-31 11:07:47,665: WARNING/ForkPoolWorker-1] [A
[2025-07-31 11:07:47,666: WARNING/ForkPoolWorker-2] 
 79%|#######9  | 200/252 [02:13<00:25,  2.02it/s]
[2025-07-31 11:07:47,666: WARNING/ForkPoolWorker-2] [A
[2025-07-31 11:07:48,327: WARNING/ForkPoolWorker-1] 
 88%|########8 | 223/252 [02:20<00:15,  1.87it/s]
[2025-07-31 11:07:48,327: WARNING/ForkPoolWorker-1] [A
[2025-07-31 11:07:48,364: WARNING/ForkPoolWorker-2] 
 80%|#######9  | 201/252 [02:14<00:28,  1.80it/s]
[2025-07-31 11:07:48,365: WARNING/ForkPoolWorker-2] [A
[2025-07-31 11:07:48,759: WARNING/ForkPoolWorker-1] 
 89%|########8 | 224/252 [02:20<00:14,  1.99it/s]
[2025-07-31 11:07:48,759: WARNING/ForkPoolWorker-1] [A
[2025-07-31 11:07:48,903: WARNING/ForkPoolWorker-2] 
 80%|########  | 202/252 [02:14<00:27,  1.81it/s]
[2025-07-31 11:07:48,904: WARNING/ForkPoolWorker-2] [A
[2025-07-31 11:07:49,156: WARNING/ForkPoolWorker-2] 
 81%|########  | 203/252 [02:15<00:22,  2.17it/s]
[2025-07-31 11:07:49,156: WARNING/ForkPoolWorker-2] [A
[2025-07-31 11:07:49,520: WARNING/ForkPoolWorker-1] 
 89%|########9 | 225/252 [02:21<00:15,  1.72it/s]
[2025-07-31 11:07:49,520: WARNING/ForkPoolWorker-1] [A
[2025-07-31 11:07:49,607: WARNING/ForkPoolWorker-2] 
 81%|########  | 204/252 [02:15<00:22,  2.18it/s]
[2025-07-31 11:07:49,607: WARNING/ForkPoolWorker-2] [A
[2025-07-31 11:07:50,052: WARNING/ForkPoolWorker-1] 
 90%|########9 | 226/252 [02:21<00:14,  1.76it/s]
[2025-07-31 11:07:50,052: WARNING/ForkPoolWorker-1] [A
[2025-07-31 11:07:50,349: WARNING/ForkPoolWorker-2] 
 81%|########1 | 205/252 [02:16<00:25,  1.84it/s]
[2025-07-31 11:07:50,350: WARNING/ForkPoolWorker-2] [A
[2025-07-31 11:07:50,504: WARNING/ForkPoolWorker-1] 
 90%|######### | 227/252 [02:22<00:13,  1.88it/s]
[2025-07-31 11:07:50,504: WARNING/ForkPoolWorker-1] [A
[2025-07-31 11:07:50,619: WARNING/ForkPoolWorker-2] 
 82%|########1 | 206/252 [02:16<00:21,  2.17it/s]
[2025-07-31 11:07:50,620: WARNING/ForkPoolWorker-2] [A
[2025-07-31 11:07:50,932: WARNING/ForkPoolWorker-1] 
 90%|######### | 228/252 [02:22<00:12,  2.00it/s]
[2025-07-31 11:07:50,932: WARNING/ForkPoolWorker-1] [A
[2025-07-31 11:07:50,974: WARNING/ForkPoolWorker-15] 
  2%|2         | 11/494 [03:18<49:56,  6.20s/it]
[2025-07-31 11:07:51,133: WARNING/ForkPoolWorker-2] 
 82%|########2 | 207/252 [02:17<00:21,  2.10it/s]
[2025-07-31 11:07:51,134: WARNING/ForkPoolWorker-2] [A
[2025-07-31 11:07:51,248: WARNING/ForkPoolWorker-1] 
 91%|######### | 229/252 [02:22<00:10,  2.24it/s]
[2025-07-31 11:07:51,249: WARNING/ForkPoolWorker-1] [A
[2025-07-31 11:07:51,616: WARNING/ForkPoolWorker-1] 
 91%|#########1| 230/252 [02:23<00:09,  2.37it/s]
[2025-07-31 11:07:51,616: WARNING/ForkPoolWorker-1] [A
[2025-07-31 11:07:51,649: WARNING/ForkPoolWorker-16] 
  2%|1         | 9/494 [03:10<56:20,  6.97s/it]  
[2025-07-31 11:07:51,669: WARNING/ForkPoolWorker-2] 
 83%|########2 | 208/252 [02:17<00:21,  2.02it/s]
[2025-07-31 11:07:51,669: WARNING/ForkPoolWorker-2] [A
[2025-07-31 11:07:52,154: WARNING/ForkPoolWorker-1] 
 92%|#########1| 231/252 [02:23<00:09,  2.19it/s]
[2025-07-31 11:07:52,155: WARNING/ForkPoolWorker-1] [A
[2025-07-31 11:07:52,184: WARNING/ForkPoolWorker-2] 
 83%|########2 | 209/252 [02:18<00:21,  2.00it/s]
[2025-07-31 11:07:52,190: WARNING/ForkPoolWorker-2] [A
[2025-07-31 11:07:52,577: WARNING/ForkPoolWorker-1] 
 92%|#########2| 232/252 [02:24<00:08,  2.24it/s]
[2025-07-31 11:07:52,577: WARNING/ForkPoolWorker-1] [A
[2025-07-31 11:07:52,789: WARNING/ForkPoolWorker-2] 
 83%|########3 | 210/252 [02:18<00:22,  1.88it/s]
[2025-07-31 11:07:52,790: WARNING/ForkPoolWorker-2] [A
[2025-07-31 11:07:53,148: WARNING/ForkPoolWorker-2] 
 84%|########3 | 211/252 [02:19<00:19,  2.08it/s]
[2025-07-31 11:07:53,149: WARNING/ForkPoolWorker-2] [A
[2025-07-31 11:07:53,207: WARNING/ForkPoolWorker-1] 
 92%|#########2| 233/252 [02:24<00:09,  1.99it/s]
[2025-07-31 11:07:53,208: WARNING/ForkPoolWorker-1] [A
[2025-07-31 11:07:53,601: WARNING/ForkPoolWorker-2] 
 84%|########4 | 212/252 [02:19<00:18,  2.12it/s]
[2025-07-31 11:07:53,618: WARNING/ForkPoolWorker-2] [A
[2025-07-31 11:07:53,661: WARNING/ForkPoolWorker-1] 
 93%|#########2| 234/252 [02:25<00:08,  2.05it/s]
[2025-07-31 11:07:53,661: WARNING/ForkPoolWorker-1] [A
[2025-07-31 11:07:54,006: WARNING/ForkPoolWorker-2] 
 85%|########4 | 213/252 [02:20<00:17,  2.21it/s]
[2025-07-31 11:07:54,006: WARNING/ForkPoolWorker-2] [A
[2025-07-31 11:07:54,077: WARNING/ForkPoolWorker-1] 
 93%|#########3| 235/252 [02:25<00:07,  2.15it/s]
[2025-07-31 11:07:54,077: WARNING/ForkPoolWorker-1] [A
[2025-07-31 11:07:54,407: WARNING/ForkPoolWorker-2] 
 85%|########4 | 214/252 [02:20<00:16,  2.29it/s]
[2025-07-31 11:07:54,407: WARNING/ForkPoolWorker-2] [A
[2025-07-31 11:07:54,636: WARNING/ForkPoolWorker-1] 
 94%|#########3| 236/252 [02:26<00:07,  2.02it/s]
[2025-07-31 11:07:54,636: WARNING/ForkPoolWorker-1] [A
[2025-07-31 11:07:54,847: WARNING/ForkPoolWorker-2] 
 85%|########5 | 215/252 [02:20<00:16,  2.28it/s]
[2025-07-31 11:07:54,847: WARNING/ForkPoolWorker-2] [A
[2025-07-31 11:07:55,082: WARNING/ForkPoolWorker-1] 
 94%|#########4| 237/252 [02:26<00:07,  2.08it/s]
[2025-07-31 11:07:55,083: WARNING/ForkPoolWorker-1] [A
[2025-07-31 11:07:55,310: WARNING/ForkPoolWorker-2] 
 86%|########5 | 216/252 [02:21<00:16,  2.25it/s]
[2025-07-31 11:07:55,310: WARNING/ForkPoolWorker-2] [A
[2025-07-31 11:07:55,543: WARNING/ForkPoolWorker-1] 
 94%|#########4| 238/252 [02:27<00:06,  2.11it/s]
[2025-07-31 11:07:55,543: WARNING/ForkPoolWorker-1] [A
[2025-07-31 11:07:55,624: WARNING/ForkPoolWorker-16] 
  2%|2         | 10/494 [03:14<48:45,  6.05s/it]
[2025-07-31 11:07:55,737: WARNING/ForkPoolWorker-2] 
 86%|########6 | 217/252 [02:21<00:15,  2.27it/s]
[2025-07-31 11:07:55,738: WARNING/ForkPoolWorker-2] [A
[2025-07-31 11:07:55,956: WARNING/ForkPoolWorker-1] 
 95%|#########4| 239/252 [02:27<00:05,  2.19it/s]
[2025-07-31 11:07:55,956: WARNING/ForkPoolWorker-1] [A
[2025-07-31 11:07:56,085: WARNING/ForkPoolWorker-15] 
  2%|2         | 12/494 [03:23<47:09,  5.87s/it]
[2025-07-31 11:07:56,126: WARNING/ForkPoolWorker-2] 
 87%|########6 | 218/252 [02:22<00:14,  2.36it/s]
[2025-07-31 11:07:56,126: WARNING/ForkPoolWorker-2] [A
[2025-07-31 11:07:56,352: WARNING/ForkPoolWorker-1] 
 95%|#########5| 240/252 [02:28<00:05,  2.28it/s]
[2025-07-31 11:07:56,352: WARNING/ForkPoolWorker-1] [A
[2025-07-31 11:07:56,604: WARNING/ForkPoolWorker-2] 
 87%|########6 | 219/252 [02:22<00:14,  2.27it/s]
[2025-07-31 11:07:56,622: WARNING/ForkPoolWorker-2] [A
[2025-07-31 11:07:56,882: WARNING/ForkPoolWorker-1] 
 96%|#########5| 241/252 [02:28<00:05,  2.15it/s]
[2025-07-31 11:07:56,883: WARNING/ForkPoolWorker-1] [A
[2025-07-31 11:07:57,096: WARNING/ForkPoolWorker-2] 
 87%|########7 | 220/252 [02:23<00:14,  2.19it/s]
[2025-07-31 11:07:57,102: WARNING/ForkPoolWorker-2] [A
[2025-07-31 11:07:57,327: WARNING/ForkPoolWorker-1] 
 96%|#########6| 242/252 [02:29<00:04,  2.18it/s]
[2025-07-31 11:07:57,328: WARNING/ForkPoolWorker-1] [A
[2025-07-31 11:07:57,655: WARNING/ForkPoolWorker-2] 
 88%|########7 | 221/252 [02:23<00:15,  2.05it/s]
[2025-07-31 11:07:57,662: WARNING/ForkPoolWorker-2] [A
[2025-07-31 11:07:57,741: WARNING/ForkPoolWorker-1] 
 96%|#########6| 243/252 [02:29<00:04,  2.24it/s]
[2025-07-31 11:07:57,741: WARNING/ForkPoolWorker-1] [A
[2025-07-31 11:07:58,014: WARNING/ForkPoolWorker-1] 
 97%|#########6| 244/252 [02:29<00:03,  2.54it/s]
[2025-07-31 11:07:58,014: WARNING/ForkPoolWorker-1] [A
[2025-07-31 11:07:58,179: WARNING/ForkPoolWorker-2] 
 88%|########8 | 222/252 [02:24<00:14,  2.01it/s]
[2025-07-31 11:07:58,179: WARNING/ForkPoolWorker-2] [A
[2025-07-31 11:07:58,450: WARNING/ForkPoolWorker-1] 
 97%|#########7| 245/252 [02:30<00:02,  2.46it/s]
[2025-07-31 11:07:58,450: WARNING/ForkPoolWorker-1] [A
[2025-07-31 11:07:58,548: WARNING/ForkPoolWorker-2] 
 88%|########8 | 223/252 [02:24<00:13,  2.18it/s]
[2025-07-31 11:07:58,548: WARNING/ForkPoolWorker-2] [A
[2025-07-31 11:07:58,895: WARNING/ForkPoolWorker-1] 
 98%|#########7| 246/252 [02:30<00:02,  2.39it/s]
[2025-07-31 11:07:58,895: WARNING/ForkPoolWorker-1] [A
[2025-07-31 11:07:59,070: WARNING/ForkPoolWorker-2] 
 89%|########8 | 224/252 [02:25<00:13,  2.09it/s]
[2025-07-31 11:07:59,082: WARNING/ForkPoolWorker-2] [A
[2025-07-31 11:07:59,396: WARNING/ForkPoolWorker-1] 
 98%|#########8| 247/252 [02:31<00:02,  2.26it/s]
[2025-07-31 11:07:59,406: WARNING/ForkPoolWorker-1] [A
[2025-07-31 11:07:59,492: WARNING/ForkPoolWorker-2] 
 89%|########9 | 225/252 [02:25<00:12,  2.17it/s]
[2025-07-31 11:07:59,492: WARNING/ForkPoolWorker-2] [A
[2025-07-31 11:07:59,509: WARNING/ForkPoolWorker-16] 
  2%|2         | 11/494 [03:17<43:20,  5.38s/it]
[2025-07-31 11:07:59,844: WARNING/ForkPoolWorker-1] 
 98%|#########8| 248/252 [02:31<00:01,  2.25it/s]
[2025-07-31 11:07:59,844: WARNING/ForkPoolWorker-1] [A
[2025-07-31 11:08:00,147: WARNING/ForkPoolWorker-2] 
 90%|########9 | 226/252 [02:26<00:13,  1.93it/s]
[2025-07-31 11:08:00,147: WARNING/ForkPoolWorker-2] [A
[2025-07-31 11:08:00,332: WARNING/ForkPoolWorker-1] 
 99%|#########8| 249/252 [02:32<00:01,  2.19it/s]
[2025-07-31 11:08:00,332: WARNING/ForkPoolWorker-1] [A
[2025-07-31 11:08:00,478: WARNING/ForkPoolWorker-2] 
 90%|######### | 227/252 [02:26<00:11,  2.16it/s]
[2025-07-31 11:08:00,478: WARNING/ForkPoolWorker-2] [A
[2025-07-31 11:08:00,744: WARNING/ForkPoolWorker-1] 
 99%|#########9| 250/252 [02:32<00:00,  2.25it/s]
[2025-07-31 11:08:00,744: WARNING/ForkPoolWorker-1] [A
[2025-07-31 11:08:01,024: WARNING/ForkPoolWorker-2] 
 90%|######### | 228/252 [02:27<00:11,  2.05it/s]
[2025-07-31 11:08:01,024: WARNING/ForkPoolWorker-2] [A
[2025-07-31 11:08:01,312: WARNING/ForkPoolWorker-15] 
  3%|2         | 13/494 [03:28<45:30,  5.68s/it]
[2025-07-31 11:08:01,383: WARNING/ForkPoolWorker-1] 
100%|#########9| 251/252 [02:33<00:00,  1.99it/s]
[2025-07-31 11:08:01,392: WARNING/ForkPoolWorker-1] [A
[2025-07-31 11:08:01,436: WARNING/ForkPoolWorker-2] 
 91%|######### | 229/252 [02:27<00:10,  2.15it/s]
[2025-07-31 11:08:01,462: WARNING/ForkPoolWorker-2] [A
[2025-07-31 11:08:01,882: WARNING/ForkPoolWorker-1] 
100%|##########| 252/252 [02:33<00:00,  1.99it/s]
[2025-07-31 11:08:01,893: WARNING/ForkPoolWorker-1] [A
[2025-07-31 11:08:01,893: WARNING/ForkPoolWorker-1] 
100%|##########| 252/252 [02:33<00:00,  1.64it/s]
[2025-07-31 11:08:01,948: WARNING/ForkPoolWorker-2] 
 91%|#########1| 230/252 [02:27<00:10,  2.09it/s]
[2025-07-31 11:08:01,948: WARNING/ForkPoolWorker-2] [A
[2025-07-31 11:08:02,185: WARNING/ForkPoolWorker-2] 
 92%|#########1| 231/252 [02:28<00:08,  2.46it/s]
[2025-07-31 11:08:02,194: WARNING/ForkPoolWorker-2] [A
[2025-07-31 11:08:02,512: WARNING/ForkPoolWorker-2] 
 92%|#########2| 232/252 [02:28<00:07,  2.61it/s]
[2025-07-31 11:08:02,512: WARNING/ForkPoolWorker-2] [A
[2025-07-31 11:08:02,995: WARNING/ForkPoolWorker-2] 
 92%|#########2| 233/252 [02:29<00:07,  2.42it/s]
[2025-07-31 11:08:02,996: WARNING/ForkPoolWorker-2] [A
[2025-07-31 11:08:03,364: WARNING/ForkPoolWorker-2] 
 93%|#########2| 234/252 [02:29<00:07,  2.50it/s]
[2025-07-31 11:08:03,370: WARNING/ForkPoolWorker-2] [A
[2025-07-31 11:08:03,465: WARNING/ForkPoolWorker-16] 
  2%|2         | 12/494 [03:21<39:45,  4.95s/it]
[2025-07-31 11:08:03,637: WARNING/ForkPoolWorker-2] 
 93%|#########3| 235/252 [02:29<00:06,  2.77it/s]
[2025-07-31 11:08:03,637: WARNING/ForkPoolWorker-2] [A
[2025-07-31 11:08:04,112: WARNING/ForkPoolWorker-2] 
 94%|#########3| 236/252 [02:30<00:06,  2.53it/s]
[2025-07-31 11:08:04,112: WARNING/ForkPoolWorker-2] [A
[2025-07-31 11:08:04,510: WARNING/ForkPoolWorker-2] 
 94%|#########4| 237/252 [02:30<00:05,  2.52it/s]
[2025-07-31 11:08:04,510: WARNING/ForkPoolWorker-2] [A
[2025-07-31 11:08:05,028: WARNING/ForkPoolWorker-2] 
 94%|#########4| 238/252 [02:31<00:06,  2.31it/s]
[2025-07-31 11:08:05,028: WARNING/ForkPoolWorker-2] [A
[2025-07-31 11:08:05,347: WARNING/ForkPoolWorker-15] 
  3%|2         | 14/494 [03:32<41:26,  5.18s/it]
[2025-07-31 11:08:05,348: WARNING/ForkPoolWorker-2] 
 95%|#########4| 239/252 [02:31<00:05,  2.51it/s]
[2025-07-31 11:08:05,350: WARNING/ForkPoolWorker-2] [A
[2025-07-31 11:08:05,806: WARNING/ForkPoolWorker-2] 
 95%|#########5| 240/252 [02:31<00:04,  2.41it/s]
[2025-07-31 11:08:05,806: WARNING/ForkPoolWorker-2] [A
[2025-07-31 11:08:06,244: WARNING/ForkPoolWorker-2] 
 96%|#########5| 241/252 [02:32<00:04,  2.36it/s]
[2025-07-31 11:08:06,244: WARNING/ForkPoolWorker-2] [A
[2025-07-31 11:08:06,672: WARNING/ForkPoolWorker-2] 
 96%|#########6| 242/252 [02:32<00:04,  2.35it/s]
[2025-07-31 11:08:06,673: WARNING/ForkPoolWorker-2] [A
[2025-07-31 11:08:06,927: WARNING/ForkPoolWorker-1] 
  0%|          | 1/494 [02:38<21:43:09, 158.60s/it]
[2025-07-31 11:08:07,113: WARNING/ForkPoolWorker-2] 
 96%|#########6| 243/252 [02:33<00:03,  2.33it/s]
[2025-07-31 11:08:07,118: WARNING/ForkPoolWorker-2] [A
[2025-07-31 11:08:07,180: WARNING/ForkPoolWorker-16] 
  3%|2         | 13/494 [03:25<36:40,  4.58s/it]
[2025-07-31 11:08:07,528: WARNING/ForkPoolWorker-2] 
 97%|#########6| 244/252 [02:33<00:03,  2.35it/s]
[2025-07-31 11:08:07,528: WARNING/ForkPoolWorker-2] [A
[2025-07-31 11:08:07,862: WARNING/ForkPoolWorker-2] 
 97%|#########7| 245/252 [02:33<00:02,  2.51it/s]
[2025-07-31 11:08:07,862: WARNING/ForkPoolWorker-2] [A
[2025-07-31 11:08:08,502: WARNING/ForkPoolWorker-2] 
 98%|#########7| 246/252 [02:34<00:02,  2.13it/s]
[2025-07-31 11:08:08,502: WARNING/ForkPoolWorker-2] [A
[2025-07-31 11:08:08,839: WARNING/ForkPoolWorker-2] 
 98%|#########8| 247/252 [02:34<00:02,  2.32it/s]
[2025-07-31 11:08:08,840: WARNING/ForkPoolWorker-2] [A
[2025-07-31 11:08:09,085: WARNING/ForkPoolWorker-2] 
 98%|#########8| 248/252 [02:35<00:01,  2.67it/s]
[2025-07-31 11:08:09,085: WARNING/ForkPoolWorker-2] [A
[2025-07-31 11:08:09,221: WARNING/ForkPoolWorker-15] 
  3%|3         | 15/494 [03:36<38:12,  4.79s/it]
[2025-07-31 11:08:09,620: WARNING/ForkPoolWorker-2] 
 99%|#########8| 249/252 [02:35<00:01,  2.36it/s]
[2025-07-31 11:08:09,620: WARNING/ForkPoolWorker-2] [A
[2025-07-31 11:08:10,109: WARNING/ForkPoolWorker-2] 
 99%|#########9| 250/252 [02:36<00:00,  2.26it/s]
[2025-07-31 11:08:10,110: WARNING/ForkPoolWorker-2] [A
[2025-07-31 11:08:10,376: WARNING/ForkPoolWorker-2] 
100%|#########9| 251/252 [02:36<00:00,  2.56it/s]
[2025-07-31 11:08:10,382: WARNING/ForkPoolWorker-2] [A
[2025-07-31 11:08:10,387: WARNING/ForkPoolWorker-1] 
  0%|          | 2/494 [02:42<9:12:12, 67.34s/it]  
[2025-07-31 11:08:10,409: WARNING/ForkPoolWorker-16] 
  3%|2         | 14/494 [03:28<33:21,  4.17s/it]
[2025-07-31 11:08:10,714: WARNING/ForkPoolWorker-2] 
100%|##########| 252/252 [02:36<00:00,  2.67it/s]
[2025-07-31 11:08:10,714: WARNING/ForkPoolWorker-2] [A
[2025-07-31 11:08:10,714: WARNING/ForkPoolWorker-2] 
100%|##########| 252/252 [02:36<00:00,  1.61it/s]
[2025-07-31 11:08:12,140: WARNING/ForkPoolWorker-15] 
  3%|3         | 16/494 [03:39<33:39,  4.22s/it]
[2025-07-31 11:08:12,677: WARNING/ForkPoolWorker-1] 
  1%|          | 3/494 [02:44<5:08:00, 37.64s/it]
[2025-07-31 11:08:12,816: WARNING/ForkPoolWorker-16] 
  3%|3         | 15/494 [03:31<29:01,  3.64s/it]
[2025-07-31 11:08:14,054: WARNING/ForkPoolWorker-2] 
  0%|          | 1/494 [02:40<21:55:14, 160.07s/it]
[2025-07-31 11:08:14,111: WARNING/ForkPoolWorker-15] 
  3%|3         | 17/494 [03:41<28:10,  3.54s/it]
[2025-07-31 11:08:14,348: WARNING/ForkPoolWorker-1] 
  1%|          | 4/494 [02:46<3:11:24, 23.44s/it]
[2025-07-31 11:08:14,511: WARNING/ForkPoolWorker-16] 
  3%|3         | 16/494 [03:32<24:19,  3.05s/it]
[2025-07-31 11:08:16,115: WARNING/ForkPoolWorker-15] 
  4%|3         | 18/494 [03:43<24:26,  3.08s/it]
[2025-07-31 11:08:16,190: WARNING/ForkPoolWorker-2] 
  0%|          | 2/494 [02:42<9:10:46, 67.17s/it]  
[2025-07-31 11:08:16,364: WARNING/ForkPoolWorker-16] 
  3%|3         | 17/494 [03:34<21:24,  2.69s/it]
[2025-07-31 11:08:16,368: WARNING/ForkPoolWorker-1] 
  1%|1         | 5/494 [02:48<2:08:04, 15.71s/it]
[2025-07-31 11:08:18,109: WARNING/ForkPoolWorker-15] 
  4%|3         | 19/494 [03:45<21:48,  2.76s/it]
[2025-07-31 11:08:18,261: WARNING/ForkPoolWorker-2] 
  1%|          | 3/494 [02:44<5:06:24, 37.44s/it]
[2025-07-31 11:08:18,331: WARNING/ForkPoolWorker-16] 
  4%|3         | 18/494 [03:36<19:37,  2.47s/it]
[2025-07-31 11:08:18,347: WARNING/ForkPoolWorker-1] 
  1%|1         | 6/494 [02:50<1:29:49, 11.04s/it]
[2025-07-31 11:08:19,969: WARNING/ForkPoolWorker-1] 
  1%|1         | 7/494 [02:51<1:04:38,  7.96s/it]
[2025-07-31 11:08:20,138: WARNING/ForkPoolWorker-16] 
  4%|3         | 19/494 [03:38<18:00,  2.27s/it]
[2025-07-31 11:08:20,404: WARNING/ForkPoolWorker-15] 
  4%|4         | 20/494 [03:47<20:40,  2.62s/it]
[2025-07-31 11:08:20,582: WARNING/ForkPoolWorker-2] 
  1%|          | 4/494 [02:46<3:12:32, 23.58s/it]
[2025-07-31 11:08:21,667: WARNING/ForkPoolWorker-1] 
  2%|1         | 8/494 [02:53<48:21,  5.97s/it]  
[2025-07-31 11:08:21,747: WARNING/ForkPoolWorker-16] 
  4%|4         | 20/494 [03:40<16:23,  2.07s/it]
[2025-07-31 11:08:22,413: WARNING/ForkPoolWorker-15] 
  4%|4         | 21/494 [03:49<19:11,  2.43s/it]
[2025-07-31 11:08:23,025: WARNING/ForkPoolWorker-2] 
  1%|1         | 5/494 [02:49<2:10:02, 15.96s/it]
[2025-07-31 11:08:23,458: WARNING/ForkPoolWorker-1] 
  2%|1         | 9/494 [02:55<37:41,  4.66s/it]
[2025-07-31 11:08:23,581: WARNING/ForkPoolWorker-16] 
  4%|4         | 21/494 [03:41<15:47,  2.00s/it]
[2025-07-31 11:08:24,673: WARNING/ForkPoolWorker-15] 
  4%|4         | 22/494 [03:51<18:44,  2.38s/it]
[2025-07-31 11:08:25,212: WARNING/ForkPoolWorker-1] 
  2%|2         | 10/494 [02:56<30:22,  3.77s/it]
[2025-07-31 11:08:25,246: WARNING/ForkPoolWorker-2] 
  1%|1         | 6/494 [02:51<1:31:47, 11.29s/it]
[2025-07-31 11:08:25,497: WARNING/ForkPoolWorker-16] 
  4%|4         | 22/494 [03:43<15:32,  1.98s/it]
[2025-07-31 11:08:26,693: WARNING/ForkPoolWorker-15] 
  5%|4         | 23/494 [03:53<17:50,  2.27s/it]
[2025-07-31 11:08:27,241: WARNING/ForkPoolWorker-1] 
  2%|2         | 11/494 [02:58<26:01,  3.23s/it]
[2025-07-31 11:08:27,315: WARNING/ForkPoolWorker-2] 
  1%|1         | 7/494 [02:53<1:07:08,  8.27s/it]
[2025-07-31 11:08:27,324: WARNING/ForkPoolWorker-16] 
  5%|4         | 23/494 [03:45<15:09,  1.93s/it]
[2025-07-31 11:08:28,816: WARNING/ForkPoolWorker-15] 
  5%|4         | 24/494 [03:55<17:27,  2.23s/it]
[2025-07-31 11:08:29,216: WARNING/ForkPoolWorker-1] 
  2%|2         | 12/494 [03:00<22:54,  2.85s/it]
[2025-07-31 11:08:29,304: WARNING/ForkPoolWorker-16] 
  5%|4         | 24/494 [03:47<15:14,  1.95s/it]
[2025-07-31 11:08:29,479: WARNING/ForkPoolWorker-2] 
  2%|1         | 8/494 [02:55<51:15,  6.33s/it]  
[2025-07-31 11:08:30,799: WARNING/ForkPoolWorker-15] 
  5%|5         | 25/494 [03:57<16:50,  2.15s/it]
[2025-07-31 11:08:31,011: WARNING/ForkPoolWorker-1] 
  3%|2         | 13/494 [03:02<20:17,  2.53s/it]
[2025-07-31 11:08:31,212: WARNING/ForkPoolWorker-16] 
  5%|5         | 25/494 [03:49<15:07,  1.93s/it]
[2025-07-31 11:08:31,611: WARNING/ForkPoolWorker-2] 
  2%|1         | 9/494 [02:57<40:32,  5.02s/it]
[2025-07-31 11:08:32,753: WARNING/ForkPoolWorker-15] 
  5%|5         | 26/494 [03:59<16:20,  2.09s/it]
[2025-07-31 11:08:32,989: WARNING/ForkPoolWorker-1] 
  3%|2         | 14/494 [03:04<18:54,  2.36s/it]
[2025-07-31 11:08:33,118: WARNING/ForkPoolWorker-16] 
  5%|5         | 26/494 [03:51<15:01,  1.93s/it]
[2025-07-31 11:08:33,655: WARNING/ForkPoolWorker-2] 
  2%|2         | 10/494 [02:59<33:03,  4.10s/it]
[2025-07-31 11:08:34,814: WARNING/ForkPoolWorker-16] 
  5%|5         | 27/494 [03:53<14:27,  1.86s/it]
[2025-07-31 11:08:35,029: WARNING/ForkPoolWorker-1] 
  3%|3         | 15/494 [03:06<18:05,  2.27s/it]
[2025-07-31 11:08:35,089: WARNING/ForkPoolWorker-15] 
  5%|5         | 27/494 [04:02<16:51,  2.17s/it]
[2025-07-31 11:08:35,936: WARNING/ForkPoolWorker-2] 
  2%|2         | 11/494 [03:01<28:30,  3.54s/it]
[2025-07-31 11:08:36,601: WARNING/ForkPoolWorker-16] 
  6%|5         | 28/494 [03:55<14:15,  1.84s/it]
[2025-07-31 11:08:36,974: WARNING/ForkPoolWorker-1] 
  3%|3         | 16/494 [03:08<17:17,  2.17s/it]
[2025-07-31 11:08:37,189: WARNING/ForkPoolWorker-15] 
  6%|5         | 28/494 [04:04<16:40,  2.15s/it]
[2025-07-31 11:08:38,170: WARNING/ForkPoolWorker-2] 
  2%|2         | 12/494 [03:04<25:14,  3.14s/it]
[2025-07-31 11:08:38,181: WARNING/ForkPoolWorker-16] 
  6%|5         | 29/494 [03:56<13:38,  1.76s/it]
[2025-07-31 11:08:38,705: WARNING/ForkPoolWorker-1] 
  3%|3         | 17/494 [03:10<16:11,  2.04s/it]
[2025-07-31 11:08:39,526: WARNING/ForkPoolWorker-15] 
  6%|5         | 29/494 [04:06<17:04,  2.20s/it]
[2025-07-31 11:08:40,083: WARNING/ForkPoolWorker-16] 
  6%|6         | 30/494 [03:58<13:56,  1.80s/it]
[2025-07-31 11:08:40,135: WARNING/ForkPoolWorker-2] 
  3%|2         | 13/494 [03:06<22:20,  2.79s/it]
[2025-07-31 11:08:40,417: WARNING/ForkPoolWorker-1] 
  4%|3         | 18/494 [03:12<15:23,  1.94s/it]
[2025-07-31 11:08:41,653: WARNING/ForkPoolWorker-15] 
  6%|6         | 30/494 [04:08<16:51,  2.18s/it]
[2025-07-31 11:08:41,926: WARNING/ForkPoolWorker-16] 
  6%|6         | 31/494 [04:00<14:00,  1.81s/it]
[2025-07-31 11:08:42,068: WARNING/ForkPoolWorker-1] 
  4%|3         | 19/494 [03:13<14:40,  1.85s/it]
[2025-07-31 11:08:42,287: WARNING/ForkPoolWorker-2] 
  3%|2         | 14/494 [03:08<20:46,  2.60s/it]
[2025-07-31 11:08:43,509: WARNING/ForkPoolWorker-15] 
  6%|6         | 31/494 [04:10<16:04,  2.08s/it]
[2025-07-31 11:08:44,013: WARNING/ForkPoolWorker-16] 
  6%|6         | 32/494 [04:02<14:35,  1.90s/it]
[2025-07-31 11:08:44,071: WARNING/ForkPoolWorker-1] 
  4%|4         | 20/494 [03:15<14:59,  1.90s/it]
[2025-07-31 11:08:44,582: WARNING/ForkPoolWorker-2] 
  3%|3         | 15/494 [03:10<19:59,  2.51s/it]
[2025-07-31 11:08:45,535: WARNING/ForkPoolWorker-15] 
  6%|6         | 32/494 [04:12<15:54,  2.07s/it]
[2025-07-31 11:08:45,809: WARNING/ForkPoolWorker-16] 
  7%|6         | 33/494 [04:04<14:20,  1.87s/it]
[2025-07-31 11:08:46,055: WARNING/ForkPoolWorker-1] 
  4%|4         | 21/494 [03:17<15:09,  1.92s/it]
[2025-07-31 11:08:46,708: WARNING/ForkPoolWorker-2] 
  3%|3         | 16/494 [03:12<19:02,  2.39s/it]
[2025-07-31 11:08:47,312: WARNING/ForkPoolWorker-15] 
  7%|6         | 33/494 [04:14<15:12,  1.98s/it]
[2025-07-31 11:08:47,487: WARNING/ForkPoolWorker-16] 
  7%|6         | 34/494 [04:05<13:52,  1.81s/it]
[2025-07-31 11:08:47,761: WARNING/ForkPoolWorker-1] 
  4%|4         | 22/494 [03:19<14:37,  1.86s/it]
[2025-07-31 11:08:48,868: WARNING/ForkPoolWorker-2] 
  3%|3         | 17/494 [03:14<18:27,  2.32s/it]
[2025-07-31 11:08:49,348: WARNING/ForkPoolWorker-15] 
  7%|6         | 34/494 [04:16<15:18,  2.00s/it]
[2025-07-31 11:08:49,427: WARNING/ForkPoolWorker-16] 
  7%|7         | 35/494 [04:07<14:08,  1.85s/it]
[2025-07-31 11:08:49,882: WARNING/ForkPoolWorker-1] 
  5%|4         | 23/494 [03:21<15:12,  1.94s/it]
[2025-07-31 11:08:51,088: WARNING/ForkPoolWorker-2] 
  4%|3         | 18/494 [03:17<18:10,  2.29s/it]
[2025-07-31 11:08:51,253: WARNING/ForkPoolWorker-15] 
  7%|7         | 35/494 [04:18<15:03,  1.97s/it]
[2025-07-31 11:08:51,347: WARNING/ForkPoolWorker-16] 
  7%|7         | 36/494 [04:09<14:16,  1.87s/it]
[2025-07-31 11:08:51,598: WARNING/ForkPoolWorker-1] 
  5%|4         | 24/494 [03:23<14:38,  1.87s/it]
[2025-07-31 11:08:53,032: WARNING/ForkPoolWorker-16] 
  7%|7         | 37/494 [04:11<13:49,  1.81s/it]
[2025-07-31 11:08:53,126: WARNING/ForkPoolWorker-1] 
  5%|5         | 25/494 [03:24<13:49,  1.77s/it]
[2025-07-31 11:08:53,358: WARNING/ForkPoolWorker-2] 
  4%|3         | 19/494 [03:19<18:05,  2.28s/it]
[2025-07-31 11:08:53,630: WARNING/ForkPoolWorker-15] 
  7%|7         | 36/494 [04:20<15:57,  2.09s/it]
[2025-07-31 11:08:54,671: WARNING/ForkPoolWorker-1] 
  5%|5         | 26/494 [03:26<13:16,  1.70s/it]
[2025-07-31 11:08:54,775: WARNING/ForkPoolWorker-16] 
  8%|7         | 38/494 [04:13<13:37,  1.79s/it]
[2025-07-31 11:08:55,462: WARNING/ForkPoolWorker-2] 
  4%|4         | 20/494 [03:21<17:37,  2.23s/it]
[2025-07-31 11:08:55,665: WARNING/ForkPoolWorker-15] 
  7%|7         | 37/494 [04:22<15:47,  2.07s/it]
[2025-07-31 11:08:56,513: WARNING/ForkPoolWorker-1] 
  5%|5         | 27/494 [03:28<13:34,  1.74s/it]
[2025-07-31 11:08:56,531: WARNING/ForkPoolWorker-16] 
  8%|7         | 39/494 [04:14<13:30,  1.78s/it]
[2025-07-31 11:08:57,734: WARNING/ForkPoolWorker-2] 
  4%|4         | 21/494 [03:23<17:40,  2.24s/it]
[2025-07-31 11:08:57,762: WARNING/ForkPoolWorker-15] 
  8%|7         | 38/494 [04:24<15:49,  2.08s/it]
[2025-07-31 11:08:58,364: WARNING/ForkPoolWorker-1] 
  6%|5         | 28/494 [03:30<13:47,  1.78s/it]
[2025-07-31 11:08:58,412: WARNING/ForkPoolWorker-16] 
  8%|8         | 40/494 [04:16<13:42,  1.81s/it]
[2025-07-31 11:08:59,753: WARNING/ForkPoolWorker-15] 
  8%|7         | 39/494 [04:26<15:34,  2.05s/it]
[2025-07-31 11:08:59,821: WARNING/ForkPoolWorker-2] 
  4%|4         | 22/494 [03:25<17:16,  2.20s/it]
[2025-07-31 11:09:00,354: WARNING/ForkPoolWorker-1] 
  6%|5         | 29/494 [03:32<14:15,  1.84s/it]
[2025-07-31 11:09:00,432: WARNING/ForkPoolWorker-16] 
  8%|8         | 41/494 [04:18<14:09,  1.87s/it]
[2025-07-31 11:09:01,881: WARNING/ForkPoolWorker-15] 
  8%|8         | 40/494 [04:29<15:42,  2.08s/it]
[2025-07-31 11:09:01,943: WARNING/ForkPoolWorker-1] 
  6%|6         | 30/494 [03:33<13:38,  1.76s/it]
[2025-07-31 11:09:02,016: WARNING/ForkPoolWorker-16] 
  9%|8         | 42/494 [04:20<13:27,  1.79s/it]
[2025-07-31 11:09:02,155: WARNING/ForkPoolWorker-2] 
  5%|4         | 23/494 [03:28<17:33,  2.24s/it]
[2025-07-31 11:09:03,494: WARNING/ForkPoolWorker-16] 
  9%|8         | 43/494 [04:21<12:44,  1.69s/it]
[2025-07-31 11:09:03,715: WARNING/ForkPoolWorker-1] 
  6%|6         | 31/494 [03:35<13:38,  1.77s/it]
[2025-07-31 11:09:04,206: WARNING/ForkPoolWorker-15] 
  8%|8         | 41/494 [04:31<16:14,  2.15s/it]
[2025-07-31 11:09:04,775: WARNING/ForkPoolWorker-2] 
  5%|4         | 24/494 [03:30<18:25,  2.35s/it]
[2025-07-31 11:09:05,048: WARNING/ForkPoolWorker-16] 
  9%|8         | 44/494 [04:23<12:23,  1.65s/it]
[2025-07-31 11:09:05,247: WARNING/ForkPoolWorker-1] 
  6%|6         | 32/494 [03:36<13:03,  1.70s/it]
[2025-07-31 11:09:06,618: WARNING/ForkPoolWorker-15] 
  9%|8         | 42/494 [04:33<16:47,  2.23s/it]
[2025-07-31 11:09:06,694: WARNING/ForkPoolWorker-16] 
  9%|9         | 45/494 [04:25<12:21,  1.65s/it]
[2025-07-31 11:09:06,856: WARNING/ForkPoolWorker-1] 
  7%|6         | 33/494 [03:38<12:49,  1.67s/it]
[2025-07-31 11:09:06,908: WARNING/ForkPoolWorker-2] 
  5%|5         | 25/494 [03:32<17:52,  2.29s/it]
[2025-07-31 11:09:08,262: WARNING/ForkPoolWorker-16] 
  9%|9         | 46/494 [04:26<12:08,  1.63s/it]
[2025-07-31 11:09:08,432: WARNING/ForkPoolWorker-1] 
  7%|6         | 34/494 [03:40<12:35,  1.64s/it]
[2025-07-31 11:09:08,940: WARNING/ForkPoolWorker-15] 
  9%|8         | 43/494 [04:36<16:57,  2.26s/it]
[2025-07-31 11:09:09,252: WARNING/ForkPoolWorker-2] 
  5%|5         | 26/494 [03:35<17:58,  2.30s/it]
[2025-07-31 11:09:09,701: WARNING/ForkPoolWorker-16] 
 10%|9         | 47/494 [04:28<11:41,  1.57s/it]
[2025-07-31 11:09:10,113: WARNING/ForkPoolWorker-1] 
  7%|7         | 35/494 [03:41<12:39,  1.65s/it]
[2025-07-31 11:09:11,217: WARNING/ForkPoolWorker-15] 
  9%|8         | 44/494 [04:38<16:57,  2.26s/it]
[2025-07-31 11:09:11,312: WARNING/ForkPoolWorker-16] 
 10%|9         | 48/494 [04:29<11:45,  1.58s/it]
[2025-07-31 11:09:11,565: WARNING/ForkPoolWorker-1] 
  7%|7         | 36/494 [03:43<12:09,  1.59s/it]
[2025-07-31 11:09:11,603: WARNING/ForkPoolWorker-2] 
  5%|5         | 27/494 [03:37<18:02,  2.32s/it]
[2025-07-31 11:09:12,880: WARNING/ForkPoolWorker-16] 
 10%|9         | 49/494 [04:31<11:42,  1.58s/it]
[2025-07-31 11:09:12,981: WARNING/ForkPoolWorker-1] 
  7%|7         | 37/494 [03:44<11:43,  1.54s/it]
[2025-07-31 11:09:13,638: WARNING/ForkPoolWorker-15] 
  9%|9         | 45/494 [04:40<17:17,  2.31s/it]
[2025-07-31 11:09:13,846: WARNING/ForkPoolWorker-2] 
  6%|5         | 28/494 [03:39<17:49,  2.30s/it]
[2025-07-31 11:09:14,513: WARNING/ForkPoolWorker-16] 
 10%|#         | 50/494 [04:32<11:47,  1.59s/it]
[2025-07-31 11:09:14,675: WARNING/ForkPoolWorker-1] 
  8%|7         | 38/494 [03:46<12:03,  1.59s/it]
[2025-07-31 11:09:16,049: WARNING/ForkPoolWorker-15] 
  9%|9         | 46/494 [04:43<17:28,  2.34s/it]
[2025-07-31 11:09:16,069: WARNING/ForkPoolWorker-16] 
 10%|#         | 51/494 [04:34<11:41,  1.58s/it]
[2025-07-31 11:09:16,156: WARNING/ForkPoolWorker-2] 
  6%|5         | 29/494 [03:42<17:49,  2.30s/it]
[2025-07-31 11:09:16,344: WARNING/ForkPoolWorker-1] 
  8%|7         | 39/494 [03:48<12:13,  1.61s/it]
[2025-07-31 11:09:17,714: WARNING/ForkPoolWorker-16] 
 11%|#         | 52/494 [04:36<11:47,  1.60s/it]
[2025-07-31 11:09:17,994: WARNING/ForkPoolWorker-1] 
  8%|8         | 40/494 [03:49<12:16,  1.62s/it]
[2025-07-31 11:09:18,339: WARNING/ForkPoolWorker-2] 
  6%|6         | 30/494 [03:44<17:30,  2.26s/it]
[2025-07-31 11:09:18,364: WARNING/ForkPoolWorker-15] 
 10%|9         | 47/494 [04:45<17:22,  2.33s/it]
[2025-07-31 11:09:19,378: WARNING/ForkPoolWorker-16] 
 11%|#         | 53/494 [04:37<11:54,  1.62s/it]
[2025-07-31 11:09:19,571: WARNING/ForkPoolWorker-1] 
  8%|8         | 41/494 [03:51<12:08,  1.61s/it]
[2025-07-31 11:09:20,456: WARNING/ForkPoolWorker-2] 
  6%|6         | 31/494 [03:46<17:08,  2.22s/it]
[2025-07-31 11:09:20,653: WARNING/ForkPoolWorker-15] 
 10%|9         | 48/494 [04:47<17:14,  2.32s/it]
[2025-07-31 11:09:21,048: WARNING/ForkPoolWorker-16] 
 11%|#         | 54/494 [04:39<11:59,  1.64s/it]
[2025-07-31 11:09:21,410: WARNING/ForkPoolWorker-1] 
  9%|8         | 42/494 [03:53<12:38,  1.68s/it]
[2025-07-31 11:09:22,526: WARNING/ForkPoolWorker-16] 
 11%|#1        | 55/494 [04:40<11:37,  1.59s/it]
[2025-07-31 11:09:22,910: WARNING/ForkPoolWorker-1] 
  9%|8         | 43/494 [03:54<12:12,  1.62s/it]
[2025-07-31 11:09:22,936: WARNING/ForkPoolWorker-2] 
  6%|6         | 32/494 [03:48<17:41,  2.30s/it]
[2025-07-31 11:09:23,073: WARNING/ForkPoolWorker-15] 
 10%|9         | 49/494 [04:50<17:25,  2.35s/it]
[2025-07-31 11:09:23,980: WARNING/ForkPoolWorker-16] 
 11%|#1        | 56/494 [04:42<11:17,  1.55s/it]
[2025-07-31 11:09:24,445: WARNING/ForkPoolWorker-1] 
  9%|8         | 44/494 [03:56<11:58,  1.60s/it]
[2025-07-31 11:09:25,345: WARNING/ForkPoolWorker-2] 
  7%|6         | 33/494 [03:51<17:54,  2.33s/it]
[2025-07-31 11:09:25,421: WARNING/ForkPoolWorker-16] 
 12%|#1        | 57/494 [04:43<11:02,  1.52s/it]
[2025-07-31 11:09:25,608: WARNING/ForkPoolWorker-15] 
 10%|#         | 50/494 [04:52<17:48,  2.41s/it]
[2025-07-31 11:09:26,053: WARNING/ForkPoolWorker-1] 
  9%|9         | 45/494 [03:57<11:58,  1.60s/it]
[2025-07-31 11:09:26,778: WARNING/ForkPoolWorker-16] 
 12%|#1        | 58/494 [04:45<10:40,  1.47s/it]
[2025-07-31 11:09:27,485: WARNING/ForkPoolWorker-1] 
  9%|9         | 46/494 [03:59<11:34,  1.55s/it]
[2025-07-31 11:09:27,672: WARNING/ForkPoolWorker-2] 
  7%|6         | 34/494 [03:53<17:51,  2.33s/it]
[2025-07-31 11:09:28,108: WARNING/ForkPoolWorker-16] 
 12%|#1        | 59/494 [04:46<10:20,  1.43s/it]
[2025-07-31 11:09:28,146: WARNING/ForkPoolWorker-15] 
 10%|#         | 51/494 [04:55<18:01,  2.44s/it]
[2025-07-31 11:09:28,881: WARNING/ForkPoolWorker-1] 
 10%|9         | 47/494 [04:00<11:12,  1.50s/it]
[2025-07-31 11:09:29,574: WARNING/ForkPoolWorker-16] 
 12%|#2        | 60/494 [04:47<10:24,  1.44s/it]
[2025-07-31 11:09:30,100: WARNING/ForkPoolWorker-2] 
  7%|7         | 35/494 [03:56<18:02,  2.36s/it]
[2025-07-31 11:09:30,368: WARNING/ForkPoolWorker-15] 
 11%|#         | 52/494 [04:57<17:31,  2.38s/it]
[2025-07-31 11:09:30,470: WARNING/ForkPoolWorker-1] 
 10%|9         | 48/494 [04:02<11:22,  1.53s/it]
[2025-07-31 11:09:31,201: WARNING/ForkPoolWorker-16] 
 12%|#2        | 61/494 [04:49<10:47,  1.50s/it]
[2025-07-31 11:09:32,185: WARNING/ForkPoolWorker-1] 
 10%|9         | 49/494 [04:03<11:45,  1.59s/it]
[2025-07-31 11:09:32,317: WARNING/ForkPoolWorker-2] 
  7%|7         | 36/494 [03:58<17:41,  2.32s/it]
[2025-07-31 11:09:32,661: WARNING/ForkPoolWorker-15] 
 11%|#         | 53/494 [04:59<17:17,  2.35s/it]
[2025-07-31 11:09:32,843: WARNING/ForkPoolWorker-16] 
 13%|#2        | 62/494 [04:51<11:04,  1.54s/it]
[2025-07-31 11:09:33,797: WARNING/ForkPoolWorker-1] 
 10%|#         | 50/494 [04:05<11:47,  1.59s/it]
[2025-07-31 11:09:34,501: WARNING/ForkPoolWorker-16] 
 13%|#2        | 63/494 [04:52<11:18,  1.57s/it]
[2025-07-31 11:09:34,616: WARNING/ForkPoolWorker-2] 
  7%|7         | 37/494 [04:00<17:36,  2.31s/it]
[2025-07-31 11:09:35,139: WARNING/ForkPoolWorker-15] 
 11%|#         | 54/494 [05:02<17:31,  2.39s/it]
[2025-07-31 11:09:35,219: WARNING/ForkPoolWorker-1] 
 10%|#         | 51/494 [04:06<11:23,  1.54s/it]
[2025-07-31 11:09:36,027: WARNING/ForkPoolWorker-16] 
 13%|#2        | 64/494 [04:54<11:10,  1.56s/it]
[2025-07-31 11:09:36,822: WARNING/ForkPoolWorker-2] 
  8%|7         | 38/494 [04:02<17:19,  2.28s/it]
[2025-07-31 11:09:36,831: WARNING/ForkPoolWorker-1] 
 11%|#         | 52/494 [04:08<11:30,  1.56s/it]
[2025-07-31 11:09:37,441: WARNING/ForkPoolWorker-15] 
 11%|#1        | 55/494 [05:04<17:17,  2.36s/it]
[2025-07-31 11:09:37,542: WARNING/ForkPoolWorker-16] 
 13%|#3        | 65/494 [04:55<11:03,  1.55s/it]
[2025-07-31 11:09:38,441: WARNING/ForkPoolWorker-1] 
 11%|#         | 53/494 [04:10<11:35,  1.58s/it]
[2025-07-31 11:09:38,965: WARNING/ForkPoolWorker-2] 
  8%|7         | 39/494 [04:04<16:58,  2.24s/it]
[2025-07-31 11:09:39,323: WARNING/ForkPoolWorker-16] 
 13%|#3        | 66/494 [04:57<11:31,  1.62s/it]
[2025-07-31 11:09:39,483: WARNING/ForkPoolWorker-15] 
 11%|#1        | 56/494 [05:06<16:33,  2.27s/it]
[2025-07-31 11:09:40,161: WARNING/ForkPoolWorker-1] 
 11%|#         | 54/494 [04:11<11:52,  1.62s/it]
[2025-07-31 11:09:40,806: WARNING/ForkPoolWorker-16] 
 14%|#3        | 67/494 [04:59<11:13,  1.58s/it]
[2025-07-31 11:09:41,195: WARNING/ForkPoolWorker-2] 
  8%|8         | 40/494 [04:07<16:55,  2.24s/it]
[2025-07-31 11:09:41,716: WARNING/ForkPoolWorker-15] 
 12%|#1        | 57/494 [05:08<16:26,  2.26s/it]
[2025-07-31 11:09:41,750: WARNING/ForkPoolWorker-1] 
 11%|#1        | 55/494 [04:13<11:47,  1.61s/it]
[2025-07-31 11:09:42,432: WARNING/ForkPoolWorker-16] 
 14%|#3        | 68/494 [05:00<11:17,  1.59s/it]
[2025-07-31 11:09:43,203: WARNING/ForkPoolWorker-1] 
 11%|#1        | 56/494 [04:14<11:24,  1.56s/it]
[2025-07-31 11:09:43,489: WARNING/ForkPoolWorker-2] 
  8%|8         | 41/494 [04:09<17:00,  2.25s/it]
[2025-07-31 11:09:43,922: WARNING/ForkPoolWorker-16] 
 14%|#3        | 69/494 [05:02<11:03,  1.56s/it]
[2025-07-31 11:09:43,999: WARNING/ForkPoolWorker-15] 
 12%|#1        | 58/494 [05:11<16:27,  2.27s/it]
[2025-07-31 11:09:44,817: WARNING/ForkPoolWorker-1] 
 12%|#1        | 57/494 [04:16<11:29,  1.58s/it]
[2025-07-31 11:09:45,663: WARNING/ForkPoolWorker-16] 
 14%|#4        | 70/494 [05:04<11:24,  1.62s/it]
[2025-07-31 11:09:45,900: WARNING/ForkPoolWorker-2] 
  9%|8         | 42/494 [04:11<17:19,  2.30s/it]
[2025-07-31 11:09:46,244: WARNING/ForkPoolWorker-15] 
 12%|#1        | 59/494 [05:13<16:22,  2.26s/it]
[2025-07-31 11:09:46,336: WARNING/ForkPoolWorker-1] 
 12%|#1        | 58/494 [04:18<11:20,  1.56s/it]
[2025-07-31 11:09:46,697: WARNING/ForkPoolWorker-16] 
 14%|#4        | 71/494 [05:05<10:09,  1.44s/it]
[2025-07-31 11:09:47,585: WARNING/ForkPoolWorker-2] 
  9%|8         | 43/494 [04:13<15:54,  2.12s/it]
[2025-07-31 11:09:47,897: WARNING/ForkPoolWorker-1] 
 12%|#1        | 59/494 [04:19<11:18,  1.56s/it]
[2025-07-31 11:09:48,222: WARNING/ForkPoolWorker-16] 
 15%|#4        | 72/494 [05:06<10:18,  1.47s/it]
[2025-07-31 11:09:48,531: WARNING/ForkPoolWorker-15] 
 12%|#2        | 60/494 [05:15<16:23,  2.27s/it]
[2025-07-31 11:09:49,058: WARNING/ForkPoolWorker-2] 
  9%|8         | 44/494 [04:15<14:25,  1.92s/it]
[2025-07-31 11:09:49,619: WARNING/ForkPoolWorker-1] 
 12%|#2        | 60/494 [04:21<11:38,  1.61s/it]
[2025-07-31 11:09:50,247: WARNING/ForkPoolWorker-16] 
 15%|#4        | 73/494 [05:08<11:27,  1.63s/it]
[2025-07-31 11:09:50,444: WARNING/ForkPoolWorker-15] 
 12%|#2        | 61/494 [05:17<15:35,  2.16s/it]
[2025-07-31 11:09:51,106: WARNING/ForkPoolWorker-2] 
  9%|9         | 45/494 [04:17<14:40,  1.96s/it]
[2025-07-31 11:09:51,208: WARNING/ForkPoolWorker-1] 
 12%|#2        | 61/494 [04:22<11:34,  1.60s/it]
[2025-07-31 11:09:51,892: WARNING/ForkPoolWorker-16] 
 15%|#4        | 74/494 [05:10<11:27,  1.64s/it]
[2025-07-31 11:09:52,786: WARNING/ForkPoolWorker-15] 
 13%|#2        | 62/494 [05:19<15:57,  2.22s/it]
[2025-07-31 11:09:52,889: WARNING/ForkPoolWorker-1] 
 13%|#2        | 62/494 [04:24<11:42,  1.63s/it]
[2025-07-31 11:09:53,198: WARNING/ForkPoolWorker-2] 
  9%|9         | 46/494 [04:19<14:55,  2.00s/it]
[2025-07-31 11:09:53,673: WARNING/ForkPoolWorker-16] 
 15%|#5        | 75/494 [05:12<11:44,  1.68s/it]
[2025-07-31 11:09:54,467: WARNING/ForkPoolWorker-1] 
 13%|#2        | 63/494 [04:26<11:34,  1.61s/it]
[2025-07-31 11:09:55,181: WARNING/ForkPoolWorker-16] 
 15%|#5        | 76/494 [05:13<11:20,  1.63s/it]
[2025-07-31 11:09:55,183: WARNING/ForkPoolWorker-15] 
 13%|#2        | 63/494 [05:22<16:18,  2.27s/it]
[2025-07-31 11:09:55,317: WARNING/ForkPoolWorker-1] 
 13%|#2        | 64/494 [04:26<09:54,  1.38s/it]
[2025-07-31 11:09:55,444: WARNING/ForkPoolWorker-2] 
 10%|9         | 47/494 [04:21<15:26,  2.07s/it]
[2025-07-31 11:09:56,911: WARNING/ForkPoolWorker-16] 
 16%|#5        | 77/494 [05:15<11:31,  1.66s/it]
[2025-07-31 11:09:56,975: WARNING/ForkPoolWorker-1] 
 13%|#3        | 65/494 [04:28<10:28,  1.47s/it]
[2025-07-31 11:09:57,457: WARNING/ForkPoolWorker-15] 
 13%|#2        | 64/494 [05:24<16:16,  2.27s/it]
[2025-07-31 11:09:57,697: WARNING/ForkPoolWorker-2] 
 10%|9         | 48/494 [04:23<15:48,  2.13s/it]
[2025-07-31 11:09:58,541: WARNING/ForkPoolWorker-16] 
 16%|#5        | 78/494 [05:16<11:26,  1.65s/it]
[2025-07-31 11:09:58,657: WARNING/ForkPoolWorker-1] 
 13%|#3        | 66/494 [04:30<10:55,  1.53s/it]
[2025-07-31 11:09:59,790: WARNING/ForkPoolWorker-15] 
 13%|#3        | 65/494 [05:26<16:22,  2.29s/it]
[2025-07-31 11:10:00,014: WARNING/ForkPoolWorker-1] 
 14%|#3        | 67/494 [04:31<10:31,  1.48s/it]
[2025-07-31 11:10:00,023: WARNING/ForkPoolWorker-2] 
 10%|9         | 49/494 [04:26<16:13,  2.19s/it]
[2025-07-31 11:10:00,152: WARNING/ForkPoolWorker-16] 
 16%|#5        | 79/494 [05:18<11:19,  1.64s/it]
[2025-07-31 11:10:01,898: WARNING/ForkPoolWorker-15] 
 13%|#3        | 66/494 [05:29<15:56,  2.24s/it]
[2025-07-31 11:10:01,907: WARNING/ForkPoolWorker-1] 
 14%|#3        | 68/494 [04:33<11:22,  1.60s/it]
[2025-07-31 11:10:01,984: WARNING/ForkPoolWorker-2] 
 10%|#         | 50/494 [04:28<15:40,  2.12s/it]
[2025-07-31 11:10:02,092: WARNING/ForkPoolWorker-16] 
 16%|#6        | 80/494 [05:20<11:55,  1.73s/it]
[2025-07-31 11:10:03,736: WARNING/ForkPoolWorker-1] 
 14%|#3        | 69/494 [04:35<11:50,  1.67s/it]
[2025-07-31 11:10:03,925: WARNING/ForkPoolWorker-16] 
 16%|#6        | 81/494 [05:22<12:07,  1.76s/it]
[2025-07-31 11:10:04,144: WARNING/ForkPoolWorker-2] 
 10%|#         | 51/494 [04:30<15:44,  2.13s/it]
[2025-07-31 11:10:04,171: WARNING/ForkPoolWorker-15] 
 14%|#3        | 67/494 [05:31<15:59,  2.25s/it]
[2025-07-31 11:10:05,278: WARNING/ForkPoolWorker-1] 
 14%|#4        | 70/494 [04:36<11:32,  1.63s/it]
[2025-07-31 11:10:05,489: WARNING/ForkPoolWorker-16] 
 17%|#6        | 82/494 [05:23<11:40,  1.70s/it]
[2025-07-31 11:10:06,203: WARNING/ForkPoolWorker-15] 
 14%|#3        | 68/494 [05:33<15:29,  2.18s/it]
[2025-07-31 11:10:06,347: WARNING/ForkPoolWorker-2] 
 11%|#         | 52/494 [04:32<15:51,  2.15s/it]
[2025-07-31 11:10:06,895: WARNING/ForkPoolWorker-1] 
 14%|#4        | 71/494 [04:38<11:27,  1.63s/it]
[2025-07-31 11:10:07,020: WARNING/ForkPoolWorker-16] 
 17%|#6        | 83/494 [05:25<11:18,  1.65s/it]
[2025-07-31 11:10:08,409: WARNING/ForkPoolWorker-2] 
 11%|#         | 53/494 [04:34<15:37,  2.13s/it]
[2025-07-31 11:10:08,499: WARNING/ForkPoolWorker-15] 
 14%|#3        | 69/494 [05:35<15:41,  2.22s/it]
[2025-07-31 11:10:08,676: WARNING/ForkPoolWorker-16] 
 17%|#7        | 84/494 [05:27<11:17,  1.65s/it]
[2025-07-31 11:10:08,744: WARNING/ForkPoolWorker-1] 
 15%|#4        | 72/494 [04:40<11:55,  1.69s/it]
[2025-07-31 11:10:10,411: WARNING/ForkPoolWorker-1] 
 15%|#4        | 73/494 [04:42<11:49,  1.69s/it]
[2025-07-31 11:10:10,417: WARNING/ForkPoolWorker-16] 
 17%|#7        | 85/494 [05:28<11:26,  1.68s/it]
[2025-07-31 11:10:10,555: WARNING/ForkPoolWorker-2] 
 11%|#         | 54/494 [04:36<15:37,  2.13s/it]
[2025-07-31 11:10:10,589: WARNING/ForkPoolWorker-15] 
 14%|#4        | 70/494 [05:37<15:23,  2.18s/it]
[2025-07-31 11:10:11,848: WARNING/ForkPoolWorker-16] 
 17%|#7        | 86/494 [05:30<10:54,  1.60s/it]
[2025-07-31 11:10:11,861: WARNING/ForkPoolWorker-1] 
 15%|#4        | 74/494 [04:43<11:18,  1.62s/it]
[2025-07-31 11:10:12,715: WARNING/ForkPoolWorker-2] 
 11%|#1        | 55/494 [04:38<15:39,  2.14s/it]
[2025-07-31 11:10:13,014: WARNING/ForkPoolWorker-15] 
 14%|#4        | 71/494 [05:40<15:52,  2.25s/it]
[2025-07-31 11:10:13,360: WARNING/ForkPoolWorker-16] 
 18%|#7        | 87/494 [05:31<10:41,  1.58s/it]
[2025-07-31 11:10:13,528: WARNING/ForkPoolWorker-1] 
 15%|#5        | 75/494 [04:45<11:23,  1.63s/it]
[2025-07-31 11:10:14,931: WARNING/ForkPoolWorker-2] 
 11%|#1        | 56/494 [04:40<15:47,  2.16s/it]
[2025-07-31 11:10:15,067: WARNING/ForkPoolWorker-15] 
 15%|#4        | 72/494 [05:42<15:25,  2.19s/it]
[2025-07-31 11:10:15,239: WARNING/ForkPoolWorker-16] 
 18%|#7        | 88/494 [05:33<11:16,  1.67s/it]
[2025-07-31 11:10:15,498: WARNING/ForkPoolWorker-1] 
 15%|#5        | 76/494 [04:47<12:02,  1.73s/it]
[2025-07-31 11:10:16,835: WARNING/ForkPoolWorker-16] 
 18%|#8        | 89/494 [05:35<11:06,  1.65s/it]
[2025-07-31 11:10:17,021: WARNING/ForkPoolWorker-1] 
 16%|#5        | 77/494 [04:48<11:36,  1.67s/it]
[2025-07-31 11:10:17,126: WARNING/ForkPoolWorker-2] 
 12%|#1        | 57/494 [04:43<15:49,  2.17s/it]
[2025-07-31 11:10:17,304: WARNING/ForkPoolWorker-15] 
 15%|#4        | 73/494 [05:44<15:28,  2.21s/it]
[2025-07-31 11:10:18,382: WARNING/ForkPoolWorker-1] 
 16%|#5        | 78/494 [04:50<10:56,  1.58s/it]
[2025-07-31 11:10:18,535: WARNING/ForkPoolWorker-16] 
 18%|#8        | 90/494 [05:36<11:11,  1.66s/it]
[2025-07-31 11:10:19,458: WARNING/ForkPoolWorker-2] 
 12%|#1        | 58/494 [04:45<16:08,  2.22s/it]
[2025-07-31 11:10:19,653: WARNING/ForkPoolWorker-15] 
 15%|#4        | 74/494 [05:46<15:44,  2.25s/it]
[2025-07-31 11:10:19,893: WARNING/ForkPoolWorker-1] 
 16%|#5        | 79/494 [04:51<10:46,  1.56s/it]
[2025-07-31 11:10:20,206: WARNING/ForkPoolWorker-16] 
 18%|#8        | 91/494 [05:38<11:10,  1.66s/it]
[2025-07-31 11:10:21,423: WARNING/ForkPoolWorker-1] 
 16%|#6        | 80/494 [04:53<10:41,  1.55s/it]
[2025-07-31 11:10:21,682: WARNING/ForkPoolWorker-16] 
 19%|#8        | 92/494 [05:40<10:46,  1.61s/it]
[2025-07-31 11:10:21,768: WARNING/ForkPoolWorker-2] 
 12%|#1        | 59/494 [04:47<16:17,  2.25s/it]
[2025-07-31 11:10:21,905: WARNING/ForkPoolWorker-15] 
 15%|#5        | 75/494 [05:49<15:42,  2.25s/it]
[2025-07-31 11:10:23,128: WARNING/ForkPoolWorker-1] 
 16%|#6        | 81/494 [04:54<10:59,  1.60s/it]
[2025-07-31 11:10:23,365: WARNING/ForkPoolWorker-16] 
 19%|#8        | 93/494 [05:41<10:53,  1.63s/it]
[2025-07-31 11:10:23,947: WARNING/ForkPoolWorker-2] 
 12%|#2        | 60/494 [04:49<16:06,  2.23s/it]
[2025-07-31 11:10:24,144: WARNING/ForkPoolWorker-15] 
 15%|#5        | 76/494 [05:51<15:38,  2.25s/it]
[2025-07-31 11:10:24,939: WARNING/ForkPoolWorker-1] 
 17%|#6        | 82/494 [04:56<11:24,  1.66s/it]
[2025-07-31 11:10:24,989: WARNING/ForkPoolWorker-16] 
 19%|#9        | 94/494 [05:43<10:51,  1.63s/it]
[2025-07-31 11:10:26,005: WARNING/ForkPoolWorker-2] 
 12%|#2        | 61/494 [04:52<15:42,  2.18s/it]
[2025-07-31 11:10:26,252: WARNING/ForkPoolWorker-15] 
 16%|#5        | 77/494 [05:53<15:19,  2.21s/it]
[2025-07-31 11:10:26,795: WARNING/ForkPoolWorker-1] 
 17%|#6        | 83/494 [04:58<11:46,  1.72s/it]
[2025-07-31 11:10:26,798: WARNING/ForkPoolWorker-16] 
 19%|#9        | 95/494 [05:45<11:11,  1.68s/it]
[2025-07-31 11:10:28,139: WARNING/ForkPoolWorker-2] 
 13%|#2        | 62/494 [04:54<15:34,  2.16s/it]
[2025-07-31 11:10:28,358: WARNING/ForkPoolWorker-15] 
 16%|#5        | 78/494 [05:55<15:04,  2.18s/it]
[2025-07-31 11:10:28,436: WARNING/ForkPoolWorker-16] 
 19%|#9        | 96/494 [05:46<11:04,  1.67s/it]
[2025-07-31 11:10:28,499: WARNING/ForkPoolWorker-1] 
 17%|#7        | 84/494 [05:00<11:42,  1.71s/it]
[2025-07-31 11:10:29,973: WARNING/ForkPoolWorker-16] 
 20%|#9        | 97/494 [05:48<10:46,  1.63s/it]
[2025-07-31 11:10:30,262: WARNING/ForkPoolWorker-1] 
 17%|#7        | 85/494 [05:01<11:47,  1.73s/it]
[2025-07-31 11:10:30,427: WARNING/ForkPoolWorker-2] 
 13%|#2        | 63/494 [04:56<15:48,  2.20s/it]
[2025-07-31 11:10:30,610: WARNING/ForkPoolWorker-15] 
 16%|#5        | 79/494 [05:57<15:12,  2.20s/it]
[2025-07-31 11:10:31,638: WARNING/ForkPoolWorker-16] 
 20%|#9        | 98/494 [05:50<10:49,  1.64s/it]
[2025-07-31 11:10:31,839: WARNING/ForkPoolWorker-1] 
 17%|#7        | 86/494 [05:03<11:26,  1.68s/it]
[2025-07-31 11:10:32,541: WARNING/ForkPoolWorker-2] 
 13%|#2        | 64/494 [04:58<15:35,  2.17s/it]
[2025-07-31 11:10:32,853: WARNING/ForkPoolWorker-15] 
 16%|#6        | 80/494 [06:00<15:15,  2.21s/it]
[2025-07-31 11:10:33,425: WARNING/ForkPoolWorker-16] 
 20%|##        | 99/494 [05:51<11:05,  1.68s/it]
[2025-07-31 11:10:33,495: WARNING/ForkPoolWorker-1] 
 18%|#7        | 87/494 [05:05<11:21,  1.68s/it]
[2025-07-31 11:10:34,548: WARNING/ForkPoolWorker-2] 
 13%|#3        | 65/494 [05:00<15:11,  2.12s/it]
[2025-07-31 11:10:35,033: WARNING/ForkPoolWorker-15] 
 16%|#6        | 81/494 [06:02<15:09,  2.20s/it]
[2025-07-31 11:10:35,101: WARNING/ForkPoolWorker-16] 
 20%|##        | 100/494 [05:53<11:02,  1.68s/it]
[2025-07-31 11:10:35,273: WARNING/ForkPoolWorker-1] 
 18%|#7        | 88/494 [05:06<11:32,  1.71s/it]
[2025-07-31 11:10:36,673: WARNING/ForkPoolWorker-2] 
 13%|#3        | 66/494 [05:02<15:09,  2.12s/it]
[2025-07-31 11:10:36,816: WARNING/ForkPoolWorker-16] 
 20%|##        | 101/494 [05:55<11:04,  1.69s/it]
[2025-07-31 11:10:36,983: WARNING/ForkPoolWorker-1] 
 18%|#8        | 89/494 [05:08<11:31,  1.71s/it]
[2025-07-31 11:10:37,269: WARNING/ForkPoolWorker-15] 
 17%|#6        | 82/494 [06:04<15:11,  2.21s/it]
[2025-07-31 11:10:37,990: WARNING/ForkPoolWorker-2] 
 14%|#3        | 67/494 [05:04<13:23,  1.88s/it]
[2025-07-31 11:10:38,646: WARNING/ForkPoolWorker-1] 
 18%|#8        | 90/494 [05:10<11:24,  1.69s/it]
[2025-07-31 11:10:38,736: WARNING/ForkPoolWorker-16] 
 21%|##        | 102/494 [05:57<11:29,  1.76s/it]
[2025-07-31 11:10:39,442: WARNING/ForkPoolWorker-15] 
 17%|#6        | 83/494 [06:06<15:04,  2.20s/it]
[2025-07-31 11:10:39,901: WARNING/ForkPoolWorker-2] 
 14%|#3        | 68/494 [05:05<13:25,  1.89s/it]
[2025-07-31 11:10:40,356: WARNING/ForkPoolWorker-1] 
 18%|#8        | 91/494 [05:12<11:24,  1.70s/it]
[2025-07-31 11:10:40,552: WARNING/ForkPoolWorker-16] 
 21%|##        | 103/494 [05:58<11:35,  1.78s/it]
[2025-07-31 11:10:41,552: WARNING/ForkPoolWorker-15] 
 17%|#7        | 84/494 [06:08<14:51,  2.17s/it]
[2025-07-31 11:10:41,996: WARNING/ForkPoolWorker-2] 
 14%|#3        | 69/494 [05:08<13:49,  1.95s/it]
[2025-07-31 11:10:42,189: WARNING/ForkPoolWorker-1] 
 19%|#8        | 92/494 [05:13<11:39,  1.74s/it]
[2025-07-31 11:10:42,528: WARNING/ForkPoolWorker-16] 
 21%|##1       | 104/494 [06:00<11:56,  1.84s/it]
[2025-07-31 11:10:43,644: WARNING/ForkPoolWorker-15] 
 17%|#7        | 85/494 [06:10<14:38,  2.15s/it]
[2025-07-31 11:10:44,047: WARNING/ForkPoolWorker-1] 
 19%|#8        | 93/494 [05:15<11:51,  1.77s/it]
[2025-07-31 11:10:44,109: WARNING/ForkPoolWorker-2] 
 14%|#4        | 70/494 [05:10<14:08,  2.00s/it]
[2025-07-31 11:10:44,478: WARNING/ForkPoolWorker-16] 
 21%|##1       | 105/494 [06:02<12:07,  1.87s/it]
[2025-07-31 11:10:45,724: WARNING/ForkPoolWorker-15] 
 17%|#7        | 86/494 [06:12<14:28,  2.13s/it]
[2025-07-31 11:10:46,006: WARNING/ForkPoolWorker-1] 
 19%|#9        | 94/494 [05:17<12:11,  1.83s/it]
[2025-07-31 11:10:46,088: WARNING/ForkPoolWorker-2] 
 14%|#4        | 71/494 [05:12<14:03,  1.99s/it]
[2025-07-31 11:10:46,547: WARNING/ForkPoolWorker-16] 
 21%|##1       | 106/494 [06:04<12:28,  1.93s/it]
[2025-07-31 11:10:47,888: WARNING/ForkPoolWorker-15] 
 18%|#7        | 87/494 [06:15<14:30,  2.14s/it]
[2025-07-31 11:10:48,123: WARNING/ForkPoolWorker-1] 
 19%|#9        | 95/494 [05:19<12:44,  1.92s/it]
[2025-07-31 11:10:48,161: WARNING/ForkPoolWorker-2] 
 15%|#4        | 72/494 [05:14<14:11,  2.02s/it]
[2025-07-31 11:10:48,625: WARNING/ForkPoolWorker-16] 
 22%|##1       | 107/494 [06:07<12:44,  1.97s/it]
[2025-07-31 11:10:49,959: WARNING/ForkPoolWorker-15] 
 18%|#7        | 88/494 [06:17<14:20,  2.12s/it]
[2025-07-31 11:10:50,125: WARNING/ForkPoolWorker-1] 
 19%|#9        | 96/494 [05:21<12:52,  1.94s/it]
[2025-07-31 11:10:50,208: WARNING/ForkPoolWorker-2] 
 15%|#4        | 73/494 [05:16<14:13,  2.03s/it]
[2025-07-31 11:10:50,510: WARNING/ForkPoolWorker-16] 
 22%|##1       | 108/494 [06:08<12:31,  1.95s/it]
[2025-07-31 11:10:51,930: WARNING/ForkPoolWorker-1] 
 20%|#9        | 97/494 [05:23<12:34,  1.90s/it]
[2025-07-31 11:10:52,100: WARNING/ForkPoolWorker-15] 
 18%|#8        | 89/494 [06:19<14:20,  2.13s/it]
[2025-07-31 11:10:52,148: WARNING/ForkPoolWorker-2] 
 15%|#4        | 74/494 [05:18<14:00,  2.00s/it]
[2025-07-31 11:10:52,229: WARNING/ForkPoolWorker-16] 
 22%|##2       | 109/494 [06:10<12:03,  1.88s/it]
[2025-07-31 11:10:53,603: WARNING/ForkPoolWorker-1] 
 20%|#9        | 98/494 [05:25<12:05,  1.83s/it]
[2025-07-31 11:10:53,868: WARNING/ForkPoolWorker-16] 
 22%|##2       | 110/494 [06:12<11:33,  1.81s/it]
[2025-07-31 11:10:54,260: WARNING/ForkPoolWorker-2] 
 15%|#5        | 75/494 [05:20<14:12,  2.03s/it]
[2025-07-31 11:10:54,365: WARNING/ForkPoolWorker-15] 
 18%|#8        | 90/494 [06:21<14:35,  2.17s/it]
[2025-07-31 11:10:55,361: WARNING/ForkPoolWorker-1] 
 20%|##        | 99/494 [05:27<11:55,  1.81s/it]
[2025-07-31 11:10:55,720: WARNING/ForkPoolWorker-16] 
 22%|##2       | 111/494 [06:14<11:37,  1.82s/it]
[2025-07-31 11:10:56,384: WARNING/ForkPoolWorker-2] 
 15%|#5        | 76/494 [05:22<14:21,  2.06s/it]
[2025-07-31 11:10:56,480: WARNING/ForkPoolWorker-15] 
 18%|#8        | 91/494 [06:23<14:27,  2.15s/it]
[2025-07-31 11:10:57,183: WARNING/ForkPoolWorker-1] 
 20%|##        | 100/494 [05:28<11:54,  1.81s/it]
[2025-07-31 11:10:57,453: WARNING/ForkPoolWorker-16] 
 23%|##2       | 112/494 [06:15<11:25,  1.79s/it]
[2025-07-31 11:10:58,599: WARNING/ForkPoolWorker-15] 
 19%|#8        | 92/494 [06:25<14:20,  2.14s/it]
[2025-07-31 11:10:58,602: WARNING/ForkPoolWorker-2] 
 16%|#5        | 77/494 [05:24<14:39,  2.11s/it]
[2025-07-31 11:10:59,011: WARNING/ForkPoolWorker-1] 
 20%|##        | 101/494 [05:30<11:54,  1.82s/it]
[2025-07-31 11:10:59,525: WARNING/ForkPoolWorker-16] 
 23%|##2       | 113/494 [06:17<11:55,  1.88s/it]
[2025-07-31 11:11:00,674: WARNING/ForkPoolWorker-2] 
 16%|#5        | 78/494 [05:26<14:29,  2.09s/it]
[2025-07-31 11:11:00,737: WARNING/ForkPoolWorker-15] 
 19%|#8        | 93/494 [06:27<14:18,  2.14s/it]
[2025-07-31 11:11:00,918: WARNING/ForkPoolWorker-1] 
 21%|##        | 102/494 [05:32<12:03,  1.84s/it]
[2025-07-31 11:11:01,265: WARNING/ForkPoolWorker-16] 
 23%|##3       | 114/494 [06:19<11:37,  1.84s/it]
[2025-07-31 11:11:02,550: WARNING/ForkPoolWorker-2] 
 16%|#5        | 79/494 [05:28<14:03,  2.03s/it]
[2025-07-31 11:11:02,566: WARNING/ForkPoolWorker-1] 
 21%|##        | 103/494 [05:34<11:38,  1.79s/it]
[2025-07-31 11:11:02,986: WARNING/ForkPoolWorker-15] 
 19%|#9        | 94/494 [06:30<14:29,  2.17s/it]
[2025-07-31 11:11:02,994: WARNING/ForkPoolWorker-16] 
 23%|##3       | 115/494 [06:21<11:23,  1.80s/it]
[2025-07-31 11:11:04,169: WARNING/ForkPoolWorker-1] 
 21%|##1       | 104/494 [05:35<11:15,  1.73s/it]
[2025-07-31 11:11:04,478: WARNING/ForkPoolWorker-2] 
 16%|#6        | 80/494 [05:30<13:48,  2.00s/it]
[2025-07-31 11:11:04,664: WARNING/ForkPoolWorker-16] 
 23%|##3       | 116/494 [06:23<11:06,  1.76s/it]
[2025-07-31 11:11:05,225: WARNING/ForkPoolWorker-15] 
 19%|#9        | 95/494 [06:32<14:34,  2.19s/it]
[2025-07-31 11:11:05,948: WARNING/ForkPoolWorker-1] 
 21%|##1       | 105/494 [05:37<11:18,  1.75s/it]
[2025-07-31 11:11:06,469: WARNING/ForkPoolWorker-2] 
 16%|#6        | 81/494 [05:32<13:45,  2.00s/it]
[2025-07-31 11:11:06,542: WARNING/ForkPoolWorker-16] 
 24%|##3       | 117/494 [06:24<11:17,  1.80s/it]
[2025-07-31 11:11:07,447: WARNING/ForkPoolWorker-15] 
 19%|#9        | 96/494 [06:34<14:36,  2.20s/it]
[2025-07-31 11:11:07,644: WARNING/ForkPoolWorker-1] 
 21%|##1       | 106/494 [05:39<11:11,  1.73s/it]
[2025-07-31 11:11:08,480: WARNING/ForkPoolWorker-2] 
 17%|#6        | 82/494 [05:34<13:44,  2.00s/it]
[2025-07-31 11:11:08,567: WARNING/ForkPoolWorker-16] 
 24%|##3       | 118/494 [06:26<11:41,  1.87s/it]
[2025-07-31 11:11:09,626: WARNING/ForkPoolWorker-15] 
 20%|#9        | 97/494 [06:36<14:31,  2.19s/it]
[2025-07-31 11:11:09,695: WARNING/ForkPoolWorker-1] 
 22%|##1       | 107/494 [05:41<11:46,  1.83s/it]
[2025-07-31 11:11:10,431: WARNING/ForkPoolWorker-2] 
 17%|#6        | 83/494 [05:36<13:36,  1.99s/it]
[2025-07-31 11:11:10,460: WARNING/ForkPoolWorker-16] 
 24%|##4       | 119/494 [06:28<11:42,  1.87s/it]
[2025-07-31 11:11:11,510: WARNING/ForkPoolWorker-1] 
 22%|##1       | 108/494 [05:43<11:43,  1.82s/it]
[2025-07-31 11:11:11,819: WARNING/ForkPoolWorker-15] 
 20%|#9        | 98/494 [06:38<14:28,  2.19s/it]
[2025-07-31 11:11:12,301: WARNING/ForkPoolWorker-16] 
 24%|##4       | 120/494 [06:30<11:37,  1.86s/it]
[2025-07-31 11:11:12,368: WARNING/ForkPoolWorker-2] 
 17%|#7        | 84/494 [05:38<13:28,  1.97s/it]
[2025-07-31 11:11:13,415: WARNING/ForkPoolWorker-1] 
 22%|##2       | 109/494 [05:45<11:51,  1.85s/it]
[2025-07-31 11:11:13,850: WARNING/ForkPoolWorker-15] 
 20%|##        | 99/494 [06:41<14:07,  2.15s/it]
[2025-07-31 11:11:14,060: WARNING/ForkPoolWorker-16] 
 24%|##4       | 121/494 [06:32<11:23,  1.83s/it]
[2025-07-31 11:11:14,430: WARNING/ForkPoolWorker-2] 
 17%|#7        | 85/494 [05:40<13:37,  2.00s/it]
[2025-07-31 11:11:15,383: WARNING/ForkPoolWorker-1] 
 22%|##2       | 110/494 [05:47<12:03,  1.88s/it]
[2025-07-31 11:11:15,984: WARNING/ForkPoolWorker-15] 
 20%|##        | 100/494 [06:43<14:03,  2.14s/it]
[2025-07-31 11:11:16,083: WARNING/ForkPoolWorker-16] 
 25%|##4       | 122/494 [06:34<11:43,  1.89s/it]
[2025-07-31 11:11:16,424: WARNING/ForkPoolWorker-2] 
 17%|#7        | 86/494 [05:42<13:34,  2.00s/it]
[2025-07-31 11:11:17,369: WARNING/ForkPoolWorker-1] 
 22%|##2       | 111/494 [05:49<12:13,  1.91s/it]
[2025-07-31 11:11:17,919: WARNING/ForkPoolWorker-16] 
 25%|##4       | 123/494 [06:36<11:35,  1.87s/it]
[2025-07-31 11:11:18,131: WARNING/ForkPoolWorker-2] 
 18%|#7        | 87/494 [05:44<12:57,  1.91s/it]
[2025-07-31 11:11:18,213: WARNING/ForkPoolWorker-15] 
 20%|##        | 101/494 [06:45<14:12,  2.17s/it]
[2025-07-31 11:11:19,144: WARNING/ForkPoolWorker-1] 
 23%|##2       | 112/494 [05:50<11:55,  1.87s/it]
[2025-07-31 11:11:19,739: WARNING/ForkPoolWorker-16] 
 25%|##5       | 124/494 [06:38<11:27,  1.86s/it]
[2025-07-31 11:11:19,901: WARNING/ForkPoolWorker-2] 
 18%|#7        | 88/494 [05:45<12:38,  1.87s/it]
[2025-07-31 11:11:20,354: WARNING/ForkPoolWorker-15] 
 21%|##        | 102/494 [06:47<14:06,  2.16s/it]
[2025-07-31 11:11:20,987: WARNING/ForkPoolWorker-1] 
 23%|##2       | 113/494 [05:52<11:50,  1.86s/it]
[2025-07-31 11:11:21,566: WARNING/ForkPoolWorker-16] 
 25%|##5       | 125/494 [06:39<11:22,  1.85s/it]
[2025-07-31 11:11:21,616: WARNING/ForkPoolWorker-2] 
 18%|#8        | 89/494 [05:47<12:17,  1.82s/it]
[2025-07-31 11:11:22,426: WARNING/ForkPoolWorker-15] 
 21%|##        | 103/494 [06:49<13:54,  2.13s/it]
[2025-07-31 11:11:22,883: WARNING/ForkPoolWorker-1] 
 23%|##3       | 114/494 [05:54<11:51,  1.87s/it]
[2025-07-31 11:11:23,496: WARNING/ForkPoolWorker-16] 
 26%|##5       | 126/494 [06:41<11:29,  1.87s/it]
[2025-07-31 11:11:23,515: WARNING/ForkPoolWorker-2] 
 18%|#8        | 90/494 [05:49<12:25,  1.85s/it]
[2025-07-31 11:11:24,469: WARNING/ForkPoolWorker-15] 
 21%|##1       | 104/494 [06:51<13:41,  2.11s/it]
[2025-07-31 11:11:24,900: WARNING/ForkPoolWorker-1] 
 23%|##3       | 115/494 [05:56<12:06,  1.92s/it]
[2025-07-31 11:11:25,525: WARNING/ForkPoolWorker-16] 
 26%|##5       | 127/494 [06:43<11:44,  1.92s/it]
[2025-07-31 11:11:25,527: WARNING/ForkPoolWorker-2] 
 18%|#8        | 91/494 [05:51<12:43,  1.90s/it]
[2025-07-31 11:11:26,417: WARNING/ForkPoolWorker-15] 
 21%|##1       | 105/494 [06:53<13:20,  2.06s/it]
[2025-07-31 11:11:26,969: WARNING/ForkPoolWorker-1] 
 23%|##3       | 116/494 [05:58<12:21,  1.96s/it]
[2025-07-31 11:11:27,495: WARNING/ForkPoolWorker-2] 
 19%|#8        | 92/494 [05:53<12:50,  1.92s/it]
[2025-07-31 11:11:27,587: WARNING/ForkPoolWorker-16] 
 26%|##5       | 128/494 [06:45<11:58,  1.96s/it]
[2025-07-31 11:11:28,471: WARNING/ForkPoolWorker-15] 
 21%|##1       | 106/494 [06:55<13:18,  2.06s/it]
[2025-07-31 11:11:28,922: WARNING/ForkPoolWorker-1] 
 24%|##3       | 117/494 [06:00<12:17,  1.96s/it]
[2025-07-31 11:11:29,414: WARNING/ForkPoolWorker-2] 
 19%|#8        | 93/494 [05:55<12:48,  1.92s/it]
[2025-07-31 11:11:29,419: WARNING/ForkPoolWorker-16] 
 26%|##6       | 129/494 [06:47<11:42,  1.92s/it]
[2025-07-31 11:11:30,446: WARNING/ForkPoolWorker-15] 
 22%|##1       | 107/494 [06:57<13:06,  2.03s/it]
[2025-07-31 11:11:31,044: WARNING/ForkPoolWorker-1] 
 24%|##3       | 118/494 [06:02<12:35,  2.01s/it]
[2025-07-31 11:11:31,373: WARNING/ForkPoolWorker-2] 
 19%|#9        | 94/494 [05:57<12:52,  1.93s/it]
[2025-07-31 11:11:31,425: WARNING/ForkPoolWorker-16] 
 26%|##6       | 130/494 [06:49<11:49,  1.95s/it]
[2025-07-31 11:11:32,520: WARNING/ForkPoolWorker-15] 
 22%|##1       | 108/494 [06:59<13:09,  2.04s/it]
[2025-07-31 11:11:32,933: WARNING/ForkPoolWorker-1] 
 24%|##4       | 119/494 [06:04<12:19,  1.97s/it]
[2025-07-31 11:11:33,357: WARNING/ForkPoolWorker-16] 
 27%|##6       | 131/494 [06:51<11:45,  1.94s/it]
[2025-07-31 11:11:33,375: WARNING/ForkPoolWorker-2] 
 19%|#9        | 95/494 [05:59<12:58,  1.95s/it]
[2025-07-31 11:11:34,529: WARNING/ForkPoolWorker-15] 
 22%|##2       | 109/494 [07:01<13:03,  2.03s/it]
[2025-07-31 11:11:34,953: WARNING/ForkPoolWorker-1] 
 24%|##4       | 120/494 [06:06<12:23,  1.99s/it]
[2025-07-31 11:11:35,326: WARNING/ForkPoolWorker-2] 
 19%|#9        | 96/494 [06:01<12:56,  1.95s/it]
[2025-07-31 11:11:35,388: WARNING/ForkPoolWorker-16] 
 27%|##6       | 132/494 [06:53<11:52,  1.97s/it]
[2025-07-31 11:11:36,476: WARNING/ForkPoolWorker-15] 
 22%|##2       | 110/494 [07:03<12:51,  2.01s/it]
[2025-07-31 11:11:37,008: WARNING/ForkPoolWorker-1] 
 24%|##4       | 121/494 [06:08<12:28,  2.01s/it]
[2025-07-31 11:11:37,304: WARNING/ForkPoolWorker-2] 
 20%|#9        | 97/494 [06:03<12:57,  1.96s/it]
[2025-07-31 11:11:37,417: WARNING/ForkPoolWorker-16] 
 27%|##6       | 133/494 [06:55<11:57,  1.99s/it]
[2025-07-31 11:11:38,735: WARNING/ForkPoolWorker-15] 
 22%|##2       | 111/494 [07:05<13:17,  2.08s/it]
[2025-07-31 11:11:39,060: WARNING/ForkPoolWorker-1] 
 25%|##4       | 122/494 [06:10<12:31,  2.02s/it]
[2025-07-31 11:11:39,184: WARNING/ForkPoolWorker-2] 
 20%|#9        | 98/494 [06:05<12:46,  1.94s/it]
[2025-07-31 11:11:39,367: WARNING/ForkPoolWorker-16] 
 27%|##7       | 134/494 [06:57<11:51,  1.98s/it]
[2025-07-31 11:11:40,761: WARNING/ForkPoolWorker-15] 
 23%|##2       | 112/494 [07:07<13:09,  2.07s/it]
[2025-07-31 11:11:41,050: WARNING/ForkPoolWorker-1] 
 25%|##4       | 123/494 [06:12<12:24,  2.01s/it]
[2025-07-31 11:11:41,373: WARNING/ForkPoolWorker-2] 
 20%|##        | 99/494 [06:07<13:14,  2.01s/it]
[2025-07-31 11:11:41,512: WARNING/ForkPoolWorker-16] 
 27%|##7       | 135/494 [06:59<12:07,  2.03s/it]
[2025-07-31 11:11:42,550: WARNING/ForkPoolWorker-15] 
 23%|##2       | 113/494 [07:09<12:35,  1.98s/it]
[2025-07-31 11:11:43,032: WARNING/ForkPoolWorker-1] 
 25%|##5       | 124/494 [06:14<12:21,  2.00s/it]
[2025-07-31 11:11:43,520: WARNING/ForkPoolWorker-16] 
 28%|##7       | 136/494 [07:01<12:03,  2.02s/it]
[2025-07-31 11:11:43,611: WARNING/ForkPoolWorker-2] 
 20%|##        | 100/494 [06:09<13:39,  2.08s/it]
[2025-07-31 11:11:44,590: WARNING/ForkPoolWorker-15] 
 23%|##3       | 114/494 [07:11<12:40,  2.00s/it]
[2025-07-31 11:11:44,897: WARNING/ForkPoolWorker-1] 
 25%|##5       | 125/494 [06:16<12:04,  1.96s/it]
[2025-07-31 11:11:45,439: WARNING/ForkPoolWorker-16] 
 28%|##7       | 137/494 [07:03<11:50,  1.99s/it]
[2025-07-31 11:11:45,616: WARNING/ForkPoolWorker-2] 
 20%|##        | 101/494 [06:11<13:28,  2.06s/it]
[2025-07-31 11:11:46,722: WARNING/ForkPoolWorker-15] 
 23%|##3       | 115/494 [07:13<12:53,  2.04s/it]
[2025-07-31 11:11:46,956: WARNING/ForkPoolWorker-1] 
 26%|##5       | 126/494 [06:18<12:12,  1.99s/it]
[2025-07-31 11:11:47,396: WARNING/ForkPoolWorker-16] 
 28%|##7       | 138/494 [07:05<11:45,  1.98s/it]
[2025-07-31 11:11:47,474: WARNING/ForkPoolWorker-2] 
 21%|##        | 102/494 [06:13<13:03,  2.00s/it]
[2025-07-31 11:11:48,761: WARNING/ForkPoolWorker-15] 
 23%|##3       | 116/494 [07:15<12:50,  2.04s/it]
[2025-07-31 11:11:48,852: WARNING/ForkPoolWorker-1] 
 26%|##5       | 127/494 [06:20<12:00,  1.96s/it]
[2025-07-31 11:11:49,169: WARNING/ForkPoolWorker-16] 
 28%|##8       | 139/494 [07:07<11:20,  1.92s/it]
[2025-07-31 11:11:49,386: WARNING/ForkPoolWorker-2] 
 21%|##        | 103/494 [06:15<12:50,  1.97s/it]
[2025-07-31 11:11:50,763: WARNING/ForkPoolWorker-1] 
 26%|##5       | 128/494 [06:22<11:52,  1.95s/it]
[2025-07-31 11:11:50,834: WARNING/ForkPoolWorker-15] 
 24%|##3       | 117/494 [07:17<12:52,  2.05s/it]
[2025-07-31 11:11:51,045: WARNING/ForkPoolWorker-16] 
 28%|##8       | 140/494 [07:09<11:14,  1.91s/it]
[2025-07-31 11:11:51,413: WARNING/ForkPoolWorker-2] 
 21%|##1       | 104/494 [06:17<12:55,  1.99s/it]
[2025-07-31 11:11:52,622: WARNING/ForkPoolWorker-1] 
 26%|##6       | 129/494 [06:24<11:41,  1.92s/it]
[2025-07-31 11:11:52,863: WARNING/ForkPoolWorker-15] 
 24%|##3       | 118/494 [07:20<12:48,  2.04s/it]
[2025-07-31 11:11:52,863: WARNING/ForkPoolWorker-16] 
 29%|##8       | 141/494 [07:11<11:03,  1.88s/it]
[2025-07-31 11:11:53,514: WARNING/ForkPoolWorker-2] 
 21%|##1       | 105/494 [06:19<13:06,  2.02s/it]
[2025-07-31 11:11:54,530: WARNING/ForkPoolWorker-1] 
 26%|##6       | 130/494 [06:26<11:37,  1.92s/it]
[2025-07-31 11:11:54,848: WARNING/ForkPoolWorker-16] 
 29%|##8       | 142/494 [07:13<11:12,  1.91s/it]
[2025-07-31 11:11:54,965: WARNING/ForkPoolWorker-15] 
 24%|##4       | 119/494 [07:22<12:52,  2.06s/it]
[2025-07-31 11:11:55,667: WARNING/ForkPoolWorker-2] 
 21%|##1       | 106/494 [06:21<13:19,  2.06s/it]
[2025-07-31 11:11:56,566: WARNING/ForkPoolWorker-1] 
 27%|##6       | 131/494 [06:28<11:48,  1.95s/it]
[2025-07-31 11:11:56,635: WARNING/ForkPoolWorker-16] 
 29%|##8       | 143/494 [07:15<10:57,  1.87s/it]
[2025-07-31 11:11:56,917: WARNING/ForkPoolWorker-15] 
 24%|##4       | 120/494 [07:24<12:38,  2.03s/it]
[2025-07-31 11:11:57,450: WARNING/ForkPoolWorker-2] 
 22%|##1       | 107/494 [06:23<12:45,  1.98s/it]
[2025-07-31 11:11:58,262: WARNING/ForkPoolWorker-1] 
 27%|##6       | 132/494 [06:29<11:18,  1.87s/it]
[2025-07-31 11:11:58,430: WARNING/ForkPoolWorker-16] 
 29%|##9       | 144/494 [07:16<10:47,  1.85s/it]
[2025-07-31 11:11:59,189: WARNING/ForkPoolWorker-15] 
 24%|##4       | 121/494 [07:26<13:03,  2.10s/it]
[2025-07-31 11:11:59,270: WARNING/ForkPoolWorker-2] 
 22%|##1       | 108/494 [06:25<12:25,  1.93s/it]
[2025-07-31 11:12:00,077: WARNING/ForkPoolWorker-1] 
 27%|##6       | 133/494 [06:31<11:10,  1.86s/it]
[2025-07-31 11:12:00,243: WARNING/ForkPoolWorker-16] 
 29%|##9       | 145/494 [07:18<10:41,  1.84s/it]
[2025-07-31 11:12:01,269: WARNING/ForkPoolWorker-15] 
 25%|##4       | 122/494 [07:28<12:59,  2.09s/it]
[2025-07-31 11:12:01,296: WARNING/ForkPoolWorker-2] 
 22%|##2       | 109/494 [06:27<12:34,  1.96s/it]
[2025-07-31 11:12:02,034: WARNING/ForkPoolWorker-1] 
 27%|##7       | 134/494 [06:33<11:19,  1.89s/it]
[2025-07-31 11:12:02,114: WARNING/ForkPoolWorker-16] 
 30%|##9       | 146/494 [07:20<10:43,  1.85s/it]
[2025-07-31 11:12:03,025: WARNING/ForkPoolWorker-2] 
 22%|##2       | 110/494 [06:29<12:05,  1.89s/it]
[2025-07-31 11:12:03,456: WARNING/ForkPoolWorker-15] 
 25%|##4       | 123/494 [07:30<13:07,  2.12s/it]
[2025-07-31 11:12:03,976: WARNING/ForkPoolWorker-1] 
 27%|##7       | 135/494 [06:35<11:23,  1.90s/it]
[2025-07-31 11:12:04,103: WARNING/ForkPoolWorker-16] 
 30%|##9       | 147/494 [07:22<10:56,  1.89s/it]
[2025-07-31 11:12:04,688: WARNING/ForkPoolWorker-2] 
 22%|##2       | 111/494 [06:30<11:37,  1.82s/it]
[2025-07-31 11:12:05,681: WARNING/ForkPoolWorker-15] 
 25%|##5       | 124/494 [07:32<13:16,  2.15s/it]
[2025-07-31 11:12:05,727: WARNING/ForkPoolWorker-1] 
 28%|##7       | 136/494 [06:37<11:05,  1.86s/it]
[2025-07-31 11:12:05,939: WARNING/ForkPoolWorker-16] 
 30%|##9       | 148/494 [07:24<10:48,  1.87s/it]
[2025-07-31 11:12:06,488: WARNING/ForkPoolWorker-2] 
 23%|##2       | 112/494 [06:32<11:33,  1.82s/it]
[2025-07-31 11:12:07,525: WARNING/ForkPoolWorker-1] 
 28%|##7       | 137/494 [06:39<10:56,  1.84s/it]
[2025-07-31 11:12:07,791: WARNING/ForkPoolWorker-15] 
 25%|##5       | 125/494 [07:34<13:09,  2.14s/it]
[2025-07-31 11:12:07,853: WARNING/ForkPoolWorker-16] 
 30%|###       | 149/494 [07:26<10:50,  1.89s/it]
[2025-07-31 11:12:08,501: WARNING/ForkPoolWorker-2] 
 23%|##2       | 113/494 [06:34<11:54,  1.87s/it]
[2025-07-31 11:12:09,636: WARNING/ForkPoolWorker-1] 
 28%|##7       | 138/494 [06:41<11:23,  1.92s/it]
[2025-07-31 11:12:09,825: WARNING/ForkPoolWorker-15] 
 26%|##5       | 126/494 [07:36<12:55,  2.11s/it]
[2025-07-31 11:12:09,901: WARNING/ForkPoolWorker-16] 
 30%|###       | 150/494 [07:28<11:05,  1.93s/it]
[2025-07-31 11:12:10,481: WARNING/ForkPoolWorker-2] 
 23%|##3       | 114/494 [06:36<12:04,  1.91s/it]
[2025-07-31 11:12:11,653: WARNING/ForkPoolWorker-1] 
 28%|##8       | 139/494 [06:43<11:32,  1.95s/it]
[2025-07-31 11:12:11,704: WARNING/ForkPoolWorker-15] 
 26%|##5       | 127/494 [07:38<12:28,  2.04s/it]
[2025-07-31 11:12:11,934: WARNING/ForkPoolWorker-16] 
 31%|###       | 151/494 [07:30<11:13,  1.96s/it]
[2025-07-31 11:12:12,461: WARNING/ForkPoolWorker-2] 
 23%|##3       | 115/494 [06:38<12:10,  1.93s/it]
[2025-07-31 11:12:13,595: WARNING/ForkPoolWorker-1] 
 28%|##8       | 140/494 [06:45<11:29,  1.95s/it]
[2025-07-31 11:12:13,870: WARNING/ForkPoolWorker-16] 
 31%|###       | 152/494 [07:32<11:08,  1.96s/it]
[2025-07-31 11:12:13,879: WARNING/ForkPoolWorker-15] 
 26%|##5       | 128/494 [07:41<12:41,  2.08s/it]
[2025-07-31 11:12:14,504: WARNING/ForkPoolWorker-2] 
 23%|##3       | 116/494 [06:40<12:19,  1.96s/it]
[2025-07-31 11:12:15,424: WARNING/ForkPoolWorker-1] 
 29%|##8       | 141/494 [06:47<11:14,  1.91s/it]
[2025-07-31 11:12:15,872: WARNING/ForkPoolWorker-16] 
 31%|###       | 153/494 [07:34<11:11,  1.97s/it]
[2025-07-31 11:12:15,916: WARNING/ForkPoolWorker-15] 
 26%|##6       | 129/494 [07:43<12:34,  2.07s/it]
[2025-07-31 11:12:16,375: WARNING/ForkPoolWorker-2] 
 24%|##3       | 117/494 [06:42<12:10,  1.94s/it]
[2025-07-31 11:12:17,665: WARNING/ForkPoolWorker-1] 
 29%|##8       | 142/494 [06:49<11:47,  2.01s/it]
[2025-07-31 11:12:17,735: WARNING/ForkPoolWorker-15] 
 26%|##6       | 130/494 [07:44<12:05,  1.99s/it]
[2025-07-31 11:12:17,939: WARNING/ForkPoolWorker-16] 
 31%|###1      | 154/494 [07:36<11:19,  2.00s/it]
[2025-07-31 11:12:18,572: WARNING/ForkPoolWorker-2] 
 24%|##3       | 118/494 [06:44<12:37,  2.02s/it]
[2025-07-31 11:12:19,605: WARNING/ForkPoolWorker-1] 
 29%|##8       | 143/494 [06:51<11:38,  1.99s/it]
[2025-07-31 11:12:19,643: WARNING/ForkPoolWorker-15] 
 27%|##6       | 131/494 [07:46<11:54,  1.97s/it]
[2025-07-31 11:12:20,079: WARNING/ForkPoolWorker-16] 
 31%|###1      | 155/494 [07:38<11:31,  2.04s/it]
[2025-07-31 11:12:20,780: WARNING/ForkPoolWorker-2] 
 24%|##4       | 119/494 [06:46<12:57,  2.07s/it]
[2025-07-31 11:12:21,655: WARNING/ForkPoolWorker-1] 
 29%|##9       | 144/494 [06:53<11:42,  2.01s/it]
[2025-07-31 11:12:21,844: WARNING/ForkPoolWorker-15] 
 27%|##6       | 132/494 [07:49<12:17,  2.04s/it]
[2025-07-31 11:12:22,113: WARNING/ForkPoolWorker-16] 
 32%|###1      | 156/494 [07:40<11:29,  2.04s/it]
[2025-07-31 11:12:22,867: WARNING/ForkPoolWorker-2] 
 24%|##4       | 120/494 [06:48<12:56,  2.08s/it]
[2025-07-31 11:12:23,834: WARNING/ForkPoolWorker-1] 
 29%|##9       | 145/494 [06:55<11:58,  2.06s/it]
[2025-07-31 11:12:23,925: WARNING/ForkPoolWorker-15] 
 27%|##6       | 133/494 [07:51<12:20,  2.05s/it]
[2025-07-31 11:12:24,239: WARNING/ForkPoolWorker-16] 
 32%|###1      | 157/494 [07:42<11:35,  2.07s/it]
[2025-07-31 11:12:24,965: WARNING/ForkPoolWorker-2] 
 24%|##4       | 121/494 [06:50<12:57,  2.08s/it]
[2025-07-31 11:12:25,885: WARNING/ForkPoolWorker-1] 
 30%|##9       | 146/494 [06:57<11:55,  2.06s/it]
[2025-07-31 11:12:26,010: WARNING/ForkPoolWorker-15] 
 27%|##7       | 134/494 [07:53<12:21,  2.06s/it]
[2025-07-31 11:12:26,251: WARNING/ForkPoolWorker-16] 
 32%|###1      | 158/494 [07:44<11:28,  2.05s/it]
[2025-07-31 11:12:26,980: WARNING/ForkPoolWorker-2] 
 25%|##4       | 122/494 [06:52<12:47,  2.06s/it]
[2025-07-31 11:12:27,836: WARNING/ForkPoolWorker-1] 
 30%|##9       | 147/494 [06:59<11:42,  2.03s/it]
[2025-07-31 11:12:28,046: WARNING/ForkPoolWorker-15] 
 27%|##7       | 135/494 [07:55<12:17,  2.05s/it]
[2025-07-31 11:12:28,172: WARNING/ForkPoolWorker-16] 
 32%|###2      | 159/494 [07:46<11:13,  2.01s/it]
[2025-07-31 11:12:28,674: WARNING/ForkPoolWorker-2] 
 25%|##4       | 123/494 [06:54<12:04,  1.95s/it]
[2025-07-31 11:12:29,691: WARNING/ForkPoolWorker-1] 
 30%|##9       | 148/494 [07:01<11:23,  1.97s/it]
[2025-07-31 11:12:30,043: WARNING/ForkPoolWorker-16] 
 32%|###2      | 160/494 [07:48<10:57,  1.97s/it]
[2025-07-31 11:12:30,074: WARNING/ForkPoolWorker-15] 
 28%|##7       | 136/494 [07:57<12:12,  2.05s/it]
[2025-07-31 11:12:30,477: WARNING/ForkPoolWorker-2] 
 25%|##5       | 124/494 [06:56<11:45,  1.91s/it]
[2025-07-31 11:12:31,375: WARNING/ForkPoolWorker-1] 
 30%|###       | 149/494 [07:03<10:51,  1.89s/it]
[2025-07-31 11:12:32,090: WARNING/ForkPoolWorker-16] 
 33%|###2      | 161/494 [07:50<11:03,  1.99s/it]
[2025-07-31 11:12:32,217: WARNING/ForkPoolWorker-15] 
 28%|##7       | 137/494 [07:59<12:18,  2.07s/it]
[2025-07-31 11:12:32,397: WARNING/ForkPoolWorker-2] 
 25%|##5       | 125/494 [06:58<11:45,  1.91s/it]
[2025-07-31 11:12:33,358: WARNING/ForkPoolWorker-1] 
 30%|###       | 150/494 [07:05<10:59,  1.92s/it]
[2025-07-31 11:12:34,073: WARNING/ForkPoolWorker-16] 
 33%|###2      | 162/494 [07:52<11:00,  1.99s/it]
[2025-07-31 11:12:34,417: WARNING/ForkPoolWorker-15] 
 28%|##7       | 138/494 [08:01<12:32,  2.11s/it]
[2025-07-31 11:12:34,417: WARNING/ForkPoolWorker-2] 
 26%|##5       | 126/494 [07:00<11:55,  1.94s/it]
[2025-07-31 11:12:35,357: WARNING/ForkPoolWorker-1] 
 31%|###       | 151/494 [07:07<11:05,  1.94s/it]
[2025-07-31 11:12:35,989: WARNING/ForkPoolWorker-16] 
 33%|###2      | 163/494 [07:54<10:51,  1.97s/it]
[2025-07-31 11:12:36,356: WARNING/ForkPoolWorker-2] 
 26%|##5       | 127/494 [07:02<11:52,  1.94s/it]
[2025-07-31 11:12:36,542: WARNING/ForkPoolWorker-15] 
 28%|##8       | 139/494 [08:03<12:31,  2.12s/it]
[2025-07-31 11:12:37,277: WARNING/ForkPoolWorker-1] 
 31%|###       | 152/494 [07:08<11:01,  1.93s/it]
[2025-07-31 11:12:37,869: WARNING/ForkPoolWorker-16] 
 33%|###3      | 164/494 [07:56<10:40,  1.94s/it]
[2025-07-31 11:12:38,223: WARNING/ForkPoolWorker-2] 
 26%|##5       | 128/494 [07:04<11:42,  1.92s/it]
[2025-07-31 11:12:38,618: WARNING/ForkPoolWorker-15] 
 28%|##8       | 140/494 [08:05<12:25,  2.11s/it]
[2025-07-31 11:12:39,154: WARNING/ForkPoolWorker-1] 
 31%|###       | 153/494 [07:10<10:53,  1.92s/it]
[2025-07-31 11:12:39,802: WARNING/ForkPoolWorker-16] 
 33%|###3      | 165/494 [07:58<10:37,  1.94s/it]
[2025-07-31 11:12:40,190: WARNING/ForkPoolWorker-2] 
 26%|##6       | 129/494 [07:06<11:45,  1.93s/it]
[2025-07-31 11:12:40,756: WARNING/ForkPoolWorker-15] 
 29%|##8       | 141/494 [08:07<12:26,  2.11s/it]
[2025-07-31 11:12:41,103: WARNING/ForkPoolWorker-1] 
 31%|###1      | 154/494 [07:12<10:55,  1.93s/it]
[2025-07-31 11:12:41,765: WARNING/ForkPoolWorker-16] 
 34%|###3      | 166/494 [08:00<10:38,  1.95s/it]
[2025-07-31 11:12:42,070: WARNING/ForkPoolWorker-2] 
 26%|##6       | 130/494 [07:08<11:38,  1.92s/it]
[2025-07-31 11:12:42,836: WARNING/ForkPoolWorker-15] 
 29%|##8       | 142/494 [08:09<12:20,  2.10s/it]
[2025-07-31 11:12:43,124: WARNING/ForkPoolWorker-1] 
 31%|###1      | 155/494 [07:14<11:02,  1.95s/it]
[2025-07-31 11:12:43,729: WARNING/ForkPoolWorker-16] 
 34%|###3      | 167/494 [08:02<10:38,  1.95s/it]
[2025-07-31 11:12:43,830: WARNING/ForkPoolWorker-2] 
 27%|##6       | 131/494 [07:09<11:18,  1.87s/it]
[2025-07-31 11:12:44,880: WARNING/ForkPoolWorker-15] 
 29%|##8       | 143/494 [08:12<12:12,  2.09s/it]
[2025-07-31 11:12:45,001: WARNING/ForkPoolWorker-1] 
 32%|###1      | 156/494 [07:16<10:52,  1.93s/it]
[2025-07-31 11:12:45,674: WARNING/ForkPoolWorker-16] 
 34%|###4      | 168/494 [08:04<10:35,  1.95s/it]
[2025-07-31 11:12:45,777: WARNING/ForkPoolWorker-2] 
 27%|##6       | 132/494 [07:11<11:25,  1.89s/it]
[2025-07-31 11:12:46,932: WARNING/ForkPoolWorker-1] 
 32%|###1      | 157/494 [07:18<10:50,  1.93s/it]
[2025-07-31 11:12:47,108: WARNING/ForkPoolWorker-15] 
 29%|##9       | 144/494 [08:14<12:25,  2.13s/it]
[2025-07-31 11:12:47,643: WARNING/ForkPoolWorker-16] 
 34%|###4      | 169/494 [08:06<10:35,  1.96s/it]
[2025-07-31 11:12:47,653: WARNING/ForkPoolWorker-2] 
 27%|##6       | 133/494 [07:13<11:21,  1.89s/it]
[2025-07-31 11:12:48,874: WARNING/ForkPoolWorker-1] 
 32%|###1      | 158/494 [07:20<10:50,  1.93s/it]
[2025-07-31 11:12:49,417: WARNING/ForkPoolWorker-15] 
 29%|##9       | 145/494 [08:16<12:41,  2.18s/it]
[2025-07-31 11:12:49,617: WARNING/ForkPoolWorker-16] 
 34%|###4      | 170/494 [08:08<10:35,  1.96s/it]
[2025-07-31 11:12:49,710: WARNING/ForkPoolWorker-2] 
 27%|##7       | 134/494 [07:15<11:37,  1.94s/it]
[2025-07-31 11:12:50,818: WARNING/ForkPoolWorker-1] 
 32%|###2      | 159/494 [07:22<10:49,  1.94s/it]
[2025-07-31 11:12:51,506: WARNING/ForkPoolWorker-15] 
 30%|##9       | 146/494 [08:18<12:29,  2.15s/it]
[2025-07-31 11:12:51,640: WARNING/ForkPoolWorker-2] 
 27%|##7       | 135/494 [07:17<11:35,  1.94s/it]
[2025-07-31 11:12:51,715: WARNING/ForkPoolWorker-16] 
 35%|###4      | 171/494 [08:10<10:46,  2.00s/it]
[2025-07-31 11:12:52,617: WARNING/ForkPoolWorker-1] 
 32%|###2      | 160/494 [07:24<10:33,  1.90s/it]
[2025-07-31 11:12:53,555: WARNING/ForkPoolWorker-2] 
 28%|##7       | 136/494 [07:19<11:30,  1.93s/it]
[2025-07-31 11:12:53,621: WARNING/ForkPoolWorker-15] 
 30%|##9       | 147/494 [08:20<12:23,  2.14s/it]
[2025-07-31 11:12:53,779: WARNING/ForkPoolWorker-16] 
 35%|###4      | 172/494 [08:12<10:50,  2.02s/it]
[2025-07-31 11:12:54,685: WARNING/ForkPoolWorker-1] 
 33%|###2      | 161/494 [07:26<10:48,  1.95s/it]
[2025-07-31 11:12:55,779: WARNING/ForkPoolWorker-15] 
 30%|##9       | 148/494 [08:22<12:23,  2.15s/it]
[2025-07-31 11:12:55,795: WARNING/ForkPoolWorker-2] 
 28%|##7       | 137/494 [07:21<12:02,  2.02s/it]
[2025-07-31 11:12:55,796: WARNING/ForkPoolWorker-16] 
 35%|###5      | 173/494 [08:14<10:48,  2.02s/it]
[2025-07-31 11:12:56,669: WARNING/ForkPoolWorker-1] 
 33%|###2      | 162/494 [07:28<10:50,  1.96s/it]
[2025-07-31 11:12:57,735: WARNING/ForkPoolWorker-16] 
 35%|###5      | 174/494 [08:16<10:38,  2.00s/it]
[2025-07-31 11:12:57,735: WARNING/ForkPoolWorker-15] 
 30%|###       | 149/494 [08:24<12:01,  2.09s/it]
[2025-07-31 11:12:57,800: WARNING/ForkPoolWorker-2] 
 28%|##7       | 138/494 [07:23<11:58,  2.02s/it]
[2025-07-31 11:12:58,620: WARNING/ForkPoolWorker-1] 
 33%|###2      | 163/494 [07:30<10:47,  1.96s/it]
[2025-07-31 11:12:59,625: WARNING/ForkPoolWorker-16] 
 35%|###5      | 175/494 [08:18<10:26,  1.96s/it]
[2025-07-31 11:12:59,765: WARNING/ForkPoolWorker-2] 
 28%|##8       | 139/494 [07:25<11:50,  2.00s/it]
[2025-07-31 11:12:59,947: WARNING/ForkPoolWorker-15] 
 30%|###       | 150/494 [08:27<12:11,  2.13s/it]
[2025-07-31 11:13:00,538: WARNING/ForkPoolWorker-1] 
 33%|###3      | 164/494 [07:32<10:41,  1.94s/it]
[2025-07-31 11:13:01,523: WARNING/ForkPoolWorker-16] 
 36%|###5      | 176/494 [08:19<10:18,  1.94s/it]
[2025-07-31 11:13:01,634: WARNING/ForkPoolWorker-2] 
 28%|##8       | 140/494 [07:27<11:34,  1.96s/it]
[2025-07-31 11:13:02,152: WARNING/ForkPoolWorker-15] 
 31%|###       | 151/494 [08:29<12:17,  2.15s/it]
[2025-07-31 11:13:02,324: WARNING/ForkPoolWorker-1] 
 33%|###3      | 165/494 [07:33<10:24,  1.90s/it]
[2025-07-31 11:13:03,426: WARNING/ForkPoolWorker-16] 
 36%|###5      | 177/494 [08:21<10:12,  1.93s/it]
[2025-07-31 11:13:03,716: WARNING/ForkPoolWorker-2] 
 29%|##8       | 141/494 [07:29<11:45,  2.00s/it]
[2025-07-31 11:13:04,346: WARNING/ForkPoolWorker-1] 
 34%|###3      | 166/494 [07:36<10:34,  1.93s/it]
[2025-07-31 11:13:04,372: WARNING/ForkPoolWorker-15] 
 31%|###       | 152/494 [08:31<12:22,  2.17s/it]
[2025-07-31 11:13:05,446: WARNING/ForkPoolWorker-16] 
 36%|###6      | 178/494 [08:23<10:18,  1.96s/it]
[2025-07-31 11:13:05,760: WARNING/ForkPoolWorker-2] 
 29%|##8       | 142/494 [07:31<11:47,  2.01s/it]
[2025-07-31 11:13:06,377: WARNING/ForkPoolWorker-15] 
 31%|###       | 153/494 [08:33<12:03,  2.12s/it]
[2025-07-31 11:13:06,436: WARNING/ForkPoolWorker-1] 
 34%|###3      | 167/494 [07:38<10:47,  1.98s/it]
[2025-07-31 11:13:07,440: WARNING/ForkPoolWorker-16] 
 36%|###6      | 179/494 [08:25<10:20,  1.97s/it]
[2025-07-31 11:13:07,777: WARNING/ForkPoolWorker-2] 
 29%|##8       | 143/494 [07:33<11:46,  2.01s/it]
[2025-07-31 11:13:08,316: WARNING/ForkPoolWorker-15] 
 31%|###1      | 154/494 [08:35<11:42,  2.07s/it]
[2025-07-31 11:13:08,547: WARNING/ForkPoolWorker-1] 
 34%|###4      | 168/494 [07:40<10:57,  2.02s/it]
[2025-07-31 11:13:09,243: WARNING/ForkPoolWorker-16] 
 36%|###6      | 180/494 [08:27<10:02,  1.92s/it]
[2025-07-31 11:13:09,441: WARNING/ForkPoolWorker-2] 
 29%|##9       | 144/494 [07:35<11:08,  1.91s/it]
[2025-07-31 11:13:10,320: WARNING/ForkPoolWorker-1] 
 34%|###4      | 169/494 [07:41<10:32,  1.95s/it]
[2025-07-31 11:13:10,422: WARNING/ForkPoolWorker-15] 
 31%|###1      | 155/494 [08:37<11:44,  2.08s/it]
[2025-07-31 11:13:11,213: WARNING/ForkPoolWorker-16] 
 37%|###6      | 181/494 [08:29<10:05,  1.93s/it]
[2025-07-31 11:13:11,496: WARNING/ForkPoolWorker-2] 
 29%|##9       | 145/494 [07:37<11:21,  1.95s/it]
[2025-07-31 11:13:12,032: WARNING/ForkPoolWorker-1] 
 34%|###4      | 170/494 [07:43<10:07,  1.88s/it]
[2025-07-31 11:13:12,661: WARNING/ForkPoolWorker-15] 
 32%|###1      | 156/494 [08:39<11:58,  2.13s/it]
[2025-07-31 11:13:13,129: WARNING/ForkPoolWorker-2] 
 30%|##9       | 146/494 [07:39<10:46,  1.86s/it]
[2025-07-31 11:13:13,160: WARNING/ForkPoolWorker-16] 
 37%|###6      | 182/494 [08:31<10:04,  1.94s/it]
[2025-07-31 11:13:13,957: WARNING/ForkPoolWorker-1] 
 35%|###4      | 171/494 [07:45<10:10,  1.89s/it]
[2025-07-31 11:13:14,601: WARNING/ForkPoolWorker-15] 
 32%|###1      | 157/494 [08:41<11:37,  2.07s/it]
[2025-07-31 11:13:15,120: WARNING/ForkPoolWorker-16] 
 37%|###7      | 183/494 [08:33<10:04,  1.94s/it]
[2025-07-31 11:13:15,185: WARNING/ForkPoolWorker-2] 
 30%|##9       | 147/494 [07:41<11:05,  1.92s/it]
[2025-07-31 11:13:16,001: WARNING/ForkPoolWorker-1] 
 35%|###4      | 172/494 [07:47<10:23,  1.94s/it]
[2025-07-31 11:13:16,524: WARNING/ForkPoolWorker-15] 
 32%|###1      | 158/494 [08:43<11:20,  2.03s/it]
[2025-07-31 11:13:17,097: WARNING/ForkPoolWorker-16] 
 37%|###7      | 184/494 [08:35<10:05,  1.95s/it]
[2025-07-31 11:13:17,297: WARNING/ForkPoolWorker-2] 
 30%|##9       | 148/494 [07:43<11:23,  1.98s/it]
[2025-07-31 11:13:18,066: WARNING/ForkPoolWorker-1] 
 35%|###5      | 173/494 [07:49<10:34,  1.98s/it]
[2025-07-31 11:13:18,397: WARNING/ForkPoolWorker-15] 
 32%|###2      | 159/494 [08:45<11:03,  1.98s/it]
[2025-07-31 11:13:19,216: WARNING/ForkPoolWorker-16] 
 37%|###7      | 185/494 [08:37<10:19,  2.00s/it]
[2025-07-31 11:13:19,362: WARNING/ForkPoolWorker-2] 
 30%|###       | 149/494 [07:45<11:30,  2.00s/it]
[2025-07-31 11:13:19,992: WARNING/ForkPoolWorker-1] 
 35%|###5      | 174/494 [07:51<10:27,  1.96s/it]
[2025-07-31 11:13:20,389: WARNING/ForkPoolWorker-15] 
 32%|###2      | 160/494 [08:47<11:02,  1.98s/it]
[2025-07-31 11:13:21,253: WARNING/ForkPoolWorker-16] 
 38%|###7      | 186/494 [08:39<10:20,  2.01s/it]
[2025-07-31 11:13:21,437: WARNING/ForkPoolWorker-2] 
 30%|###       | 150/494 [07:47<11:36,  2.02s/it]
[2025-07-31 11:13:21,893: WARNING/ForkPoolWorker-1] 
 35%|###5      | 175/494 [07:53<10:19,  1.94s/it]
[2025-07-31 11:13:22,397: WARNING/ForkPoolWorker-15] 
 33%|###2      | 161/494 [08:49<11:03,  1.99s/it]
[2025-07-31 11:13:23,031: WARNING/ForkPoolWorker-16] 
 38%|###7      | 187/494 [08:41<09:56,  1.94s/it]
[2025-07-31 11:13:23,067: WARNING/ForkPoolWorker-2] 
 31%|###       | 151/494 [07:49<10:53,  1.91s/it]
[2025-07-31 11:13:23,534: WARNING/ForkPoolWorker-1] 
 36%|###5      | 176/494 [07:55<09:48,  1.85s/it]
[2025-07-31 11:13:24,506: WARNING/ForkPoolWorker-15] 
 33%|###2      | 162/494 [08:51<11:12,  2.03s/it]
[2025-07-31 11:13:24,907: WARNING/ForkPoolWorker-16] 
 38%|###8      | 188/494 [08:43<09:48,  1.92s/it]
[2025-07-31 11:13:24,998: WARNING/ForkPoolWorker-2] 
 31%|###       | 152/494 [07:51<10:54,  1.91s/it]
[2025-07-31 11:13:25,233: WARNING/ForkPoolWorker-1] 
 36%|###5      | 177/494 [07:56<09:32,  1.81s/it]
[2025-07-31 11:13:26,707: WARNING/ForkPoolWorker-15] 
 33%|###2      | 163/494 [08:53<11:28,  2.08s/it]
[2025-07-31 11:13:26,855: WARNING/ForkPoolWorker-16] 
 38%|###8      | 189/494 [08:45<09:48,  1.93s/it]
[2025-07-31 11:13:26,864: WARNING/ForkPoolWorker-2] 
 31%|###       | 153/494 [07:52<10:47,  1.90s/it]
[2025-07-31 11:13:27,169: WARNING/ForkPoolWorker-1] 
 36%|###6      | 178/494 [07:58<09:43,  1.85s/it]
[2025-07-31 11:13:28,623: WARNING/ForkPoolWorker-16] 
 38%|###8      | 190/494 [08:47<09:32,  1.88s/it]
[2025-07-31 11:13:28,838: WARNING/ForkPoolWorker-15] 
 33%|###3      | 164/494 [08:55<11:31,  2.09s/it]
[2025-07-31 11:13:28,851: WARNING/ForkPoolWorker-1] 
 36%|###6      | 179/494 [08:00<09:25,  1.80s/it]
[2025-07-31 11:13:28,865: WARNING/ForkPoolWorker-2] 
 31%|###1      | 154/494 [07:54<10:56,  1.93s/it]
[2025-07-31 11:13:30,614: WARNING/ForkPoolWorker-16] 
 39%|###8      | 191/494 [08:49<09:40,  1.91s/it]
[2025-07-31 11:13:30,831: WARNING/ForkPoolWorker-15] 
 33%|###3      | 165/494 [08:57<11:19,  2.06s/it]
[2025-07-31 11:13:30,841: WARNING/ForkPoolWorker-2] 
 31%|###1      | 155/494 [07:56<10:58,  1.94s/it]
[2025-07-31 11:13:30,955: WARNING/ForkPoolWorker-1] 
 36%|###6      | 180/494 [08:02<09:52,  1.89s/it]
[2025-07-31 11:13:32,547: WARNING/ForkPoolWorker-16] 
 39%|###8      | 192/494 [08:50<09:39,  1.92s/it]
[2025-07-31 11:13:32,881: WARNING/ForkPoolWorker-1] 
 37%|###6      | 181/494 [08:04<09:54,  1.90s/it]
[2025-07-31 11:13:32,913: WARNING/ForkPoolWorker-2] 
 32%|###1      | 156/494 [07:58<11:09,  1.98s/it]
[2025-07-31 11:13:32,962: WARNING/ForkPoolWorker-15] 
 34%|###3      | 166/494 [09:00<11:23,  2.08s/it]
[2025-07-31 11:13:34,617: WARNING/ForkPoolWorker-16] 
 39%|###9      | 193/494 [08:53<09:51,  1.96s/it]
[2025-07-31 11:13:34,909: WARNING/ForkPoolWorker-2] 
 32%|###1      | 157/494 [08:00<11:09,  1.99s/it]
[2025-07-31 11:13:35,013: WARNING/ForkPoolWorker-1] 
 37%|###6      | 182/494 [08:06<10:14,  1.97s/it]
[2025-07-31 11:13:35,245: WARNING/ForkPoolWorker-15] 
 34%|###3      | 167/494 [09:02<11:41,  2.14s/it]
[2025-07-31 11:13:36,788: WARNING/ForkPoolWorker-16] 
 39%|###9      | 194/494 [08:55<10:08,  2.03s/it]
[2025-07-31 11:13:37,111: WARNING/ForkPoolWorker-2] 
 32%|###1      | 158/494 [08:03<11:27,  2.05s/it]
[2025-07-31 11:13:37,154: WARNING/ForkPoolWorker-1] 
 37%|###7      | 183/494 [08:08<10:28,  2.02s/it]
[2025-07-31 11:13:37,622: WARNING/ForkPoolWorker-15] 
 34%|###4      | 168/494 [09:04<12:01,  2.21s/it]
[2025-07-31 11:13:38,832: WARNING/ForkPoolWorker-16] 
 39%|###9      | 195/494 [08:57<10:07,  2.03s/it]
[2025-07-31 11:13:39,203: WARNING/ForkPoolWorker-1] 
 37%|###7      | 184/494 [08:10<10:29,  2.03s/it]
[2025-07-31 11:13:39,236: WARNING/ForkPoolWorker-2] 
 32%|###2      | 159/494 [08:05<11:35,  2.07s/it]
[2025-07-31 11:13:39,786: WARNING/ForkPoolWorker-15] 
 34%|###4      | 169/494 [09:06<11:54,  2.20s/it]
[2025-07-31 11:13:41,069: WARNING/ForkPoolWorker-16] 
 40%|###9      | 196/494 [08:59<10:23,  2.09s/it]
[2025-07-31 11:13:41,324: WARNING/ForkPoolWorker-1] 
 37%|###7      | 185/494 [08:12<10:35,  2.06s/it]
[2025-07-31 11:13:41,458: WARNING/ForkPoolWorker-2] 
 32%|###2      | 160/494 [08:07<11:47,  2.12s/it]
[2025-07-31 11:13:41,690: WARNING/ForkPoolWorker-15] 
 34%|###4      | 170/494 [09:08<11:23,  2.11s/it]
[2025-07-31 11:13:43,210: WARNING/ForkPoolWorker-16] 
 40%|###9      | 197/494 [09:01<10:25,  2.11s/it]
[2025-07-31 11:13:43,469: WARNING/ForkPoolWorker-1] 
 38%|###7      | 186/494 [08:15<10:41,  2.08s/it]
[2025-07-31 11:13:43,500: WARNING/ForkPoolWorker-2] 
 33%|###2      | 161/494 [08:09<11:37,  2.10s/it]
[2025-07-31 11:13:43,789: WARNING/ForkPoolWorker-15] 
 35%|###4      | 171/494 [09:10<11:20,  2.11s/it]
[2025-07-31 11:13:44,991: WARNING/ForkPoolWorker-16] 
 40%|####      | 198/494 [09:03<09:54,  2.01s/it]
[2025-07-31 11:13:45,222: WARNING/ForkPoolWorker-1] 
 38%|###7      | 187/494 [08:16<10:09,  1.98s/it]
[2025-07-31 11:13:45,532: WARNING/ForkPoolWorker-2] 
 33%|###2      | 162/494 [08:11<11:29,  2.08s/it]
[2025-07-31 11:13:45,856: WARNING/ForkPoolWorker-15] 
 35%|###4      | 172/494 [09:13<11:14,  2.10s/it]
[2025-07-31 11:13:46,960: WARNING/ForkPoolWorker-16] 
 40%|####      | 199/494 [09:05<09:49,  2.00s/it]
[2025-07-31 11:13:47,097: WARNING/ForkPoolWorker-1] 
 38%|###8      | 188/494 [08:18<09:57,  1.95s/it]
[2025-07-31 11:13:47,481: WARNING/ForkPoolWorker-2] 
 33%|###2      | 163/494 [08:13<11:14,  2.04s/it]
[2025-07-31 11:13:47,940: WARNING/ForkPoolWorker-15] 
 35%|###5      | 173/494 [09:15<11:11,  2.09s/it]
[2025-07-31 11:13:48,859: WARNING/ForkPoolWorker-16] 
 40%|####      | 200/494 [09:07<09:38,  1.97s/it]
[2025-07-31 11:13:49,169: WARNING/ForkPoolWorker-1] 
 38%|###8      | 189/494 [08:20<10:06,  1.99s/it]
[2025-07-31 11:13:49,549: WARNING/ForkPoolWorker-2] 
 33%|###3      | 164/494 [08:15<11:15,  2.05s/it]
[2025-07-31 11:13:50,267: WARNING/ForkPoolWorker-15] 
 35%|###5      | 174/494 [09:17<11:31,  2.16s/it]
[2025-07-31 11:13:50,847: WARNING/ForkPoolWorker-16] 
 41%|####      | 201/494 [09:09<09:38,  1.97s/it]
[2025-07-31 11:13:51,133: WARNING/ForkPoolWorker-1] 
 38%|###8      | 190/494 [08:22<10:02,  1.98s/it]
[2025-07-31 11:13:51,591: WARNING/ForkPoolWorker-2] 
 33%|###3      | 165/494 [08:17<11:13,  2.05s/it]
[2025-07-31 11:13:52,299: WARNING/ForkPoolWorker-15] 
 35%|###5      | 175/494 [09:19<11:17,  2.12s/it]
[2025-07-31 11:13:52,959: WARNING/ForkPoolWorker-16] 
 41%|####      | 202/494 [09:11<09:48,  2.02s/it]
[2025-07-31 11:13:53,238: WARNING/ForkPoolWorker-1] 
 39%|###8      | 191/494 [08:24<10:11,  2.02s/it]
[2025-07-31 11:13:53,708: WARNING/ForkPoolWorker-2] 
 34%|###3      | 166/494 [08:19<11:17,  2.07s/it]
[2025-07-31 11:13:54,414: WARNING/ForkPoolWorker-15] 
 36%|###5      | 176/494 [09:21<11:14,  2.12s/it]
[2025-07-31 11:13:54,903: WARNING/ForkPoolWorker-16] 
 41%|####1     | 203/494 [09:13<09:40,  1.99s/it]
[2025-07-31 11:13:55,226: WARNING/ForkPoolWorker-1] 
 39%|###8      | 192/494 [08:26<10:06,  2.01s/it]
[2025-07-31 11:13:55,600: WARNING/ForkPoolWorker-2] 
 34%|###3      | 167/494 [08:21<10:58,  2.01s/it]
[2025-07-31 11:13:56,635: WARNING/ForkPoolWorker-15] 
 36%|###5      | 177/494 [09:23<11:21,  2.15s/it]
[2025-07-31 11:13:56,886: WARNING/ForkPoolWorker-16] 
 41%|####1     | 204/494 [09:15<09:37,  1.99s/it]
[2025-07-31 11:13:57,415: WARNING/ForkPoolWorker-1] 
 39%|###9      | 193/494 [08:29<10:20,  2.06s/it]
[2025-07-31 11:13:57,876: WARNING/ForkPoolWorker-2] 
 34%|###4      | 168/494 [08:23<11:22,  2.09s/it]
[2025-07-31 11:13:58,716: WARNING/ForkPoolWorker-15] 
 36%|###6      | 178/494 [09:25<11:12,  2.13s/it]
[2025-07-31 11:13:59,115: WARNING/ForkPoolWorker-16] 
 41%|####1     | 205/494 [09:17<09:55,  2.06s/it]
[2025-07-31 11:13:59,466: WARNING/ForkPoolWorker-1] 
 39%|###9      | 194/494 [08:31<10:17,  2.06s/it]
[2025-07-31 11:13:59,985: WARNING/ForkPoolWorker-2] 
 34%|###4      | 169/494 [08:26<11:21,  2.10s/it]
[2025-07-31 11:14:00,810: WARNING/ForkPoolWorker-15] 
 36%|###6      | 179/494 [09:27<11:07,  2.12s/it]
[2025-07-31 11:14:01,141: WARNING/ForkPoolWorker-16] 
 42%|####1     | 206/494 [09:19<09:50,  2.05s/it]
[2025-07-31 11:14:01,496: WARNING/ForkPoolWorker-1] 
 39%|###9      | 195/494 [08:33<10:13,  2.05s/it]
[2025-07-31 11:14:02,031: WARNING/ForkPoolWorker-2] 
 34%|###4      | 170/494 [08:28<11:14,  2.08s/it]
[2025-07-31 11:14:03,075: WARNING/ForkPoolWorker-16] 
 42%|####1     | 207/494 [09:21<09:38,  2.02s/it]
[2025-07-31 11:14:03,120: WARNING/ForkPoolWorker-15] 
 36%|###6      | 180/494 [09:30<11:23,  2.18s/it]
[2025-07-31 11:14:03,492: WARNING/ForkPoolWorker-1] 
 40%|###9      | 196/494 [08:35<10:06,  2.03s/it]
[2025-07-31 11:14:03,679: WARNING/ForkPoolWorker-2] 
 35%|###4      | 171/494 [08:29<10:30,  1.95s/it]
[2025-07-31 11:14:04,807: WARNING/ForkPoolWorker-16] 
 42%|####2     | 208/494 [09:23<09:12,  1.93s/it]
[2025-07-31 11:14:05,356: WARNING/ForkPoolWorker-15] 
 37%|###6      | 181/494 [09:32<11:26,  2.19s/it]
[2025-07-31 11:14:05,377: WARNING/ForkPoolWorker-1] 
 40%|###9      | 197/494 [08:37<09:50,  1.99s/it]
[2025-07-31 11:14:05,549: WARNING/ForkPoolWorker-2] 
 35%|###4      | 172/494 [08:31<10:20,  1.93s/it]
[2025-07-31 11:14:06,569: WARNING/ForkPoolWorker-16] 
 42%|####2     | 209/494 [09:24<08:55,  1.88s/it]
[2025-07-31 11:14:07,419: WARNING/ForkPoolWorker-15] 
 37%|###6      | 182/494 [09:34<11:12,  2.15s/it]
[2025-07-31 11:14:07,438: WARNING/ForkPoolWorker-1] 
 40%|####      | 198/494 [08:39<09:55,  2.01s/it]
[2025-07-31 11:14:07,444: WARNING/ForkPoolWorker-2] 
 35%|###5      | 173/494 [08:33<10:15,  1.92s/it]
[2025-07-31 11:14:08,582: WARNING/ForkPoolWorker-16] 
 43%|####2     | 210/494 [09:26<09:05,  1.92s/it]
[2025-07-31 11:14:09,346: WARNING/ForkPoolWorker-15] 
 37%|###7      | 183/494 [09:36<10:48,  2.09s/it]
[2025-07-31 11:14:09,509: WARNING/ForkPoolWorker-2] 
 35%|###5      | 174/494 [08:35<10:27,  1.96s/it]
[2025-07-31 11:14:09,551: WARNING/ForkPoolWorker-1] 
 40%|####      | 199/494 [08:41<10:02,  2.04s/it]
[2025-07-31 11:14:10,461: WARNING/ForkPoolWorker-16] 
 43%|####2     | 211/494 [09:28<08:59,  1.91s/it]
[2025-07-31 11:14:11,400: WARNING/ForkPoolWorker-15] 
 37%|###7      | 184/494 [09:38<10:43,  2.08s/it]
[2025-07-31 11:14:11,576: WARNING/ForkPoolWorker-2] 
 35%|###5      | 175/494 [08:37<10:35,  1.99s/it]
[2025-07-31 11:14:11,739: WARNING/ForkPoolWorker-1] 
 40%|####      | 200/494 [08:43<10:13,  2.09s/it]
[2025-07-31 11:14:12,313: WARNING/ForkPoolWorker-16] 
 43%|####2     | 212/494 [09:30<08:53,  1.89s/it]
[2025-07-31 11:14:13,431: WARNING/ForkPoolWorker-1] 
 41%|####      | 201/494 [08:45<09:36,  1.97s/it]
[2025-07-31 11:14:13,457: WARNING/ForkPoolWorker-15] 
 37%|###7      | 185/494 [09:40<10:39,  2.07s/it]
[2025-07-31 11:14:13,733: WARNING/ForkPoolWorker-2] 
 36%|###5      | 176/494 [08:39<10:49,  2.04s/it]
[2025-07-31 11:14:13,935: WARNING/ForkPoolWorker-16] 
 43%|####3     | 213/494 [09:32<08:28,  1.81s/it]
[2025-07-31 11:14:15,116: WARNING/ForkPoolWorker-1] 
 41%|####      | 202/494 [08:46<09:09,  1.88s/it]
[2025-07-31 11:14:15,557: WARNING/ForkPoolWorker-2] 
 36%|###5      | 177/494 [08:41<10:26,  1.98s/it]
[2025-07-31 11:14:15,665: WARNING/ForkPoolWorker-16] 
 43%|####3     | 214/494 [09:34<08:20,  1.79s/it]
[2025-07-31 11:14:15,721: WARNING/ForkPoolWorker-15] 
 38%|###7      | 186/494 [09:42<10:55,  2.13s/it]
[2025-07-31 11:14:17,047: WARNING/ForkPoolWorker-1] 
 41%|####1     | 203/494 [08:48<09:12,  1.90s/it]
[2025-07-31 11:14:17,441: WARNING/ForkPoolWorker-2] 
 36%|###6      | 178/494 [08:43<10:15,  1.95s/it]
[2025-07-31 11:14:17,529: WARNING/ForkPoolWorker-16] 
 44%|####3     | 215/494 [09:35<08:24,  1.81s/it]
[2025-07-31 11:14:17,904: WARNING/ForkPoolWorker-15] 
 38%|###7      | 187/494 [09:45<10:58,  2.15s/it]
[2025-07-31 11:14:18,950: WARNING/ForkPoolWorker-1] 
 41%|####1     | 204/494 [08:50<09:10,  1.90s/it]
[2025-07-31 11:14:19,207: WARNING/ForkPoolWorker-2] 
 36%|###6      | 179/494 [08:45<09:56,  1.89s/it]
[2025-07-31 11:14:19,304: WARNING/ForkPoolWorker-16] 
 44%|####3     | 216/494 [09:37<08:20,  1.80s/it]
[2025-07-31 11:14:20,042: WARNING/ForkPoolWorker-15] 
 38%|###8      | 188/494 [09:47<10:55,  2.14s/it]
[2025-07-31 11:14:20,832: WARNING/ForkPoolWorker-1] 
 41%|####1     | 205/494 [08:52<09:07,  1.89s/it]
[2025-07-31 11:14:21,049: WARNING/ForkPoolWorker-2] 
 36%|###6      | 180/494 [08:47<09:49,  1.88s/it]
[2025-07-31 11:14:21,127: WARNING/ForkPoolWorker-16] 
 44%|####3     | 217/494 [09:39<08:20,  1.81s/it]
[2025-07-31 11:14:22,097: WARNING/ForkPoolWorker-15] 
 38%|###8      | 189/494 [09:49<10:45,  2.12s/it]
[2025-07-31 11:14:22,731: WARNING/ForkPoolWorker-1] 
 42%|####1     | 206/494 [08:54<09:05,  1.90s/it]
[2025-07-31 11:14:23,159: WARNING/ForkPoolWorker-2] 
 37%|###6      | 181/494 [08:49<10:09,  1.95s/it]
[2025-07-31 11:14:23,182: WARNING/ForkPoolWorker-16] 
 44%|####4     | 218/494 [09:41<08:39,  1.88s/it]
[2025-07-31 11:14:24,160: WARNING/ForkPoolWorker-15] 
 38%|###8      | 190/494 [09:51<10:38,  2.10s/it]
[2025-07-31 11:14:24,586: WARNING/ForkPoolWorker-1] 
 42%|####1     | 207/494 [08:56<09:00,  1.88s/it]
[2025-07-31 11:14:25,143: WARNING/ForkPoolWorker-2] 
 37%|###6      | 182/494 [08:51<10:11,  1.96s/it]
[2025-07-31 11:14:25,182: WARNING/ForkPoolWorker-16] 
 44%|####4     | 219/494 [09:43<08:47,  1.92s/it]
[2025-07-31 11:14:25,995: WARNING/ForkPoolWorker-15] 
 39%|###8      | 191/494 [09:53<10:12,  2.02s/it]
[2025-07-31 11:14:26,566: WARNING/ForkPoolWorker-1] 
 42%|####2     | 208/494 [08:58<09:06,  1.91s/it]
[2025-07-31 11:14:27,202: WARNING/ForkPoolWorker-2] 
 37%|###7      | 183/494 [08:53<10:18,  1.99s/it]
[2025-07-31 11:14:27,221: WARNING/ForkPoolWorker-16] 
 45%|####4     | 220/494 [09:45<08:55,  1.95s/it]
[2025-07-31 11:14:28,021: WARNING/ForkPoolWorker-15] 
 39%|###8      | 192/494 [09:55<10:10,  2.02s/it]
[2025-07-31 11:14:28,689: WARNING/ForkPoolWorker-1] 
 42%|####2     | 209/494 [09:00<09:22,  1.98s/it]
[2025-07-31 11:14:29,175: WARNING/ForkPoolWorker-2] 
 37%|###7      | 184/494 [08:55<10:15,  1.98s/it]
[2025-07-31 11:14:29,192: WARNING/ForkPoolWorker-16] 
 45%|####4     | 221/494 [09:47<08:54,  1.96s/it]
[2025-07-31 11:14:30,021: WARNING/ForkPoolWorker-15] 
 39%|###9      | 193/494 [09:57<10:06,  2.02s/it]
[2025-07-31 11:14:30,792: WARNING/ForkPoolWorker-1] 
 43%|####2     | 210/494 [09:02<09:31,  2.01s/it]
[2025-07-31 11:14:31,116: WARNING/ForkPoolWorker-16] 
 45%|####4     | 222/494 [09:49<08:49,  1.95s/it]
[2025-07-31 11:14:31,155: WARNING/ForkPoolWorker-2] 
 37%|###7      | 185/494 [08:57<10:12,  1.98s/it]
[2025-07-31 11:14:31,970: WARNING/ForkPoolWorker-15] 
 39%|###9      | 194/494 [09:59<09:58,  2.00s/it]
[2025-07-31 11:14:32,791: WARNING/ForkPoolWorker-1] 
 43%|####2     | 211/494 [09:04<09:28,  2.01s/it]
[2025-07-31 11:14:33,165: WARNING/ForkPoolWorker-16] 
 45%|####5     | 223/494 [09:51<08:56,  1.98s/it]
[2025-07-31 11:14:33,239: WARNING/ForkPoolWorker-2] 
 38%|###7      | 186/494 [08:59<10:20,  2.01s/it]
[2025-07-31 11:14:34,073: WARNING/ForkPoolWorker-15] 
 39%|###9      | 195/494 [10:01<10:06,  2.03s/it]
[2025-07-31 11:14:34,744: WARNING/ForkPoolWorker-1] 
 43%|####2     | 212/494 [09:06<09:21,  1.99s/it]
[2025-07-31 11:14:35,251: WARNING/ForkPoolWorker-16] 
 45%|####5     | 224/494 [09:53<09:02,  2.01s/it]
[2025-07-31 11:14:35,406: WARNING/ForkPoolWorker-2] 
 38%|###7      | 187/494 [09:01<10:32,  2.06s/it]
[2025-07-31 11:14:36,008: WARNING/ForkPoolWorker-15] 
 40%|###9      | 196/494 [10:03<09:56,  2.00s/it]
[2025-07-31 11:14:36,489: WARNING/ForkPoolWorker-1] 
 43%|####3     | 213/494 [09:08<08:59,  1.92s/it]
[2025-07-31 11:14:37,186: WARNING/ForkPoolWorker-16] 
 46%|####5     | 225/494 [09:55<08:54,  1.99s/it]
[2025-07-31 11:14:37,355: WARNING/ForkPoolWorker-2] 
 38%|###8      | 188/494 [09:03<10:20,  2.03s/it]
[2025-07-31 11:14:38,081: WARNING/ForkPoolWorker-15] 
 40%|###9      | 197/494 [10:05<10:00,  2.02s/it]
[2025-07-31 11:14:38,563: WARNING/ForkPoolWorker-1] 
 43%|####3     | 214/494 [09:10<09:10,  1.96s/it]
[2025-07-31 11:14:39,236: WARNING/ForkPoolWorker-16] 
 46%|####5     | 226/494 [09:57<08:57,  2.01s/it]
[2025-07-31 11:14:39,263: WARNING/ForkPoolWorker-2] 
 38%|###8      | 189/494 [09:05<10:07,  1.99s/it]
[2025-07-31 11:14:40,156: WARNING/ForkPoolWorker-15] 
 40%|####      | 198/494 [10:07<10:03,  2.04s/it]
[2025-07-31 11:14:40,581: WARNING/ForkPoolWorker-1] 
 44%|####3     | 215/494 [09:12<09:12,  1.98s/it]
[2025-07-31 11:14:41,290: WARNING/ForkPoolWorker-2] 
 38%|###8      | 190/494 [09:07<10:08,  2.00s/it]
[2025-07-31 11:14:41,308: WARNING/ForkPoolWorker-16] 
 46%|####5     | 227/494 [09:59<09:01,  2.03s/it]
[2025-07-31 11:14:42,137: WARNING/ForkPoolWorker-15] 
 40%|####      | 199/494 [10:09<09:56,  2.02s/it]
[2025-07-31 11:14:42,482: WARNING/ForkPoolWorker-1] 
 44%|####3     | 216/494 [09:14<09:03,  1.96s/it]
[2025-07-31 11:14:43,231: WARNING/ForkPoolWorker-2] 
 39%|###8      | 191/494 [09:09<10:00,  1.98s/it]
[2025-07-31 11:14:43,503: WARNING/ForkPoolWorker-16] 
 46%|####6     | 228/494 [10:01<09:12,  2.08s/it]
[2025-07-31 11:14:44,135: WARNING/ForkPoolWorker-15] 
 40%|####      | 200/494 [10:11<09:52,  2.01s/it]
[2025-07-31 11:14:44,269: WARNING/ForkPoolWorker-1] 
 44%|####3     | 217/494 [09:15<08:47,  1.91s/it]
[2025-07-31 11:14:45,246: WARNING/ForkPoolWorker-2] 
 39%|###8      | 192/494 [09:11<10:01,  1.99s/it]
[2025-07-31 11:14:45,522: WARNING/ForkPoolWorker-16] 
 46%|####6     | 229/494 [10:03<09:05,  2.06s/it]
[2025-07-31 11:14:46,269: WARNING/ForkPoolWorker-15] 
 41%|####      | 201/494 [10:13<10:00,  2.05s/it]
[2025-07-31 11:14:46,271: WARNING/ForkPoolWorker-1] 
 44%|####4     | 218/494 [09:17<08:53,  1.93s/it]
[2025-07-31 11:14:47,273: WARNING/ForkPoolWorker-2] 
 39%|###9      | 193/494 [09:13<10:02,  2.00s/it]
[2025-07-31 11:14:47,642: WARNING/ForkPoolWorker-16] 
 47%|####6     | 230/494 [10:06<09:08,  2.08s/it]
[2025-07-31 11:14:48,251: WARNING/ForkPoolWorker-1] 
 44%|####4     | 219/494 [09:19<08:55,  1.95s/it]
[2025-07-31 11:14:48,508: WARNING/ForkPoolWorker-15] 
 41%|####      | 202/494 [10:15<10:15,  2.11s/it]
[2025-07-31 11:14:49,095: WARNING/ForkPoolWorker-2] 
 39%|###9      | 194/494 [09:15<09:44,  1.95s/it]
[2025-07-31 11:14:49,594: WARNING/ForkPoolWorker-16] 
 47%|####6     | 231/494 [10:07<08:56,  2.04s/it]
[2025-07-31 11:14:50,169: WARNING/ForkPoolWorker-1] 
 45%|####4     | 220/494 [09:21<08:51,  1.94s/it]
[2025-07-31 11:14:50,585: WARNING/ForkPoolWorker-15] 
 41%|####1     | 203/494 [10:17<10:10,  2.10s/it]
[2025-07-31 11:14:51,018: WARNING/ForkPoolWorker-1] 
 45%|####4     | 221/494 [09:22<07:20,  1.61s/it]
[2025-07-31 11:14:51,154: WARNING/ForkPoolWorker-2] 
 39%|###9      | 195/494 [09:17<09:52,  1.98s/it]
[2025-07-31 11:14:51,578: WARNING/ForkPoolWorker-16] 
 47%|####6     | 232/494 [10:09<08:50,  2.02s/it]
[2025-07-31 11:14:52,688: WARNING/ForkPoolWorker-15] 
 41%|####1     | 204/494 [10:19<10:08,  2.10s/it]
[2025-07-31 11:14:52,894: WARNING/ForkPoolWorker-1] 
 45%|####4     | 222/494 [09:24<07:40,  1.69s/it]
[2025-07-31 11:14:53,426: WARNING/ForkPoolWorker-2] 
 40%|###9      | 196/494 [09:19<10:16,  2.07s/it]
[2025-07-31 11:14:53,659: WARNING/ForkPoolWorker-16] 
 47%|####7     | 233/494 [10:12<08:52,  2.04s/it]
[2025-07-31 11:14:54,571: WARNING/ForkPoolWorker-15] 
 41%|####1     | 205/494 [10:21<09:47,  2.03s/it]
[2025-07-31 11:14:54,887: WARNING/ForkPoolWorker-1] 
 45%|####5     | 223/494 [09:26<08:02,  1.78s/it]
[2025-07-31 11:14:55,332: WARNING/ForkPoolWorker-2] 
 40%|###9      | 197/494 [09:21<09:59,  2.02s/it]
[2025-07-31 11:14:55,580: WARNING/ForkPoolWorker-16] 
 47%|####7     | 234/494 [10:13<08:41,  2.00s/it]
[2025-07-31 11:14:56,653: WARNING/ForkPoolWorker-15] 
 42%|####1     | 206/494 [10:23<09:50,  2.05s/it]
[2025-07-31 11:14:56,954: WARNING/ForkPoolWorker-1] 
 45%|####5     | 224/494 [09:28<08:24,  1.87s/it]
[2025-07-31 11:14:57,164: WARNING/ForkPoolWorker-2] 
 40%|####      | 198/494 [09:23<09:41,  1.96s/it]
[2025-07-31 11:14:57,322: WARNING/ForkPoolWorker-16] 
 48%|####7     | 235/494 [10:15<08:18,  1.93s/it]
[2025-07-31 11:14:58,702: WARNING/ForkPoolWorker-15] 
 42%|####1     | 207/494 [10:25<09:48,  2.05s/it]
[2025-07-31 11:14:58,986: WARNING/ForkPoolWorker-1] 
 46%|####5     | 225/494 [09:30<08:35,  1.92s/it]
[2025-07-31 11:14:59,166: WARNING/ForkPoolWorker-2] 
 40%|####      | 199/494 [09:25<09:42,  1.98s/it]
[2025-07-31 11:14:59,192: WARNING/ForkPoolWorker-16] 
 48%|####7     | 236/494 [10:17<08:12,  1.91s/it]
[2025-07-31 11:15:00,860: WARNING/ForkPoolWorker-15] 
 42%|####2     | 208/494 [10:28<09:54,  2.08s/it]
[2025-07-31 11:15:01,044: WARNING/ForkPoolWorker-1] 
 46%|####5     | 226/494 [09:32<08:45,  1.96s/it]
[2025-07-31 11:15:01,099: WARNING/ForkPoolWorker-2] 
 40%|####      | 200/494 [09:27<09:37,  1.96s/it]
[2025-07-31 11:15:01,159: WARNING/ForkPoolWorker-16] 
 48%|####7     | 237/494 [10:19<08:15,  1.93s/it]
[2025-07-31 11:15:02,957: WARNING/ForkPoolWorker-15] 
 42%|####2     | 209/494 [10:30<09:54,  2.09s/it]
[2025-07-31 11:15:03,034: WARNING/ForkPoolWorker-2] 
 41%|####      | 201/494 [09:29<09:32,  1.95s/it]
[2025-07-31 11:15:03,044: WARNING/ForkPoolWorker-1] 
 46%|####5     | 227/494 [09:34<08:46,  1.97s/it]
[2025-07-31 11:15:03,221: WARNING/ForkPoolWorker-16] 
 48%|####8     | 238/494 [10:21<08:23,  1.97s/it]
[2025-07-31 11:15:04,872: WARNING/ForkPoolWorker-16] 
 48%|####8     | 239/494 [10:23<07:57,  1.87s/it]
[2025-07-31 11:15:05,014: WARNING/ForkPoolWorker-2] 
 41%|####      | 202/494 [09:31<09:32,  1.96s/it]
[2025-07-31 11:15:05,084: WARNING/ForkPoolWorker-1] 
 46%|####6     | 228/494 [09:36<08:49,  1.99s/it]
[2025-07-31 11:15:05,140: WARNING/ForkPoolWorker-15] 
 43%|####2     | 210/494 [10:32<10:00,  2.12s/it]
[2025-07-31 11:15:06,602: WARNING/ForkPoolWorker-16] 
 49%|####8     | 240/494 [10:25<07:44,  1.83s/it]
[2025-07-31 11:15:07,103: WARNING/ForkPoolWorker-1] 
 46%|####6     | 229/494 [09:38<08:50,  2.00s/it]
[2025-07-31 11:15:07,114: WARNING/ForkPoolWorker-2] 
 41%|####1     | 203/494 [09:33<09:42,  2.00s/it]
[2025-07-31 11:15:07,118: WARNING/ForkPoolWorker-15] 
 43%|####2     | 211/494 [10:34<09:47,  2.07s/it]
[2025-07-31 11:15:08,822: WARNING/ForkPoolWorker-16] 
 49%|####8     | 241/494 [10:27<08:12,  1.95s/it]
[2025-07-31 11:15:09,200: WARNING/ForkPoolWorker-1] 
 47%|####6     | 230/494 [09:40<08:55,  2.03s/it]
[2025-07-31 11:15:09,285: WARNING/ForkPoolWorker-15] 
 43%|####2     | 212/494 [10:36<09:52,  2.10s/it]
[2025-07-31 11:15:09,315: WARNING/ForkPoolWorker-2] 
 41%|####1     | 204/494 [09:35<09:58,  2.06s/it]
[2025-07-31 11:15:10,942: WARNING/ForkPoolWorker-16] 
 49%|####8     | 242/494 [10:29<08:23,  2.00s/it]
[2025-07-31 11:15:11,232: WARNING/ForkPoolWorker-1] 
 47%|####6     | 231/494 [09:42<08:53,  2.03s/it]
[2025-07-31 11:15:11,233: WARNING/ForkPoolWorker-15] 
 43%|####3     | 213/494 [10:38<09:37,  2.06s/it]
[2025-07-31 11:15:11,382: WARNING/ForkPoolWorker-2] 
 41%|####1     | 205/494 [09:37<09:56,  2.06s/it]
[2025-07-31 11:15:13,090: WARNING/ForkPoolWorker-16] 
 49%|####9     | 243/494 [10:31<08:31,  2.04s/it]
[2025-07-31 11:15:13,151: WARNING/ForkPoolWorker-1] 
 47%|####6     | 232/494 [09:44<08:43,  2.00s/it]
[2025-07-31 11:15:13,409: WARNING/ForkPoolWorker-2] 
 42%|####1     | 206/494 [09:39<09:51,  2.05s/it]
[2025-07-31 11:15:13,453: WARNING/ForkPoolWorker-15] 
 43%|####3     | 214/494 [10:40<09:49,  2.11s/it]
[2025-07-31 11:15:15,212: WARNING/ForkPoolWorker-16] 
 49%|####9     | 244/494 [10:33<08:37,  2.07s/it]
[2025-07-31 11:15:15,229: WARNING/ForkPoolWorker-1] 
 47%|####7     | 233/494 [09:46<08:47,  2.02s/it]
[2025-07-31 11:15:15,539: WARNING/ForkPoolWorker-2] 
 42%|####1     | 207/494 [09:41<09:55,  2.08s/it]
[2025-07-31 11:15:15,594: WARNING/ForkPoolWorker-15] 
 44%|####3     | 215/494 [10:42<09:50,  2.12s/it]
[2025-07-31 11:15:17,353: WARNING/ForkPoolWorker-16] 
 50%|####9     | 245/494 [10:35<08:40,  2.09s/it]
[2025-07-31 11:15:17,381: WARNING/ForkPoolWorker-1] 
 47%|####7     | 234/494 [09:49<08:55,  2.06s/it]
[2025-07-31 11:15:17,691: WARNING/ForkPoolWorker-15] 
 44%|####3     | 216/494 [10:44<09:46,  2.11s/it]
[2025-07-31 11:15:17,818: WARNING/ForkPoolWorker-2] 
 42%|####2     | 208/494 [09:43<10:11,  2.14s/it]
[2025-07-31 11:15:19,161: WARNING/ForkPoolWorker-1] 
 48%|####7     | 235/494 [09:50<08:31,  1.98s/it]
[2025-07-31 11:15:19,424: WARNING/ForkPoolWorker-16] 
 50%|####9     | 246/494 [10:37<08:36,  2.08s/it]
[2025-07-31 11:15:19,634: WARNING/ForkPoolWorker-2] 
 42%|####2     | 209/494 [09:45<09:41,  2.04s/it]
[2025-07-31 11:15:19,822: WARNING/ForkPoolWorker-15] 
 44%|####3     | 217/494 [10:46<09:46,  2.12s/it]
[2025-07-31 11:15:21,226: WARNING/ForkPoolWorker-1] 
 48%|####7     | 236/494 [09:52<08:36,  2.00s/it]
[2025-07-31 11:15:21,310: WARNING/ForkPoolWorker-16] 
 50%|#####     | 247/494 [10:39<08:20,  2.02s/it]
[2025-07-31 11:15:21,558: WARNING/ForkPoolWorker-2] 
 43%|####2     | 210/494 [09:47<09:29,  2.01s/it]
[2025-07-31 11:15:22,036: WARNING/ForkPoolWorker-15] 
 44%|####4     | 218/494 [10:49<09:52,  2.15s/it]
[2025-07-31 11:15:23,098: WARNING/ForkPoolWorker-1] 
 48%|####7     | 237/494 [09:54<08:24,  1.96s/it]
[2025-07-31 11:15:23,316: WARNING/ForkPoolWorker-16] 
 50%|#####     | 248/494 [10:41<08:16,  2.02s/it]
[2025-07-31 11:15:23,480: WARNING/ForkPoolWorker-2] 
 43%|####2     | 211/494 [09:49<09:20,  1.98s/it]
[2025-07-31 11:15:24,127: WARNING/ForkPoolWorker-15] 
 44%|####4     | 219/494 [10:51<09:45,  2.13s/it]
[2025-07-31 11:15:24,967: WARNING/ForkPoolWorker-1] 
 48%|####8     | 238/494 [09:56<08:15,  1.94s/it]
[2025-07-31 11:15:25,209: WARNING/ForkPoolWorker-16] 
 50%|#####     | 249/494 [10:43<08:05,  1.98s/it]
[2025-07-31 11:15:25,505: WARNING/ForkPoolWorker-2] 
 43%|####2     | 212/494 [09:51<09:21,  1.99s/it]
[2025-07-31 11:15:26,383: WARNING/ForkPoolWorker-15] 
 45%|####4     | 220/494 [10:53<09:53,  2.17s/it]
[2025-07-31 11:15:26,799: WARNING/ForkPoolWorker-1] 
 48%|####8     | 239/494 [09:58<08:05,  1.90s/it]
[2025-07-31 11:15:27,095: WARNING/ForkPoolWorker-16] 
 51%|#####     | 250/494 [10:45<07:56,  1.95s/it]
[2025-07-31 11:15:27,338: WARNING/ForkPoolWorker-2] 
 43%|####3     | 213/494 [09:53<09:07,  1.95s/it]
[2025-07-31 11:15:28,613: WARNING/ForkPoolWorker-1] 
 49%|####8     | 240/494 [10:00<07:56,  1.88s/it]
[2025-07-31 11:15:28,629: WARNING/ForkPoolWorker-15] 
 45%|####4     | 221/494 [10:55<09:58,  2.19s/it]
[2025-07-31 11:15:28,963: WARNING/ForkPoolWorker-16] 
 51%|#####     | 251/494 [10:47<07:48,  1.93s/it]
[2025-07-31 11:15:29,159: WARNING/ForkPoolWorker-2] 
 43%|####3     | 214/494 [09:55<08:54,  1.91s/it]
[2025-07-31 11:15:30,505: WARNING/ForkPoolWorker-1] 
 49%|####8     | 241/494 [10:02<07:56,  1.88s/it]
[2025-07-31 11:15:30,743: WARNING/ForkPoolWorker-15] 
 45%|####4     | 222/494 [10:57<09:49,  2.17s/it]
[2025-07-31 11:15:30,929: WARNING/ForkPoolWorker-16] 
 51%|#####1    | 252/494 [10:49<07:49,  1.94s/it]
[2025-07-31 11:15:31,035: WARNING/ForkPoolWorker-2] 
 44%|####3     | 215/494 [09:57<08:49,  1.90s/it]
[2025-07-31 11:15:32,397: WARNING/ForkPoolWorker-1] 
 49%|####8     | 242/494 [10:04<07:54,  1.88s/it]
[2025-07-31 11:15:32,773: WARNING/ForkPoolWorker-15] 
 45%|####5     | 223/494 [10:59<09:36,  2.13s/it]
[2025-07-31 11:15:32,872: WARNING/ForkPoolWorker-16] 
 51%|#####1    | 253/494 [10:51<07:47,  1.94s/it]
[2025-07-31 11:15:32,946: WARNING/ForkPoolWorker-2] 
 44%|####3     | 216/494 [09:58<08:48,  1.90s/it]
[2025-07-31 11:15:34,330: WARNING/ForkPoolWorker-1] 
 49%|####9     | 243/494 [10:06<07:56,  1.90s/it]
[2025-07-31 11:15:34,698: WARNING/ForkPoolWorker-16] 
 51%|#####1    | 254/494 [10:53<07:37,  1.91s/it]
[2025-07-31 11:15:34,852: WARNING/ForkPoolWorker-2] 
 44%|####3     | 217/494 [10:00<08:47,  1.90s/it]
[2025-07-31 11:15:34,915: WARNING/ForkPoolWorker-15] 
 45%|####5     | 224/494 [11:02<09:35,  2.13s/it]
[2025-07-31 11:15:36,452: WARNING/ForkPoolWorker-1] 
 49%|####9     | 244/494 [10:08<08:11,  1.97s/it]
[2025-07-31 11:15:36,922: WARNING/ForkPoolWorker-16] 
 52%|#####1    | 255/494 [10:55<07:58,  2.00s/it]
[2025-07-31 11:15:37,050: WARNING/ForkPoolWorker-2] 
 44%|####4     | 218/494 [10:03<09:09,  1.99s/it]
[2025-07-31 11:15:37,089: WARNING/ForkPoolWorker-15] 
 46%|####5     | 225/494 [11:04<09:36,  2.14s/it]
[2025-07-31 11:15:38,369: WARNING/ForkPoolWorker-1] 
 50%|####9     | 245/494 [10:10<08:05,  1.95s/it]
[2025-07-31 11:15:38,979: WARNING/ForkPoolWorker-16] 
 52%|#####1    | 256/494 [10:57<08:00,  2.02s/it]
[2025-07-31 11:15:39,033: WARNING/ForkPoolWorker-2] 
 44%|####4     | 219/494 [10:05<09:07,  1.99s/it]
[2025-07-31 11:15:39,119: WARNING/ForkPoolWorker-15] 
 46%|####5     | 226/494 [11:06<09:25,  2.11s/it]
[2025-07-31 11:15:40,394: WARNING/ForkPoolWorker-1] 
 50%|####9     | 246/494 [10:12<08:09,  1.97s/it]
[2025-07-31 11:15:40,921: WARNING/ForkPoolWorker-16] 
 52%|#####2    | 257/494 [10:59<07:52,  2.00s/it]
[2025-07-31 11:15:41,126: WARNING/ForkPoolWorker-15] 
 46%|####5     | 227/494 [11:08<09:14,  2.08s/it]
[2025-07-31 11:15:41,143: WARNING/ForkPoolWorker-2] 
 45%|####4     | 220/494 [10:07<09:15,  2.03s/it]
[2025-07-31 11:15:42,332: WARNING/ForkPoolWorker-1] 
 50%|#####     | 247/494 [10:14<08:04,  1.96s/it]
[2025-07-31 11:15:42,743: WARNING/ForkPoolWorker-16] 
 52%|#####2    | 258/494 [11:01<07:38,  1.94s/it]
[2025-07-31 11:15:42,971: WARNING/ForkPoolWorker-2] 
 45%|####4     | 221/494 [10:08<08:56,  1.97s/it]
[2025-07-31 11:15:43,270: WARNING/ForkPoolWorker-15] 
 46%|####6     | 228/494 [11:10<09:18,  2.10s/it]
[2025-07-31 11:15:44,221: WARNING/ForkPoolWorker-1] 
 50%|#####     | 248/494 [10:15<07:57,  1.94s/it]
[2025-07-31 11:15:44,708: WARNING/ForkPoolWorker-16] 
 52%|#####2    | 259/494 [11:03<07:38,  1.95s/it]
[2025-07-31 11:15:44,725: WARNING/ForkPoolWorker-2] 
 45%|####4     | 222/494 [10:10<08:37,  1.90s/it]
[2025-07-31 11:15:45,456: WARNING/ForkPoolWorker-15] 
 46%|####6     | 229/494 [11:12<09:22,  2.12s/it]
[2025-07-31 11:15:46,072: WARNING/ForkPoolWorker-1] 
 50%|#####     | 249/494 [10:17<07:48,  1.91s/it]
[2025-07-31 11:15:46,631: WARNING/ForkPoolWorker-16] 
 53%|#####2    | 260/494 [11:05<07:34,  1.94s/it]
[2025-07-31 11:15:46,804: WARNING/ForkPoolWorker-2] 
 45%|####5     | 223/494 [10:12<08:49,  1.96s/it]
[2025-07-31 11:15:47,494: WARNING/ForkPoolWorker-15] 
 47%|####6     | 230/494 [11:14<09:14,  2.10s/it]
[2025-07-31 11:15:47,932: WARNING/ForkPoolWorker-1] 
 51%|#####     | 250/494 [10:19<07:43,  1.90s/it]
[2025-07-31 11:15:48,485: WARNING/ForkPoolWorker-16] 
 53%|#####2    | 261/494 [11:06<07:26,  1.92s/it]
[2025-07-31 11:15:48,705: WARNING/ForkPoolWorker-2] 
 45%|####5     | 224/494 [10:14<08:43,  1.94s/it]
[2025-07-31 11:15:49,650: WARNING/ForkPoolWorker-15] 
 47%|####6     | 231/494 [11:16<09:16,  2.12s/it]
[2025-07-31 11:15:49,960: WARNING/ForkPoolWorker-1] 
 51%|#####     | 251/494 [10:21<07:50,  1.94s/it]
[2025-07-31 11:15:50,474: WARNING/ForkPoolWorker-16] 
 53%|#####3    | 262/494 [11:08<07:29,  1.94s/it]
[2025-07-31 11:15:50,578: WARNING/ForkPoolWorker-2] 
 46%|####5     | 225/494 [10:16<08:36,  1.92s/it]
[2025-07-31 11:15:51,723: WARNING/ForkPoolWorker-15] 
 47%|####6     | 232/494 [11:18<09:11,  2.10s/it]
[2025-07-31 11:15:51,966: WARNING/ForkPoolWorker-1] 
 51%|#####1    | 252/494 [10:23<07:53,  1.96s/it]
[2025-07-31 11:15:52,445: WARNING/ForkPoolWorker-16] 
 53%|#####3    | 263/494 [11:10<07:29,  1.95s/it]
[2025-07-31 11:15:52,517: WARNING/ForkPoolWorker-2] 
 46%|####5     | 226/494 [10:18<08:35,  1.93s/it]
[2025-07-31 11:15:53,888: WARNING/ForkPoolWorker-15] 
 47%|####7     | 233/494 [11:21<09:13,  2.12s/it]
[2025-07-31 11:15:54,014: WARNING/ForkPoolWorker-1] 
 51%|#####1    | 253/494 [10:25<07:58,  1.98s/it]
[2025-07-31 11:15:54,455: WARNING/ForkPoolWorker-16] 
 53%|#####3    | 264/494 [11:12<07:32,  1.97s/it]
[2025-07-31 11:15:54,620: WARNING/ForkPoolWorker-2] 
 46%|####5     | 227/494 [10:20<08:48,  1.98s/it]
[2025-07-31 11:15:55,868: WARNING/ForkPoolWorker-15] 
 47%|####7     | 234/494 [11:23<08:59,  2.08s/it]
[2025-07-31 11:15:55,989: WARNING/ForkPoolWorker-1] 
 51%|#####1    | 254/494 [10:27<07:55,  1.98s/it]
[2025-07-31 11:15:56,256: WARNING/ForkPoolWorker-16] 
 54%|#####3    | 265/494 [11:14<07:18,  1.92s/it]
[2025-07-31 11:15:56,644: WARNING/ForkPoolWorker-2] 
 46%|####6     | 228/494 [10:22<08:49,  1.99s/it]
[2025-07-31 11:15:57,899: WARNING/ForkPoolWorker-1] 
 52%|#####1    | 255/494 [10:29<07:48,  1.96s/it]
[2025-07-31 11:15:58,097: WARNING/ForkPoolWorker-16] 
 54%|#####3    | 266/494 [11:16<07:11,  1.89s/it]
[2025-07-31 11:15:58,120: WARNING/ForkPoolWorker-15] 
 48%|####7     | 235/494 [11:25<09:12,  2.13s/it]
[2025-07-31 11:15:58,541: WARNING/ForkPoolWorker-2] 
 46%|####6     | 229/494 [10:24<08:40,  1.96s/it]
[2025-07-31 11:15:59,801: WARNING/ForkPoolWorker-16] 
 54%|#####4    | 267/494 [11:18<06:56,  1.84s/it]
[2025-07-31 11:15:59,837: WARNING/ForkPoolWorker-1] 
 52%|#####1    | 256/494 [10:31<07:44,  1.95s/it]
[2025-07-31 11:16:00,221: WARNING/ForkPoolWorker-15] 
 48%|####7     | 236/494 [11:27<09:07,  2.12s/it]
[2025-07-31 11:16:00,313: WARNING/ForkPoolWorker-2] 
 47%|####6     | 230/494 [10:26<08:23,  1.91s/it]
[2025-07-31 11:16:01,462: WARNING/ForkPoolWorker-16] 
 54%|#####4    | 268/494 [11:19<06:43,  1.78s/it]
[2025-07-31 11:16:01,605: WARNING/ForkPoolWorker-1] 
 52%|#####2    | 257/494 [10:33<07:29,  1.90s/it]
[2025-07-31 11:16:02,171: WARNING/ForkPoolWorker-2] 
 47%|####6     | 231/494 [10:28<08:17,  1.89s/it]
[2025-07-31 11:16:02,664: WARNING/ForkPoolWorker-15] 
 48%|####7     | 237/494 [11:29<09:30,  2.22s/it]
[2025-07-31 11:16:03,251: WARNING/ForkPoolWorker-16] 
 54%|#####4    | 269/494 [11:21<06:41,  1.79s/it]
[2025-07-31 11:16:03,357: WARNING/ForkPoolWorker-1] 
 52%|#####2    | 258/494 [10:35<07:17,  1.85s/it]
[2025-07-31 11:16:03,975: WARNING/ForkPoolWorker-2] 
 47%|####6     | 232/494 [10:29<08:08,  1.87s/it]
[2025-07-31 11:16:04,853: WARNING/ForkPoolWorker-15] 
 48%|####8     | 238/494 [11:32<09:25,  2.21s/it]
[2025-07-31 11:16:05,103: WARNING/ForkPoolWorker-16] 
 55%|#####4    | 270/494 [11:23<06:44,  1.81s/it]
[2025-07-31 11:16:05,411: WARNING/ForkPoolWorker-1] 
 52%|#####2    | 259/494 [10:37<07:29,  1.91s/it]
[2025-07-31 11:16:05,727: WARNING/ForkPoolWorker-2] 
 47%|####7     | 233/494 [10:31<07:57,  1.83s/it]
[2025-07-31 11:16:06,950: WARNING/ForkPoolWorker-16] 
 55%|#####4    | 271/494 [11:25<06:45,  1.82s/it]
[2025-07-31 11:16:06,979: WARNING/ForkPoolWorker-15] 
 48%|####8     | 239/494 [11:34<09:17,  2.18s/it]
[2025-07-31 11:16:07,361: WARNING/ForkPoolWorker-1] 
 53%|#####2    | 260/494 [10:39<07:30,  1.92s/it]
[2025-07-31 11:16:07,627: WARNING/ForkPoolWorker-2] 
 47%|####7     | 234/494 [10:33<08:01,  1.85s/it]
[2025-07-31 11:16:08,962: WARNING/ForkPoolWorker-16] 
 55%|#####5    | 272/494 [11:27<06:56,  1.88s/it]
[2025-07-31 11:16:09,037: WARNING/ForkPoolWorker-15] 
 49%|####8     | 240/494 [11:36<09:05,  2.15s/it]
[2025-07-31 11:16:09,394: WARNING/ForkPoolWorker-1] 
 53%|#####2    | 261/494 [10:41<07:36,  1.96s/it]
[2025-07-31 11:16:09,535: WARNING/ForkPoolWorker-2] 
 48%|####7     | 235/494 [10:35<08:03,  1.87s/it]
[2025-07-31 11:16:10,869: WARNING/ForkPoolWorker-16] 
 55%|#####5    | 273/494 [11:29<06:56,  1.89s/it]
[2025-07-31 11:16:11,078: WARNING/ForkPoolWorker-15] 
 49%|####8     | 241/494 [11:38<08:55,  2.11s/it]
[2025-07-31 11:16:11,477: WARNING/ForkPoolWorker-2] 
 48%|####7     | 236/494 [10:37<08:07,  1.89s/it]
[2025-07-31 11:16:11,532: WARNING/ForkPoolWorker-1] 
 53%|#####3    | 262/494 [10:43<07:46,  2.01s/it]
[2025-07-31 11:16:13,108: WARNING/ForkPoolWorker-16] 
 55%|#####5    | 274/494 [11:31<07:18,  1.99s/it]
[2025-07-31 11:16:13,294: WARNING/ForkPoolWorker-2] 
 48%|####7     | 237/494 [10:39<08:00,  1.87s/it]
[2025-07-31 11:16:13,300: WARNING/ForkPoolWorker-15] 
 49%|####8     | 242/494 [11:40<09:01,  2.15s/it]
[2025-07-31 11:16:13,535: WARNING/ForkPoolWorker-1] 
 53%|#####3    | 263/494 [10:45<07:44,  2.01s/it]
[2025-07-31 11:16:15,073: WARNING/ForkPoolWorker-16] 
 56%|#####5    | 275/494 [11:33<07:14,  1.98s/it]
[2025-07-31 11:16:15,347: WARNING/ForkPoolWorker-1] 
 53%|#####3    | 264/494 [10:47<07:28,  1.95s/it]
[2025-07-31 11:16:15,371: WARNING/ForkPoolWorker-2] 
 48%|####8     | 238/494 [10:41<08:14,  1.93s/it]
[2025-07-31 11:16:15,476: WARNING/ForkPoolWorker-15] 
 49%|####9     | 243/494 [11:42<09:01,  2.16s/it]
[2025-07-31 11:16:16,908: WARNING/ForkPoolWorker-16] 
 56%|#####5    | 276/494 [11:35<07:02,  1.94s/it]
[2025-07-31 11:16:17,190: WARNING/ForkPoolWorker-2] 
 48%|####8     | 239/494 [10:43<08:03,  1.90s/it]
[2025-07-31 11:16:17,486: WARNING/ForkPoolWorker-1] 
 54%|#####3    | 265/494 [10:49<07:39,  2.01s/it]
[2025-07-31 11:16:17,972: WARNING/ForkPoolWorker-15] 
 49%|####9     | 244/494 [11:45<09:24,  2.26s/it]
[2025-07-31 11:16:18,813: WARNING/ForkPoolWorker-16] 
 56%|#####6    | 277/494 [11:37<06:58,  1.93s/it]
[2025-07-31 11:16:19,148: WARNING/ForkPoolWorker-2] 
 49%|####8     | 240/494 [10:45<08:06,  1.92s/it]
[2025-07-31 11:16:19,421: WARNING/ForkPoolWorker-1] 
 54%|#####3    | 266/494 [10:51<07:32,  1.99s/it]
[2025-07-31 11:16:20,199: WARNING/ForkPoolWorker-15] 
 50%|####9     | 245/494 [11:47<09:19,  2.25s/it]
[2025-07-31 11:16:20,784: WARNING/ForkPoolWorker-16] 
 56%|#####6    | 278/494 [11:39<06:59,  1.94s/it]
[2025-07-31 11:16:21,311: WARNING/ForkPoolWorker-2] 
 49%|####8     | 241/494 [10:47<08:23,  1.99s/it]
[2025-07-31 11:16:21,545: WARNING/ForkPoolWorker-1] 
 54%|#####4    | 267/494 [10:53<07:40,  2.03s/it]
[2025-07-31 11:16:22,367: WARNING/ForkPoolWorker-15] 
 50%|####9     | 246/494 [11:49<09:11,  2.22s/it]
[2025-07-31 11:16:22,692: WARNING/ForkPoolWorker-16] 
 56%|#####6    | 279/494 [11:41<06:55,  1.93s/it]
[2025-07-31 11:16:23,306: WARNING/ForkPoolWorker-2] 
 49%|####8     | 242/494 [10:49<08:21,  1.99s/it]
[2025-07-31 11:16:23,495: WARNING/ForkPoolWorker-1] 
 54%|#####4    | 268/494 [10:55<07:32,  2.00s/it]
[2025-07-31 11:16:24,376: WARNING/ForkPoolWorker-15] 
 50%|#####     | 247/494 [11:51<08:53,  2.16s/it]
[2025-07-31 11:16:24,515: WARNING/ForkPoolWorker-16] 
 57%|#####6    | 280/494 [11:42<06:46,  1.90s/it]
[2025-07-31 11:16:25,185: WARNING/ForkPoolWorker-1] 
 54%|#####4    | 269/494 [10:56<07:09,  1.91s/it]
[2025-07-31 11:16:25,296: WARNING/ForkPoolWorker-2] 
 49%|####9     | 243/494 [10:51<08:19,  1.99s/it]
[2025-07-31 11:16:26,398: WARNING/ForkPoolWorker-15] 
 50%|#####     | 248/494 [11:53<08:41,  2.12s/it]
[2025-07-31 11:16:26,526: WARNING/ForkPoolWorker-16] 
 57%|#####6    | 281/494 [11:44<06:51,  1.93s/it]
[2025-07-31 11:16:27,089: WARNING/ForkPoolWorker-1] 
 55%|#####4    | 270/494 [10:58<07:07,  1.91s/it]
[2025-07-31 11:16:27,191: WARNING/ForkPoolWorker-2] 
 49%|####9     | 244/494 [10:53<08:10,  1.96s/it]
[2025-07-31 11:16:28,449: WARNING/ForkPoolWorker-16] 
 57%|#####7    | 282/494 [11:46<06:49,  1.93s/it]
[2025-07-31 11:16:28,476: WARNING/ForkPoolWorker-15] 
 50%|#####     | 249/494 [11:55<08:36,  2.11s/it]
[2025-07-31 11:16:28,979: WARNING/ForkPoolWorker-1] 
 55%|#####4    | 271/494 [11:00<07:04,  1.90s/it]
[2025-07-31 11:16:29,049: WARNING/ForkPoolWorker-2] 
 50%|####9     | 245/494 [10:55<08:00,  1.93s/it]
[2025-07-31 11:16:30,409: WARNING/ForkPoolWorker-16] 
 57%|#####7    | 283/494 [11:48<06:49,  1.94s/it]
[2025-07-31 11:16:30,430: WARNING/ForkPoolWorker-15] 
 51%|#####     | 250/494 [11:57<08:22,  2.06s/it]
[2025-07-31 11:16:31,042: WARNING/ForkPoolWorker-1] 
 55%|#####5    | 272/494 [11:02<07:13,  1.95s/it]
[2025-07-31 11:16:31,088: WARNING/ForkPoolWorker-2] 
 50%|####9     | 246/494 [10:57<08:06,  1.96s/it]
[2025-07-31 11:16:32,215: WARNING/ForkPoolWorker-16] 
 57%|#####7    | 284/494 [11:50<06:38,  1.90s/it]
[2025-07-31 11:16:32,536: WARNING/ForkPoolWorker-15] 
 51%|#####     | 251/494 [11:59<08:24,  2.07s/it]
[2025-07-31 11:16:32,978: WARNING/ForkPoolWorker-1] 
 55%|#####5    | 273/494 [11:04<07:10,  1.95s/it]
[2025-07-31 11:16:33,072: WARNING/ForkPoolWorker-2] 
 50%|#####     | 247/494 [10:59<08:06,  1.97s/it]
[2025-07-31 11:16:34,087: WARNING/ForkPoolWorker-16] 
 58%|#####7    | 285/494 [11:52<06:35,  1.89s/it]
[2025-07-31 11:16:34,596: WARNING/ForkPoolWorker-15] 
 51%|#####1    | 252/494 [12:01<08:20,  2.07s/it]
[2025-07-31 11:16:34,775: WARNING/ForkPoolWorker-1] 
 55%|#####5    | 274/494 [11:06<06:58,  1.90s/it]
[2025-07-31 11:16:35,106: WARNING/ForkPoolWorker-2] 
 50%|#####     | 248/494 [11:01<08:09,  1.99s/it]
[2025-07-31 11:16:35,849: WARNING/ForkPoolWorker-16] 
 58%|#####7    | 286/494 [11:54<06:25,  1.85s/it]
[2025-07-31 11:16:36,681: WARNING/ForkPoolWorker-15] 
 51%|#####1    | 253/494 [12:03<08:19,  2.07s/it]
[2025-07-31 11:16:36,690: WARNING/ForkPoolWorker-1] 
 56%|#####5    | 275/494 [11:08<06:57,  1.91s/it]
[2025-07-31 11:16:37,160: WARNING/ForkPoolWorker-2] 
 50%|#####     | 249/494 [11:03<08:12,  2.01s/it]
[2025-07-31 11:16:37,611: WARNING/ForkPoolWorker-16] 
 58%|#####8    | 287/494 [11:56<06:17,  1.83s/it]
[2025-07-31 11:16:38,693: WARNING/ForkPoolWorker-15] 
 51%|#####1    | 254/494 [12:05<08:13,  2.06s/it]
[2025-07-31 11:16:38,699: WARNING/ForkPoolWorker-1] 
 56%|#####5    | 276/494 [11:10<07:02,  1.94s/it]
[2025-07-31 11:16:39,208: WARNING/ForkPoolWorker-2] 
 51%|#####     | 250/494 [11:05<08:12,  2.02s/it]
[2025-07-31 11:16:39,537: WARNING/ForkPoolWorker-16] 
 58%|#####8    | 288/494 [11:57<06:22,  1.86s/it]
[2025-07-31 11:16:40,739: WARNING/ForkPoolWorker-15] 
 52%|#####1    | 255/494 [12:07<08:10,  2.05s/it]
[2025-07-31 11:16:40,791: WARNING/ForkPoolWorker-1] 
 56%|#####6    | 277/494 [11:12<07:10,  1.98s/it]
[2025-07-31 11:16:41,136: WARNING/ForkPoolWorker-2] 
 51%|#####     | 251/494 [11:07<08:04,  1.99s/it]
[2025-07-31 11:16:41,562: WARNING/ForkPoolWorker-16] 
 59%|#####8    | 289/494 [11:59<06:30,  1.91s/it]
[2025-07-31 11:16:42,865: WARNING/ForkPoolWorker-1] 
 56%|#####6    | 278/494 [11:14<07:14,  2.01s/it]
[2025-07-31 11:16:42,965: WARNING/ForkPoolWorker-15] 
 52%|#####1    | 256/494 [12:10<08:20,  2.10s/it]
[2025-07-31 11:16:43,274: WARNING/ForkPoolWorker-2] 
 51%|#####1    | 252/494 [11:09<08:12,  2.04s/it]
[2025-07-31 11:16:43,561: WARNING/ForkPoolWorker-16] 
 59%|#####8    | 290/494 [12:01<06:34,  1.93s/it]
[2025-07-31 11:16:44,956: WARNING/ForkPoolWorker-1] 
 56%|#####6    | 279/494 [11:16<07:17,  2.03s/it]
[2025-07-31 11:16:45,044: WARNING/ForkPoolWorker-15] 
 52%|#####2    | 257/494 [12:12<08:16,  2.10s/it]
[2025-07-31 11:16:45,167: WARNING/ForkPoolWorker-2] 
 51%|#####1    | 253/494 [11:11<08:00,  1.99s/it]
[2025-07-31 11:16:45,439: WARNING/ForkPoolWorker-16] 
 59%|#####8    | 291/494 [12:03<06:29,  1.92s/it]
[2025-07-31 11:16:47,109: WARNING/ForkPoolWorker-1] 
 57%|#####6    | 280/494 [11:18<07:22,  2.07s/it]
[2025-07-31 11:16:47,174: WARNING/ForkPoolWorker-15] 
 52%|#####2    | 258/494 [12:14<08:17,  2.11s/it]
[2025-07-31 11:16:47,193: WARNING/ForkPoolWorker-2] 
 51%|#####1    | 254/494 [11:13<08:00,  2.00s/it]
[2025-07-31 11:16:47,698: WARNING/ForkPoolWorker-16] 
 59%|#####9    | 292/494 [12:06<06:47,  2.02s/it]
[2025-07-31 11:16:49,159: WARNING/ForkPoolWorker-1] 
 57%|#####6    | 281/494 [11:20<07:19,  2.06s/it]
[2025-07-31 11:16:49,241: WARNING/ForkPoolWorker-2] 
 52%|#####1    | 255/494 [11:15<08:01,  2.02s/it]
[2025-07-31 11:16:49,255: WARNING/ForkPoolWorker-15] 
 52%|#####2    | 259/494 [12:16<08:13,  2.10s/it]
[2025-07-31 11:16:49,580: WARNING/ForkPoolWorker-16] 
 59%|#####9    | 293/494 [12:07<06:37,  1.98s/it]
[2025-07-31 11:16:51,303: WARNING/ForkPoolWorker-1] 
 57%|#####7    | 282/494 [11:22<07:22,  2.09s/it]
[2025-07-31 11:16:51,492: WARNING/ForkPoolWorker-2] 
 52%|#####1    | 256/494 [11:17<08:16,  2.09s/it]
[2025-07-31 11:16:51,549: WARNING/ForkPoolWorker-15] 
 53%|#####2    | 260/494 [12:18<08:24,  2.16s/it]
[2025-07-31 11:16:51,583: WARNING/ForkPoolWorker-16] 
 60%|#####9    | 294/494 [12:09<06:37,  1.99s/it]
[2025-07-31 11:16:53,585: WARNING/ForkPoolWorker-16] 
 60%|#####9    | 295/494 [12:11<06:36,  1.99s/it]
[2025-07-31 11:16:53,585: WARNING/ForkPoolWorker-1] 
 57%|#####7    | 283/494 [11:25<07:32,  2.15s/it]
[2025-07-31 11:16:53,615: WARNING/ForkPoolWorker-2] 
 52%|#####2    | 257/494 [11:19<08:17,  2.10s/it]
[2025-07-31 11:16:53,767: WARNING/ForkPoolWorker-15] 
 53%|#####2    | 261/494 [12:20<08:26,  2.18s/it]
[2025-07-31 11:16:55,697: WARNING/ForkPoolWorker-16] 
 60%|#####9    | 296/494 [12:14<06:41,  2.03s/it]
[2025-07-31 11:16:55,792: WARNING/ForkPoolWorker-1] 
 57%|#####7    | 284/494 [11:27<07:34,  2.16s/it]
[2025-07-31 11:16:55,838: WARNING/ForkPoolWorker-2] 
 52%|#####2    | 258/494 [11:21<08:23,  2.14s/it]
[2025-07-31 11:16:55,920: WARNING/ForkPoolWorker-15] 
 53%|#####3    | 262/494 [12:23<08:23,  2.17s/it]
[2025-07-31 11:16:57,494: WARNING/ForkPoolWorker-16] 
 60%|######    | 297/494 [12:15<06:25,  1.96s/it]
[2025-07-31 11:16:57,651: WARNING/ForkPoolWorker-1] 
 58%|#####7    | 285/494 [11:29<07:13,  2.07s/it]
[2025-07-31 11:16:57,683: WARNING/ForkPoolWorker-2] 
 52%|#####2    | 259/494 [11:23<08:01,  2.05s/it]
[2025-07-31 11:16:58,063: WARNING/ForkPoolWorker-15] 
 53%|#####3    | 263/494 [12:25<08:19,  2.16s/it]
[2025-07-31 11:16:59,465: WARNING/ForkPoolWorker-16] 
 60%|######    | 298/494 [12:17<06:24,  1.96s/it]
[2025-07-31 11:16:59,585: WARNING/ForkPoolWorker-1] 
 58%|#####7    | 286/494 [11:31<07:02,  2.03s/it]
[2025-07-31 11:16:59,699: WARNING/ForkPoolWorker-2] 
 53%|#####2    | 260/494 [11:25<07:56,  2.04s/it]
[2025-07-31 11:17:00,035: WARNING/ForkPoolWorker-15] 
 53%|#####3    | 264/494 [12:27<08:03,  2.10s/it]
[2025-07-31 11:17:01,430: WARNING/ForkPoolWorker-16] 
 61%|######    | 299/494 [12:19<06:22,  1.96s/it]
[2025-07-31 11:17:01,723: WARNING/ForkPoolWorker-1] 
 58%|#####8    | 287/494 [11:33<07:07,  2.06s/it]
[2025-07-31 11:17:01,861: WARNING/ForkPoolWorker-2] 
 53%|#####2    | 261/494 [11:27<08:03,  2.08s/it]
[2025-07-31 11:17:02,155: WARNING/ForkPoolWorker-15] 
 54%|#####3    | 265/494 [12:29<08:03,  2.11s/it]
[2025-07-31 11:17:03,369: WARNING/ForkPoolWorker-16] 
 61%|######    | 300/494 [12:21<06:19,  1.96s/it]
[2025-07-31 11:17:03,751: WARNING/ForkPoolWorker-1] 
 58%|#####8    | 288/494 [11:35<07:02,  2.05s/it]
[2025-07-31 11:17:03,752: WARNING/ForkPoolWorker-2] 
 53%|#####3    | 262/494 [11:29<07:48,  2.02s/it]
[2025-07-31 11:17:04,268: WARNING/ForkPoolWorker-15] 
 54%|#####3    | 266/494 [12:31<08:01,  2.11s/it]
[2025-07-31 11:17:05,153: WARNING/ForkPoolWorker-16] 
 61%|######    | 301/494 [12:23<06:07,  1.90s/it]
[2025-07-31 11:17:05,641: WARNING/ForkPoolWorker-2] 
 53%|#####3    | 263/494 [11:31<07:37,  1.98s/it]
[2025-07-31 11:17:05,801: WARNING/ForkPoolWorker-1] 
 59%|#####8    | 289/494 [11:37<07:00,  2.05s/it]
[2025-07-31 11:17:06,248: WARNING/ForkPoolWorker-15] 
 54%|#####4    | 267/494 [12:33<07:49,  2.07s/it]
[2025-07-31 11:17:06,995: WARNING/ForkPoolWorker-16] 
 61%|######1   | 302/494 [12:25<06:02,  1.89s/it]
[2025-07-31 11:17:07,406: WARNING/ForkPoolWorker-2] 
 53%|#####3    | 264/494 [11:33<07:20,  1.92s/it]
[2025-07-31 11:17:07,597: WARNING/ForkPoolWorker-1] 
 59%|#####8    | 290/494 [11:39<06:42,  1.98s/it]
[2025-07-31 11:17:08,469: WARNING/ForkPoolWorker-15] 
 54%|#####4    | 268/494 [12:35<07:58,  2.12s/it]
[2025-07-31 11:17:08,790: WARNING/ForkPoolWorker-16] 
 61%|######1   | 303/494 [12:27<05:54,  1.86s/it]
[2025-07-31 11:17:09,320: WARNING/ForkPoolWorker-1] 
 59%|#####8    | 291/494 [11:40<06:25,  1.90s/it]
[2025-07-31 11:17:09,438: WARNING/ForkPoolWorker-2] 
 54%|#####3    | 265/494 [11:35<07:26,  1.95s/it]
[2025-07-31 11:17:10,523: WARNING/ForkPoolWorker-15] 
 54%|#####4    | 269/494 [12:37<07:52,  2.10s/it]
[2025-07-31 11:17:10,721: WARNING/ForkPoolWorker-16] 
 62%|######1   | 304/494 [12:29<05:57,  1.88s/it]
[2025-07-31 11:17:11,103: WARNING/ForkPoolWorker-1] 
 59%|#####9    | 292/494 [11:42<06:16,  1.86s/it]
[2025-07-31 11:17:11,558: WARNING/ForkPoolWorker-2] 
 54%|#####3    | 266/494 [11:37<07:36,  2.00s/it]
[2025-07-31 11:17:12,524: WARNING/ForkPoolWorker-15] 
 55%|#####4    | 270/494 [12:39<07:43,  2.07s/it]
[2025-07-31 11:17:12,701: WARNING/ForkPoolWorker-16] 
 62%|######1   | 305/494 [12:31<06:01,  1.91s/it]
[2025-07-31 11:17:13,053: WARNING/ForkPoolWorker-1] 
 59%|#####9    | 293/494 [11:44<06:19,  1.89s/it]
[2025-07-31 11:17:13,500: WARNING/ForkPoolWorker-2] 
 54%|#####4    | 267/494 [11:39<07:30,  1.98s/it]
[2025-07-31 11:17:14,370: WARNING/ForkPoolWorker-15] 
 55%|#####4    | 271/494 [12:41<07:26,  2.00s/it]
[2025-07-31 11:17:14,748: WARNING/ForkPoolWorker-16] 
 62%|######1   | 306/494 [12:33<06:06,  1.95s/it]
[2025-07-31 11:17:15,001: WARNING/ForkPoolWorker-1] 
 60%|#####9    | 294/494 [11:46<06:21,  1.91s/it]
[2025-07-31 11:17:15,375: WARNING/ForkPoolWorker-2] 
 54%|#####4    | 268/494 [11:41<07:20,  1.95s/it]
[2025-07-31 11:17:16,609: WARNING/ForkPoolWorker-15] 
 55%|#####5    | 272/494 [12:43<07:40,  2.07s/it]
[2025-07-31 11:17:16,671: WARNING/ForkPoolWorker-16] 
 62%|######2   | 307/494 [12:35<06:03,  1.94s/it]
[2025-07-31 11:17:16,865: WARNING/ForkPoolWorker-1] 
 60%|#####9    | 295/494 [11:48<06:17,  1.89s/it]
[2025-07-31 11:17:17,251: WARNING/ForkPoolWorker-2] 
 54%|#####4    | 269/494 [11:43<07:13,  1.93s/it]
[2025-07-31 11:17:18,448: WARNING/ForkPoolWorker-16] 
 62%|######2   | 308/494 [12:36<05:51,  1.89s/it]
[2025-07-31 11:17:18,744: WARNING/ForkPoolWorker-15] 
 55%|#####5    | 273/494 [12:45<07:42,  2.09s/it]
[2025-07-31 11:17:18,776: WARNING/ForkPoolWorker-1] 
 60%|#####9    | 296/494 [11:50<06:16,  1.90s/it]
[2025-07-31 11:17:19,081: WARNING/ForkPoolWorker-2] 
 55%|#####4    | 270/494 [11:45<07:05,  1.90s/it]
[2025-07-31 11:17:20,351: WARNING/ForkPoolWorker-16] 
 63%|######2   | 309/494 [12:38<05:50,  1.90s/it]
[2025-07-31 11:17:20,706: WARNING/ForkPoolWorker-1] 
 60%|######    | 297/494 [11:52<06:14,  1.90s/it]
[2025-07-31 11:17:20,892: WARNING/ForkPoolWorker-15] 
 55%|#####5    | 274/494 [12:48<07:43,  2.11s/it]
[2025-07-31 11:17:21,088: WARNING/ForkPoolWorker-2] 
 55%|#####4    | 271/494 [11:47<07:10,  1.93s/it]
[2025-07-31 11:17:22,287: WARNING/ForkPoolWorker-16] 
 63%|######2   | 310/494 [12:40<05:51,  1.91s/it]
[2025-07-31 11:17:22,541: WARNING/ForkPoolWorker-1] 
 60%|######    | 298/494 [11:54<06:10,  1.89s/it]
[2025-07-31 11:17:22,962: WARNING/ForkPoolWorker-2] 
 55%|#####5    | 272/494 [11:48<07:04,  1.91s/it]
[2025-07-31 11:17:22,966: WARNING/ForkPoolWorker-15] 
 56%|#####5    | 275/494 [12:50<07:39,  2.10s/it]
[2025-07-31 11:17:24,018: WARNING/ForkPoolWorker-16] 
 63%|######2   | 311/494 [12:42<05:39,  1.86s/it]
[2025-07-31 11:17:24,338: WARNING/ForkPoolWorker-1] 
 61%|######    | 299/494 [11:56<06:02,  1.86s/it]
[2025-07-31 11:17:24,776: WARNING/ForkPoolWorker-2] 
 55%|#####5    | 273/494 [11:50<06:56,  1.88s/it]
[2025-07-31 11:17:25,168: WARNING/ForkPoolWorker-15] 
 56%|#####5    | 276/494 [12:52<07:44,  2.13s/it]
[2025-07-31 11:17:25,911: WARNING/ForkPoolWorker-16] 
 63%|######3   | 312/494 [12:44<05:39,  1.87s/it]
[2025-07-31 11:17:26,298: WARNING/ForkPoolWorker-1] 
 61%|######    | 300/494 [11:57<06:06,  1.89s/it]
[2025-07-31 11:17:26,566: WARNING/ForkPoolWorker-2] 
 55%|#####5    | 274/494 [11:52<06:48,  1.86s/it]
[2025-07-31 11:17:27,348: WARNING/ForkPoolWorker-15] 
 56%|#####6    | 277/494 [12:54<07:45,  2.14s/it]
[2025-07-31 11:17:27,704: WARNING/ForkPoolWorker-16] 
 63%|######3   | 313/494 [12:46<05:33,  1.84s/it]
[2025-07-31 11:17:28,304: WARNING/ForkPoolWorker-1] 
 61%|######    | 301/494 [11:59<06:11,  1.93s/it]
[2025-07-31 11:17:28,393: WARNING/ForkPoolWorker-2] 
 56%|#####5    | 275/494 [11:54<06:44,  1.85s/it]
[2025-07-31 11:17:29,340: WARNING/ForkPoolWorker-15] 
 56%|#####6    | 278/494 [12:56<07:33,  2.10s/it]
[2025-07-31 11:17:29,528: WARNING/ForkPoolWorker-16] 
 64%|######3   | 314/494 [12:47<05:30,  1.84s/it]
[2025-07-31 11:17:30,124: WARNING/ForkPoolWorker-2] 
 56%|#####5    | 276/494 [11:56<06:35,  1.81s/it]
[2025-07-31 11:17:30,195: WARNING/ForkPoolWorker-1] 
 61%|######1   | 302/494 [12:01<06:07,  1.91s/it]
[2025-07-31 11:17:31,179: WARNING/ForkPoolWorker-16] 
 64%|######3   | 315/494 [12:49<05:19,  1.78s/it]
[2025-07-31 11:17:31,742: WARNING/ForkPoolWorker-15] 
 56%|#####6    | 279/494 [12:58<07:50,  2.19s/it]
[2025-07-31 11:17:32,004: WARNING/ForkPoolWorker-2] 
 56%|#####6    | 277/494 [11:58<06:37,  1.83s/it]
[2025-07-31 11:17:32,074: WARNING/ForkPoolWorker-1] 
 61%|######1   | 303/494 [12:03<06:03,  1.90s/it]
[2025-07-31 11:17:33,303: WARNING/ForkPoolWorker-16] 
 64%|######3   | 316/494 [12:51<05:35,  1.88s/it]
[2025-07-31 11:17:33,869: WARNING/ForkPoolWorker-15] 
 57%|#####6    | 280/494 [13:01<07:44,  2.17s/it]
[2025-07-31 11:17:34,062: WARNING/ForkPoolWorker-2] 
 56%|#####6    | 278/494 [12:00<06:50,  1.90s/it]
[2025-07-31 11:17:34,096: WARNING/ForkPoolWorker-1] 
 62%|######1   | 304/494 [12:05<06:08,  1.94s/it]
[2025-07-31 11:17:35,027: WARNING/ForkPoolWorker-16] 
 64%|######4   | 317/494 [12:53<05:25,  1.84s/it]
[2025-07-31 11:17:35,840: WARNING/ForkPoolWorker-2] 
 56%|#####6    | 279/494 [12:01<06:40,  1.86s/it]
[2025-07-31 11:17:35,936: WARNING/ForkPoolWorker-1] 
 62%|######1   | 305/494 [12:07<06:00,  1.91s/it]
[2025-07-31 11:17:36,152: WARNING/ForkPoolWorker-15] 
 57%|#####6    | 281/494 [13:03<07:49,  2.20s/it]
[2025-07-31 11:17:36,805: WARNING/ForkPoolWorker-16] 
 64%|######4   | 318/494 [12:55<05:20,  1.82s/it]
[2025-07-31 11:17:37,717: WARNING/ForkPoolWorker-2] 
 57%|#####6    | 280/494 [12:03<06:39,  1.87s/it]
[2025-07-31 11:17:37,906: WARNING/ForkPoolWorker-1] 
 62%|######1   | 306/494 [12:09<06:02,  1.93s/it]
[2025-07-31 11:17:38,444: WARNING/ForkPoolWorker-15] 
 57%|#####7    | 282/494 [13:05<07:52,  2.23s/it]
[2025-07-31 11:17:38,730: WARNING/ForkPoolWorker-16] 
 65%|######4   | 319/494 [12:57<05:23,  1.85s/it]
[2025-07-31 11:17:39,821: WARNING/ForkPoolWorker-2] 
 57%|#####6    | 281/494 [12:05<06:52,  1.94s/it]
[2025-07-31 11:17:40,040: WARNING/ForkPoolWorker-1] 
 62%|######2   | 307/494 [12:11<06:12,  1.99s/it]
[2025-07-31 11:17:40,530: WARNING/ForkPoolWorker-15] 
 57%|#####7    | 283/494 [13:07<07:41,  2.19s/it]
[2025-07-31 11:17:40,969: WARNING/ForkPoolWorker-16] 
 65%|######4   | 320/494 [12:59<05:42,  1.97s/it]
[2025-07-31 11:17:41,802: WARNING/ForkPoolWorker-2] 
 57%|#####7    | 282/494 [12:07<06:53,  1.95s/it]
[2025-07-31 11:17:42,033: WARNING/ForkPoolWorker-1] 
 62%|######2   | 308/494 [12:13<06:10,  1.99s/it]
[2025-07-31 11:17:42,644: WARNING/ForkPoolWorker-15] 
 57%|#####7    | 284/494 [13:09<07:34,  2.17s/it]
[2025-07-31 11:17:42,903: WARNING/ForkPoolWorker-16] 
 65%|######4   | 321/494 [13:01<05:38,  1.96s/it]
[2025-07-31 11:17:43,709: WARNING/ForkPoolWorker-2] 
 57%|#####7    | 283/494 [12:09<06:48,  1.93s/it]
[2025-07-31 11:17:44,040: WARNING/ForkPoolWorker-1] 
 63%|######2   | 309/494 [12:15<06:09,  2.00s/it]
[2025-07-31 11:17:44,917: WARNING/ForkPoolWorker-15] 
 58%|#####7    | 285/494 [13:12<07:39,  2.20s/it]
[2025-07-31 11:17:45,043: WARNING/ForkPoolWorker-16] 
 65%|######5   | 322/494 [13:03<05:46,  2.01s/it]
[2025-07-31 11:17:45,842: WARNING/ForkPoolWorker-2] 
 57%|#####7    | 284/494 [12:11<06:59,  2.00s/it]
[2025-07-31 11:17:46,017: WARNING/ForkPoolWorker-1] 
 63%|######2   | 310/494 [12:17<06:06,  1.99s/it]
[2025-07-31 11:17:47,001: WARNING/ForkPoolWorker-16] 
 65%|######5   | 323/494 [13:05<05:41,  2.00s/it]
[2025-07-31 11:17:47,164: WARNING/ForkPoolWorker-15] 
 58%|#####7    | 286/494 [13:14<07:40,  2.21s/it]
[2025-07-31 11:17:47,844: WARNING/ForkPoolWorker-2] 
 58%|#####7    | 285/494 [12:13<06:57,  2.00s/it]
[2025-07-31 11:17:47,959: WARNING/ForkPoolWorker-1] 
 63%|######2   | 311/494 [12:19<06:01,  1.98s/it]
[2025-07-31 11:17:48,973: WARNING/ForkPoolWorker-16] 
 66%|######5   | 324/494 [13:07<05:38,  1.99s/it]
[2025-07-31 11:17:49,172: WARNING/ForkPoolWorker-15] 
 58%|#####8    | 287/494 [13:16<07:25,  2.15s/it]
[2025-07-31 11:17:49,733: WARNING/ForkPoolWorker-2] 
 58%|#####7    | 286/494 [12:15<06:48,  1.97s/it]
[2025-07-31 11:17:49,824: WARNING/ForkPoolWorker-1] 
 63%|######3   | 312/494 [12:21<05:53,  1.94s/it]
[2025-07-31 11:17:50,946: WARNING/ForkPoolWorker-16] 
 66%|######5   | 325/494 [13:09<05:35,  1.98s/it]
[2025-07-31 11:17:51,430: WARNING/ForkPoolWorker-15] 
 58%|#####8    | 288/494 [13:18<07:29,  2.18s/it]
[2025-07-31 11:17:51,661: WARNING/ForkPoolWorker-2] 
 58%|#####8    | 287/494 [12:17<06:44,  1.95s/it]
[2025-07-31 11:17:51,841: WARNING/ForkPoolWorker-1] 
 63%|######3   | 313/494 [12:23<05:54,  1.96s/it]
[2025-07-31 11:17:52,860: WARNING/ForkPoolWorker-16] 
 66%|######5   | 326/494 [13:11<05:28,  1.96s/it]
[2025-07-31 11:17:53,491: WARNING/ForkPoolWorker-2] 
 58%|#####8    | 288/494 [12:19<06:34,  1.92s/it]
[2025-07-31 11:17:53,682: WARNING/ForkPoolWorker-15] 
 59%|#####8    | 289/494 [13:20<07:31,  2.20s/it]
[2025-07-31 11:17:53,745: WARNING/ForkPoolWorker-1] 
 64%|######3   | 314/494 [12:25<05:50,  1.95s/it]
[2025-07-31 11:17:54,543: WARNING/ForkPoolWorker-16] 
 66%|######6   | 327/494 [13:12<05:14,  1.88s/it]
[2025-07-31 11:17:55,325: WARNING/ForkPoolWorker-2] 
 59%|#####8    | 289/494 [12:21<06:27,  1.89s/it]
[2025-07-31 11:17:55,496: WARNING/ForkPoolWorker-1] 
 64%|######3   | 315/494 [12:27<05:38,  1.89s/it]
[2025-07-31 11:17:55,804: WARNING/ForkPoolWorker-15] 
 59%|#####8    | 290/494 [13:22<07:24,  2.18s/it]
[2025-07-31 11:17:56,409: WARNING/ForkPoolWorker-16] 
 66%|######6   | 328/494 [13:14<05:11,  1.88s/it]
[2025-07-31 11:17:57,082: WARNING/ForkPoolWorker-2] 
 59%|#####8    | 290/494 [12:23<06:17,  1.85s/it]
[2025-07-31 11:17:57,190: WARNING/ForkPoolWorker-1] 
 64%|######3   | 316/494 [12:28<05:25,  1.83s/it]
[2025-07-31 11:17:57,841: WARNING/ForkPoolWorker-15] 
 59%|#####8    | 291/494 [13:25<07:13,  2.14s/it]
[2025-07-31 11:17:58,198: WARNING/ForkPoolWorker-16] 
 67%|######6   | 329/494 [13:16<05:05,  1.85s/it]
[2025-07-31 11:17:58,887: WARNING/ForkPoolWorker-1] 
 64%|######4   | 317/494 [12:30<05:16,  1.79s/it]
[2025-07-31 11:17:59,043: WARNING/ForkPoolWorker-2] 
 59%|#####8    | 291/494 [12:25<06:22,  1.88s/it]
[2025-07-31 11:17:59,857: WARNING/ForkPoolWorker-15] 
 59%|#####9    | 292/494 [13:27<07:04,  2.10s/it]
[2025-07-31 11:17:59,982: WARNING/ForkPoolWorker-16] 
 67%|######6   | 330/494 [13:18<05:00,  1.83s/it]
[2025-07-31 11:18:00,750: WARNING/ForkPoolWorker-1] 
 64%|######4   | 318/494 [12:32<05:18,  1.81s/it]
[2025-07-31 11:18:00,929: WARNING/ForkPoolWorker-2] 
 59%|#####9    | 292/494 [12:26<06:20,  1.89s/it]
[2025-07-31 11:18:01,778: WARNING/ForkPoolWorker-16] 
 67%|######7   | 331/494 [13:20<04:56,  1.82s/it]
[2025-07-31 11:18:01,911: WARNING/ForkPoolWorker-15] 
 59%|#####9    | 293/494 [13:29<06:59,  2.09s/it]
[2025-07-31 11:18:02,608: WARNING/ForkPoolWorker-1] 
 65%|######4   | 319/494 [12:34<05:19,  1.83s/it]
[2025-07-31 11:18:02,718: WARNING/ForkPoolWorker-2] 
 59%|#####9    | 293/494 [12:28<06:13,  1.86s/it]
[2025-07-31 11:18:03,574: WARNING/ForkPoolWorker-16] 
 67%|######7   | 332/494 [13:21<04:53,  1.81s/it]
[2025-07-31 11:18:03,980: WARNING/ForkPoolWorker-15] 
 60%|#####9    | 294/494 [13:31<06:56,  2.08s/it]
[2025-07-31 11:18:04,493: WARNING/ForkPoolWorker-1] 
 65%|######4   | 320/494 [12:36<05:20,  1.84s/it]
[2025-07-31 11:18:04,533: WARNING/ForkPoolWorker-2] 
 60%|#####9    | 294/494 [12:30<06:08,  1.84s/it]
[2025-07-31 11:18:05,808: WARNING/ForkPoolWorker-16] 
 67%|######7   | 333/494 [13:24<05:12,  1.94s/it]
[2025-07-31 11:18:06,218: WARNING/ForkPoolWorker-15] 
 60%|#####9    | 295/494 [13:33<07:03,  2.13s/it]
[2025-07-31 11:18:06,297: WARNING/ForkPoolWorker-1] 
 65%|######4   | 321/494 [12:37<05:16,  1.83s/it]
[2025-07-31 11:18:06,591: WARNING/ForkPoolWorker-2] 
 60%|#####9    | 295/494 [12:32<06:19,  1.91s/it]
[2025-07-31 11:18:07,739: WARNING/ForkPoolWorker-16] 
 68%|######7   | 334/494 [13:26<05:09,  1.94s/it]
[2025-07-31 11:18:07,976: WARNING/ForkPoolWorker-1] 
 65%|######5   | 322/494 [12:39<05:07,  1.79s/it]
[2025-07-31 11:18:08,449: WARNING/ForkPoolWorker-2] 
 60%|#####9    | 296/494 [12:34<06:14,  1.89s/it]
[2025-07-31 11:18:08,556: WARNING/ForkPoolWorker-15] 
 60%|#####9    | 296/494 [13:35<07:13,  2.19s/it]
[2025-07-31 11:18:09,668: WARNING/ForkPoolWorker-16] 
 68%|######7   | 335/494 [13:28<05:07,  1.93s/it]
[2025-07-31 11:18:09,751: WARNING/ForkPoolWorker-1] 
 65%|######5   | 323/494 [12:41<05:04,  1.78s/it]
[2025-07-31 11:18:10,527: WARNING/ForkPoolWorker-2] 
 60%|######    | 297/494 [12:36<06:23,  1.95s/it]
[2025-07-31 11:18:10,639: WARNING/ForkPoolWorker-15] 
 60%|######    | 297/494 [13:37<07:05,  2.16s/it]
[2025-07-31 11:18:11,549: WARNING/ForkPoolWorker-16] 
 68%|######8   | 336/494 [13:29<05:03,  1.92s/it]
[2025-07-31 11:18:11,908: WARNING/ForkPoolWorker-1] 
 66%|######5   | 324/494 [12:43<05:22,  1.90s/it]
[2025-07-31 11:18:12,464: WARNING/ForkPoolWorker-2] 
 60%|######    | 298/494 [12:38<06:21,  1.95s/it]
[2025-07-31 11:18:12,726: WARNING/ForkPoolWorker-15] 
 60%|######    | 298/494 [13:39<06:58,  2.14s/it]
[2025-07-31 11:18:13,371: WARNING/ForkPoolWorker-16] 
 68%|######8   | 337/494 [13:31<04:56,  1.89s/it]
[2025-07-31 11:18:13,879: WARNING/ForkPoolWorker-1] 
 66%|######5   | 325/494 [12:45<05:24,  1.92s/it]
[2025-07-31 11:18:14,367: WARNING/ForkPoolWorker-2] 
 61%|######    | 299/494 [12:40<06:16,  1.93s/it]
[2025-07-31 11:18:14,725: WARNING/ForkPoolWorker-15] 
 61%|######    | 299/494 [13:41<06:48,  2.10s/it]
[2025-07-31 11:18:15,250: WARNING/ForkPoolWorker-16] 
 68%|######8   | 338/494 [13:33<04:54,  1.89s/it]
[2025-07-31 11:18:16,126: WARNING/ForkPoolWorker-1] 
 66%|######5   | 326/494 [12:47<05:38,  2.02s/it]
[2025-07-31 11:18:16,437: WARNING/ForkPoolWorker-2] 
 61%|######    | 300/494 [12:42<06:22,  1.97s/it]
[2025-07-31 11:18:16,740: WARNING/ForkPoolWorker-15] 
 61%|######    | 300/494 [13:43<06:41,  2.07s/it]
[2025-07-31 11:18:17,273: WARNING/ForkPoolWorker-16] 
 69%|######8   | 339/494 [13:35<04:58,  1.93s/it]
[2025-07-31 11:18:17,851: WARNING/ForkPoolWorker-1] 
 66%|######6   | 327/494 [12:49<05:22,  1.93s/it]
[2025-07-31 11:18:18,544: WARNING/ForkPoolWorker-2] 
 61%|######    | 301/494 [12:44<06:28,  2.01s/it]
[2025-07-31 11:18:18,618: WARNING/ForkPoolWorker-15] 
 61%|######    | 301/494 [13:45<06:28,  2.01s/it]
[2025-07-31 11:18:19,333: WARNING/ForkPoolWorker-16] 
 69%|######8   | 340/494 [13:37<05:02,  1.97s/it]
[2025-07-31 11:18:19,802: WARNING/ForkPoolWorker-1] 
 66%|######6   | 328/494 [12:51<05:21,  1.94s/it]
[2025-07-31 11:18:20,494: WARNING/ForkPoolWorker-2] 
 61%|######1   | 302/494 [12:46<06:22,  1.99s/it]
[2025-07-31 11:18:20,608: WARNING/ForkPoolWorker-15] 
 61%|######1   | 302/494 [13:47<06:25,  2.01s/it]
[2025-07-31 11:18:21,463: WARNING/ForkPoolWorker-16] 
 69%|######9   | 341/494 [13:39<05:08,  2.02s/it]
[2025-07-31 11:18:21,939: WARNING/ForkPoolWorker-1] 
 67%|######6   | 329/494 [12:53<05:29,  2.00s/it]
[2025-07-31 11:18:22,331: WARNING/ForkPoolWorker-15] 
 61%|######1   | 303/494 [13:49<06:06,  1.92s/it]
[2025-07-31 11:18:22,573: WARNING/ForkPoolWorker-2] 
 61%|######1   | 303/494 [12:48<06:25,  2.02s/it]
[2025-07-31 11:18:23,569: WARNING/ForkPoolWorker-16] 
 69%|######9   | 342/494 [13:41<05:10,  2.04s/it]
[2025-07-31 11:18:23,965: WARNING/ForkPoolWorker-1] 
 67%|######6   | 330/494 [12:55<05:28,  2.01s/it]
[2025-07-31 11:18:24,315: WARNING/ForkPoolWorker-15] 
 62%|######1   | 304/494 [13:51<06:08,  1.94s/it]
[2025-07-31 11:18:24,564: WARNING/ForkPoolWorker-2] 
 62%|######1   | 304/494 [12:50<06:22,  2.01s/it]
[2025-07-31 11:18:25,384: WARNING/ForkPoolWorker-16] 
 69%|######9   | 343/494 [13:43<04:58,  1.97s/it]
[2025-07-31 11:18:25,901: WARNING/ForkPoolWorker-1] 
 67%|######7   | 331/494 [12:57<05:23,  1.98s/it]
[2025-07-31 11:18:26,519: WARNING/ForkPoolWorker-15] 
 62%|######1   | 305/494 [13:53<06:21,  2.02s/it]
[2025-07-31 11:18:26,529: WARNING/ForkPoolWorker-2] 
 62%|######1   | 305/494 [12:52<06:17,  2.00s/it]
[2025-07-31 11:18:27,253: WARNING/ForkPoolWorker-16] 
 70%|######9   | 344/494 [13:45<04:51,  1.94s/it]
[2025-07-31 11:18:27,702: WARNING/ForkPoolWorker-1] 
 67%|######7   | 332/494 [12:59<05:12,  1.93s/it]
[2025-07-31 11:18:28,364: WARNING/ForkPoolWorker-2] 
 62%|######1   | 306/494 [12:54<06:06,  1.95s/it]
[2025-07-31 11:18:28,569: WARNING/ForkPoolWorker-15] 
 62%|######1   | 306/494 [13:55<06:21,  2.03s/it]
[2025-07-31 11:18:29,104: WARNING/ForkPoolWorker-16] 
 70%|######9   | 345/494 [13:47<04:45,  1.92s/it]
[2025-07-31 11:18:29,617: WARNING/ForkPoolWorker-1] 
 67%|######7   | 333/494 [13:01<05:09,  1.92s/it]
[2025-07-31 11:18:30,259: WARNING/ForkPoolWorker-2] 
 62%|######2   | 307/494 [12:56<06:01,  1.93s/it]
[2025-07-31 11:18:30,665: WARNING/ForkPoolWorker-15] 
 62%|######2   | 307/494 [13:57<06:23,  2.05s/it]
[2025-07-31 11:18:30,918: WARNING/ForkPoolWorker-16] 
 70%|#######   | 346/494 [13:49<04:38,  1.88s/it]
[2025-07-31 11:18:31,405: WARNING/ForkPoolWorker-1] 
 68%|######7   | 334/494 [13:03<05:01,  1.88s/it]
[2025-07-31 11:18:32,014: WARNING/ForkPoolWorker-2] 
 62%|######2   | 308/494 [12:58<05:49,  1.88s/it]
[2025-07-31 11:18:32,673: WARNING/ForkPoolWorker-16] 
 70%|#######   | 347/494 [13:51<04:31,  1.85s/it]
[2025-07-31 11:18:32,773: WARNING/ForkPoolWorker-15] 
 62%|######2   | 308/494 [13:59<06:24,  2.07s/it]
[2025-07-31 11:18:33,356: WARNING/ForkPoolWorker-1] 
 68%|######7   | 335/494 [13:05<05:02,  1.90s/it]
[2025-07-31 11:18:33,812: WARNING/ForkPoolWorker-2] 
 63%|######2   | 309/494 [12:59<05:43,  1.85s/it]
[2025-07-31 11:18:34,518: WARNING/ForkPoolWorker-16] 
 70%|#######   | 348/494 [13:52<04:29,  1.85s/it]
[2025-07-31 11:18:34,966: WARNING/ForkPoolWorker-15] 
 63%|######2   | 309/494 [14:02<06:29,  2.10s/it]
[2025-07-31 11:18:35,194: WARNING/ForkPoolWorker-1] 
 68%|######8   | 336/494 [13:06<04:57,  1.88s/it]
[2025-07-31 11:18:35,568: WARNING/ForkPoolWorker-2] 
 63%|######2   | 310/494 [13:01<05:35,  1.83s/it]
[2025-07-31 11:18:36,369: WARNING/ForkPoolWorker-16] 
 71%|#######   | 349/494 [13:54<04:27,  1.85s/it]
[2025-07-31 11:18:37,000: WARNING/ForkPoolWorker-15] 
 63%|######2   | 310/494 [14:04<06:23,  2.08s/it]
[2025-07-31 11:18:37,078: WARNING/ForkPoolWorker-1] 
 68%|######8   | 337/494 [13:08<04:55,  1.88s/it]
[2025-07-31 11:18:37,463: WARNING/ForkPoolWorker-2] 
 63%|######2   | 311/494 [13:03<05:37,  1.85s/it]
[2025-07-31 11:18:38,114: WARNING/ForkPoolWorker-16] 
 71%|#######   | 350/494 [13:56<04:21,  1.82s/it]
[2025-07-31 11:18:38,794: WARNING/ForkPoolWorker-1] 
 68%|######8   | 338/494 [13:10<04:46,  1.83s/it]
[2025-07-31 11:18:39,297: WARNING/ForkPoolWorker-2] 
 63%|######3   | 312/494 [13:05<05:35,  1.84s/it]
[2025-07-31 11:18:39,388: WARNING/ForkPoolWorker-15] 
 63%|######2   | 311/494 [14:06<06:38,  2.17s/it]
[2025-07-31 11:18:39,813: WARNING/ForkPoolWorker-16] 
 71%|#######1  | 351/494 [13:58<04:14,  1.78s/it]
[2025-07-31 11:18:40,681: WARNING/ForkPoolWorker-1] 
 69%|######8   | 339/494 [13:12<04:46,  1.85s/it]
[2025-07-31 11:18:41,245: WARNING/ForkPoolWorker-2] 
 63%|######3   | 313/494 [13:07<05:39,  1.87s/it]
[2025-07-31 11:18:41,535: WARNING/ForkPoolWorker-15] 
 63%|######3   | 312/494 [14:08<06:34,  2.17s/it]
[2025-07-31 11:18:41,878: WARNING/ForkPoolWorker-16] 
 71%|#######1  | 352/494 [14:00<04:25,  1.87s/it]
[2025-07-31 11:18:42,584: WARNING/ForkPoolWorker-1] 
 69%|######8   | 340/494 [13:14<04:47,  1.87s/it]
[2025-07-31 11:18:43,237: WARNING/ForkPoolWorker-2] 
 64%|######3   | 314/494 [13:09<05:43,  1.91s/it]
[2025-07-31 11:18:43,686: WARNING/ForkPoolWorker-16] 
 71%|#######1  | 353/494 [14:02<04:20,  1.85s/it]
[2025-07-31 11:18:43,734: WARNING/ForkPoolWorker-15] 
 63%|######3   | 313/494 [14:10<06:33,  2.18s/it]
[2025-07-31 11:18:44,497: WARNING/ForkPoolWorker-1] 
 69%|######9   | 341/494 [13:16<04:47,  1.88s/it]
[2025-07-31 11:18:45,240: WARNING/ForkPoolWorker-2] 
 64%|######3   | 315/494 [13:11<05:46,  1.93s/it]
[2025-07-31 11:18:45,502: WARNING/ForkPoolWorker-16] 
 72%|#######1  | 354/494 [14:03<04:17,  1.84s/it]
[2025-07-31 11:18:45,894: WARNING/ForkPoolWorker-15] 
 64%|######3   | 314/494 [14:13<06:30,  2.17s/it]
[2025-07-31 11:18:46,494: WARNING/ForkPoolWorker-1] 
 69%|######9   | 342/494 [13:18<04:51,  1.92s/it]
[2025-07-31 11:18:47,180: WARNING/ForkPoolWorker-2] 
 64%|######3   | 316/494 [13:13<05:44,  1.94s/it]
[2025-07-31 11:18:47,330: WARNING/ForkPoolWorker-16] 
 72%|#######1  | 355/494 [14:05<04:15,  1.84s/it]
[2025-07-31 11:18:48,041: WARNING/ForkPoolWorker-15] 
 64%|######3   | 315/494 [14:15<06:27,  2.16s/it]
[2025-07-31 11:18:48,191: WARNING/ForkPoolWorker-1] 
 69%|######9   | 343/494 [13:19<04:39,  1.85s/it]
[2025-07-31 11:18:49,306: WARNING/ForkPoolWorker-2] 
 64%|######4   | 317/494 [13:15<05:52,  1.99s/it]
[2025-07-31 11:18:49,577: WARNING/ForkPoolWorker-16] 
 72%|#######2  | 356/494 [14:07<04:30,  1.96s/it]
[2025-07-31 11:18:50,090: WARNING/ForkPoolWorker-15] 
 64%|######3   | 316/494 [14:17<06:19,  2.13s/it]
[2025-07-31 11:18:50,400: WARNING/ForkPoolWorker-1] 
 70%|######9   | 344/494 [13:22<04:53,  1.96s/it]
[2025-07-31 11:18:51,235: WARNING/ForkPoolWorker-2] 
 64%|######4   | 318/494 [13:17<05:47,  1.97s/it]
[2025-07-31 11:18:51,600: WARNING/ForkPoolWorker-16] 
 72%|#######2  | 357/494 [14:10<04:31,  1.98s/it]
[2025-07-31 11:18:52,185: WARNING/ForkPoolWorker-15] 
 64%|######4   | 317/494 [14:19<06:15,  2.12s/it]
[2025-07-31 11:18:52,214: WARNING/ForkPoolWorker-1] 
 70%|######9   | 345/494 [13:23<04:45,  1.91s/it]
[2025-07-31 11:18:53,041: WARNING/ForkPoolWorker-2] 
 65%|######4   | 319/494 [13:19<05:36,  1.92s/it]
[2025-07-31 11:18:53,406: WARNING/ForkPoolWorker-16] 
 72%|#######2  | 358/494 [14:11<04:22,  1.93s/it]
[2025-07-31 11:18:54,199: WARNING/ForkPoolWorker-1] 
 70%|#######   | 346/494 [13:25<04:46,  1.94s/it]
[2025-07-31 11:18:54,364: WARNING/ForkPoolWorker-15] 
 64%|######4   | 318/494 [14:21<06:15,  2.14s/it]
[2025-07-31 11:18:54,800: WARNING/ForkPoolWorker-2] 
 65%|######4   | 320/494 [13:20<05:26,  1.87s/it]
[2025-07-31 11:18:55,105: WARNING/ForkPoolWorker-16] 
 73%|#######2  | 359/494 [14:13<04:10,  1.86s/it]
[2025-07-31 11:18:56,041: WARNING/ForkPoolWorker-1] 
 70%|#######   | 347/494 [13:27<04:40,  1.91s/it]
[2025-07-31 11:18:56,693: WARNING/ForkPoolWorker-2] 
 65%|######4   | 321/494 [13:22<05:25,  1.88s/it]
[2025-07-31 11:18:56,707: WARNING/ForkPoolWorker-15] 
 65%|######4   | 319/494 [14:23<06:24,  2.20s/it]
[2025-07-31 11:18:57,047: WARNING/ForkPoolWorker-16] 
 73%|#######2  | 360/494 [14:15<04:12,  1.88s/it]
[2025-07-31 11:18:57,857: WARNING/ForkPoolWorker-1] 
 70%|#######   | 348/494 [13:29<04:34,  1.88s/it]
[2025-07-31 11:18:58,683: WARNING/ForkPoolWorker-2] 
 65%|######5   | 322/494 [13:24<05:29,  1.91s/it]
[2025-07-31 11:18:58,855: WARNING/ForkPoolWorker-16] 
 73%|#######3  | 361/494 [14:17<04:07,  1.86s/it]
[2025-07-31 11:18:59,032: WARNING/ForkPoolWorker-15] 
 65%|######4   | 320/494 [14:26<06:29,  2.24s/it]
[2025-07-31 11:18:59,933: WARNING/ForkPoolWorker-1] 
 71%|#######   | 349/494 [13:31<04:41,  1.94s/it]
[2025-07-31 11:19:00,654: WARNING/ForkPoolWorker-2] 
 65%|######5   | 323/494 [13:26<05:30,  1.93s/it]
[2025-07-31 11:19:00,681: WARNING/ForkPoolWorker-16] 
 73%|#######3  | 362/494 [14:19<04:04,  1.85s/it]
[2025-07-31 11:19:01,325: WARNING/ForkPoolWorker-15] 
 65%|######4   | 321/494 [14:28<06:29,  2.25s/it]
[2025-07-31 11:19:02,001: WARNING/ForkPoolWorker-1] 
 71%|#######   | 350/494 [13:33<04:44,  1.98s/it]
[2025-07-31 11:19:02,527: WARNING/ForkPoolWorker-16] 
 73%|#######3  | 363/494 [14:20<04:02,  1.85s/it]
[2025-07-31 11:19:02,718: WARNING/ForkPoolWorker-2] 
 66%|######5   | 324/494 [13:28<05:34,  1.97s/it]
[2025-07-31 11:19:03,356: WARNING/ForkPoolWorker-15] 
 65%|######5   | 322/494 [14:30<06:16,  2.19s/it]
[2025-07-31 11:19:03,747: WARNING/ForkPoolWorker-1] 
 71%|#######1  | 351/494 [13:35<04:32,  1.91s/it]
[2025-07-31 11:19:04,442: WARNING/ForkPoolWorker-16] 
 74%|#######3  | 364/494 [14:22<04:02,  1.87s/it]
[2025-07-31 11:19:04,620: WARNING/ForkPoolWorker-2] 
 66%|######5   | 325/494 [13:30<05:29,  1.95s/it]
[2025-07-31 11:19:05,488: WARNING/ForkPoolWorker-15] 
 65%|######5   | 323/494 [14:32<06:11,  2.17s/it]
[2025-07-31 11:19:05,620: WARNING/ForkPoolWorker-1] 
 71%|#######1  | 352/494 [13:37<04:29,  1.90s/it]
[2025-07-31 11:19:06,087: WARNING/ForkPoolWorker-16] 
 74%|#######3  | 365/494 [14:24<03:52,  1.80s/it]
[2025-07-31 11:19:06,621: WARNING/ForkPoolWorker-1] 
 71%|#######1  | 353/494 [13:38<03:49,  1.63s/it]
[2025-07-31 11:19:06,832: WARNING/ForkPoolWorker-2] 
 66%|######5   | 326/494 [13:32<05:40,  2.03s/it]
[2025-07-31 11:19:06,953: WARNING/ForkPoolWorker-16] 
 74%|#######4  | 366/494 [14:25<03:14,  1.52s/it]
[2025-07-31 11:19:07,925: WARNING/ForkPoolWorker-1] 
 72%|#######1  | 354/494 [13:39<03:34,  1.53s/it]
[2025-07-31 11:19:08,004: WARNING/ForkPoolWorker-15] 
 66%|######5   | 324/494 [14:35<06:26,  2.27s/it]
[2025-07-31 11:19:08,461: WARNING/ForkPoolWorker-16] 
 74%|#######4  | 367/494 [14:26<03:12,  1.52s/it]
[2025-07-31 11:19:09,051: WARNING/ForkPoolWorker-2] 
 66%|######6   | 327/494 [13:35<05:48,  2.09s/it]
[2025-07-31 11:19:09,368: WARNING/ForkPoolWorker-16] 
 74%|#######4  | 368/494 [14:27<02:48,  1.33s/it]
[2025-07-31 11:19:09,993: WARNING/ForkPoolWorker-1] 
 72%|#######1  | 355/494 [13:41<03:55,  1.69s/it]
[2025-07-31 11:19:10,115: WARNING/ForkPoolWorker-16] 
 75%|#######4  | 369/494 [14:28<02:24,  1.16s/it]
[2025-07-31 11:19:10,779: WARNING/ForkPoolWorker-16] 
 75%|#######4  | 370/494 [14:29<02:05,  1.01s/it]
[2025-07-31 11:19:11,353: WARNING/ForkPoolWorker-15] 
 66%|######5   | 325/494 [14:38<07:18,  2.60s/it]
[2025-07-31 11:19:11,809: WARNING/ForkPoolWorker-16] 
 75%|#######5  | 371/494 [14:30<02:04,  1.02s/it]
[2025-07-31 11:19:12,224: WARNING/ForkPoolWorker-2] 
 66%|######6   | 328/494 [13:38<06:40,  2.41s/it]
[2025-07-31 11:19:12,910: WARNING/ForkPoolWorker-1] 
 72%|#######2  | 356/494 [13:44<04:44,  2.06s/it]
[2025-07-31 11:19:13,182: WARNING/ForkPoolWorker-16] 
 75%|#######5  | 372/494 [14:31<02:16,  1.12s/it]
[2025-07-31 11:19:14,272: WARNING/ForkPoolWorker-15] 
 66%|######5   | 326/494 [14:41<07:32,  2.69s/it]
[2025-07-31 11:19:14,377: WARNING/ForkPoolWorker-1] 
 72%|#######2  | 357/494 [13:46<04:17,  1.88s/it]
[2025-07-31 11:19:14,556: WARNING/ForkPoolWorker-2] 
 67%|######6   | 329/494 [13:40<06:34,  2.39s/it]
[2025-07-31 11:19:14,854: WARNING/ForkPoolWorker-16] 
 76%|#######5  | 373/494 [14:33<02:35,  1.29s/it]
[2025-07-31 11:19:16,117: WARNING/ForkPoolWorker-1] 
 72%|#######2  | 358/494 [13:47<04:10,  1.84s/it]
[2025-07-31 11:19:16,576: WARNING/ForkPoolWorker-16] 
 76%|#######5  | 374/494 [14:34<02:50,  1.42s/it]
[2025-07-31 11:19:16,748: WARNING/ForkPoolWorker-2] 
 67%|######6   | 330/494 [13:42<06:21,  2.33s/it]
[2025-07-31 11:19:16,835: WARNING/ForkPoolWorker-15] 
 66%|######6   | 327/494 [14:43<07:23,  2.65s/it]
[2025-07-31 11:19:17,886: WARNING/ForkPoolWorker-16] 
 76%|#######5  | 375/494 [14:36<02:44,  1.39s/it]
[2025-07-31 11:19:18,036: WARNING/ForkPoolWorker-1] 
 73%|#######2  | 359/494 [13:49<04:11,  1.86s/it]
[2025-07-31 11:19:19,177: WARNING/ForkPoolWorker-2] 
 67%|######7   | 331/494 [13:45<06:24,  2.36s/it]
[2025-07-31 11:19:19,442: WARNING/ForkPoolWorker-15] 
 66%|######6   | 328/494 [14:46<07:18,  2.64s/it]
[2025-07-31 11:19:19,541: WARNING/ForkPoolWorker-16] 
 76%|#######6  | 376/494 [14:37<02:53,  1.47s/it]
[2025-07-31 11:19:19,692: WARNING/ForkPoolWorker-1] 
 73%|#######2  | 360/494 [13:51<04:01,  1.80s/it]
[2025-07-31 11:19:21,316: WARNING/ForkPoolWorker-16] 
 76%|#######6  | 377/494 [14:39<03:01,  1.55s/it]
[2025-07-31 11:19:21,325: WARNING/ForkPoolWorker-2] 
 67%|######7   | 332/494 [13:47<06:11,  2.30s/it]
[2025-07-31 11:19:21,486: WARNING/ForkPoolWorker-1] 
 73%|#######3  | 361/494 [13:53<03:59,  1.80s/it]
[2025-07-31 11:19:21,563: WARNING/ForkPoolWorker-15] 
 67%|######6   | 329/494 [14:48<06:49,  2.48s/it]
[2025-07-31 11:19:23,727: WARNING/ForkPoolWorker-15] 
 67%|######6   | 330/494 [14:50<06:31,  2.39s/it]
[2025-07-31 11:19:23,938: WARNING/ForkPoolWorker-2] 
 67%|######7   | 333/494 [13:49<06:24,  2.39s/it]
[2025-07-31 11:19:23,944: WARNING/ForkPoolWorker-16] 
 77%|#######6  | 378/494 [14:42<03:38,  1.88s/it]
[2025-07-31 11:19:24,026: WARNING/ForkPoolWorker-1] 
 73%|#######3  | 362/494 [13:55<04:26,  2.02s/it]
[2025-07-31 11:19:25,702: WARNING/ForkPoolWorker-15] 
 67%|######7   | 331/494 [14:52<06:09,  2.26s/it]
[2025-07-31 11:19:26,411: WARNING/ForkPoolWorker-2] 
 68%|######7   | 334/494 [13:52<06:26,  2.42s/it]
[2025-07-31 11:19:26,452: WARNING/ForkPoolWorker-16] 
 77%|#######6  | 379/494 [14:44<03:57,  2.07s/it]
[2025-07-31 11:19:26,555: WARNING/ForkPoolWorker-1] 
 73%|#######3  | 363/494 [13:58<04:44,  2.17s/it]
[2025-07-31 11:19:27,838: WARNING/ForkPoolWorker-15] 
 67%|######7   | 332/494 [14:54<06:00,  2.23s/it]
[2025-07-31 11:19:28,429: WARNING/ForkPoolWorker-2] 
 68%|######7   | 335/494 [13:54<06:05,  2.30s/it]
[2025-07-31 11:19:28,594: WARNING/ForkPoolWorker-1] 
 74%|#######3  | 364/494 [14:00<04:37,  2.13s/it]
[2025-07-31 11:19:28,715: WARNING/ForkPoolWorker-16] 
 77%|#######6  | 380/494 [14:47<04:02,  2.13s/it]
[2025-07-31 11:19:30,156: WARNING/ForkPoolWorker-1] 
 74%|#######3  | 365/494 [14:01<04:13,  1.96s/it]
[2025-07-31 11:19:30,216: WARNING/ForkPoolWorker-15] 
 67%|######7   | 333/494 [14:57<06:05,  2.27s/it]
[2025-07-31 11:19:30,554: WARNING/ForkPoolWorker-2] 
 68%|######8   | 336/494 [13:56<05:54,  2.24s/it]
[2025-07-31 11:19:30,650: WARNING/ForkPoolWorker-16] 
 77%|#######7  | 381/494 [14:49<03:53,  2.07s/it]
[2025-07-31 11:19:31,698: WARNING/ForkPoolWorker-1] 
 74%|#######4  | 366/494 [14:03<03:54,  1.84s/it]
[2025-07-31 11:19:32,018: WARNING/ForkPoolWorker-16] 
 77%|#######7  | 382/494 [14:50<03:28,  1.86s/it]
[2025-07-31 11:19:32,758: WARNING/ForkPoolWorker-15] 
 68%|######7   | 334/494 [14:59<06:16,  2.35s/it]
[2025-07-31 11:19:32,838: WARNING/ForkPoolWorker-2] 
 68%|######8   | 337/494 [13:58<05:54,  2.26s/it]
[2025-07-31 11:19:33,035: WARNING/ForkPoolWorker-1] 
 74%|#######4  | 367/494 [14:04<03:34,  1.69s/it]
[2025-07-31 11:19:33,664: WARNING/ForkPoolWorker-16] 
 78%|#######7  | 383/494 [14:52<03:19,  1.80s/it]
[2025-07-31 11:19:34,842: WARNING/ForkPoolWorker-15] 
 68%|######7   | 335/494 [15:02<06:01,  2.27s/it]
[2025-07-31 11:19:34,881: WARNING/ForkPoolWorker-1] 
 74%|#######4  | 368/494 [14:06<03:38,  1.73s/it]
[2025-07-31 11:19:34,884: WARNING/ForkPoolWorker-2] 
 68%|######8   | 338/494 [14:00<05:42,  2.19s/it]
[2025-07-31 11:19:35,521: WARNING/ForkPoolWorker-16] 
 78%|#######7  | 384/494 [14:53<03:19,  1.81s/it]
[2025-07-31 11:19:36,709: WARNING/ForkPoolWorker-1] 
 75%|#######4  | 369/494 [14:08<03:40,  1.76s/it]
[2025-07-31 11:19:36,886: WARNING/ForkPoolWorker-2] 
 69%|######8   | 339/494 [14:02<05:31,  2.14s/it]
[2025-07-31 11:19:36,892: WARNING/ForkPoolWorker-15] 
 68%|######8   | 336/494 [15:04<05:48,  2.21s/it]
[2025-07-31 11:19:37,417: WARNING/ForkPoolWorker-16] 
 78%|#######7  | 385/494 [14:55<03:20,  1.84s/it]
[2025-07-31 11:19:38,596: WARNING/ForkPoolWorker-1] 
 75%|#######4  | 370/494 [14:10<03:43,  1.80s/it]
[2025-07-31 11:19:38,860: WARNING/ForkPoolWorker-2] 
 69%|######8   | 340/494 [14:04<05:21,  2.09s/it]
[2025-07-31 11:19:39,133: WARNING/ForkPoolWorker-15] 
 68%|######8   | 337/494 [15:06<05:47,  2.22s/it]
[2025-07-31 11:19:39,195: WARNING/ForkPoolWorker-16] 
 78%|#######8  | 386/494 [14:57<03:16,  1.82s/it]
[2025-07-31 11:19:40,476: WARNING/ForkPoolWorker-1] 
 75%|#######5  | 371/494 [14:12<03:44,  1.82s/it]
[2025-07-31 11:19:40,891: WARNING/ForkPoolWorker-2] 
 69%|######9   | 341/494 [14:06<05:16,  2.07s/it]
[2025-07-31 11:19:40,930: WARNING/ForkPoolWorker-16] 
 78%|#######8  | 387/494 [14:59<03:12,  1.79s/it]
[2025-07-31 11:19:41,281: WARNING/ForkPoolWorker-15] 
 68%|######8   | 338/494 [15:08<05:42,  2.20s/it]
[2025-07-31 11:19:42,328: WARNING/ForkPoolWorker-1] 
 75%|#######5  | 372/494 [14:14<03:43,  1.83s/it]
[2025-07-31 11:19:42,747: WARNING/ForkPoolWorker-2] 
 69%|######9   | 342/494 [14:08<05:04,  2.01s/it]
[2025-07-31 11:19:42,990: WARNING/ForkPoolWorker-16] 
 79%|#######8  | 388/494 [15:01<03:18,  1.87s/it]
[2025-07-31 11:19:43,442: WARNING/ForkPoolWorker-15] 
 69%|######8   | 339/494 [15:10<05:38,  2.19s/it]
[2025-07-31 11:19:44,141: WARNING/ForkPoolWorker-16] 
 79%|#######8  | 389/494 [15:02<02:54,  1.66s/it]
[2025-07-31 11:19:44,298: WARNING/ForkPoolWorker-1] 
 76%|#######5  | 373/494 [14:15<03:46,  1.87s/it]
[2025-07-31 11:19:44,756: WARNING/ForkPoolWorker-2] 
 69%|######9   | 343/494 [14:10<05:03,  2.01s/it]
[2025-07-31 11:19:45,410: WARNING/ForkPoolWorker-15] 
 69%|######8   | 340/494 [15:12<05:25,  2.12s/it]
[2025-07-31 11:19:46,229: WARNING/ForkPoolWorker-16] 
 79%|#######8  | 390/494 [15:04<03:05,  1.79s/it]
[2025-07-31 11:19:46,541: WARNING/ForkPoolWorker-1] 
 76%|#######5  | 374/494 [14:18<03:58,  1.98s/it]
[2025-07-31 11:19:47,105: WARNING/ForkPoolWorker-2] 
 70%|######9   | 344/494 [14:13<05:16,  2.11s/it]
[2025-07-31 11:19:47,149: WARNING/ForkPoolWorker-15] 
 69%|######9   | 341/494 [15:14<05:07,  2.01s/it]
[2025-07-31 11:19:48,142: WARNING/ForkPoolWorker-16] 
 79%|#######9  | 391/494 [15:06<03:07,  1.82s/it]
[2025-07-31 11:19:48,444: WARNING/ForkPoolWorker-1] 
 76%|#######5  | 375/494 [14:20<03:53,  1.96s/it]
[2025-07-31 11:19:48,647: WARNING/ForkPoolWorker-2] 
 70%|######9   | 345/494 [14:14<04:48,  1.94s/it]
[2025-07-31 11:19:49,136: WARNING/ForkPoolWorker-15] 
 69%|######9   | 342/494 [15:16<05:04,  2.00s/it]
[2025-07-31 11:19:50,145: WARNING/ForkPoolWorker-16] 
 79%|#######9  | 392/494 [15:08<03:11,  1.88s/it]
[2025-07-31 11:19:50,446: WARNING/ForkPoolWorker-1] 
 76%|#######6  | 376/494 [14:22<03:52,  1.97s/it]
[2025-07-31 11:19:50,622: WARNING/ForkPoolWorker-2] 
 70%|#######   | 346/494 [14:16<04:48,  1.95s/it]
[2025-07-31 11:19:51,335: WARNING/ForkPoolWorker-15] 
 69%|######9   | 343/494 [15:18<05:11,  2.06s/it]
[2025-07-31 11:19:52,030: WARNING/ForkPoolWorker-16] 
 80%|#######9  | 393/494 [15:10<03:09,  1.88s/it]
[2025-07-31 11:19:52,269: WARNING/ForkPoolWorker-1] 
 76%|#######6  | 377/494 [14:23<03:45,  1.93s/it]
[2025-07-31 11:19:52,441: WARNING/ForkPoolWorker-2] 
 70%|#######   | 347/494 [14:18<04:40,  1.91s/it]
[2025-07-31 11:19:53,391: WARNING/ForkPoolWorker-15] 
 70%|######9   | 344/494 [15:20<05:08,  2.06s/it]
[2025-07-31 11:19:53,996: WARNING/ForkPoolWorker-1] 
 77%|#######6  | 378/494 [14:25<03:36,  1.87s/it]
[2025-07-31 11:19:54,005: WARNING/ForkPoolWorker-16] 
 80%|#######9  | 394/494 [15:12<03:10,  1.91s/it]
[2025-07-31 11:19:54,226: WARNING/ForkPoolWorker-2] 
 70%|#######   | 348/494 [14:20<04:33,  1.87s/it]
[2025-07-31 11:19:55,529: WARNING/ForkPoolWorker-15] 
 70%|######9   | 345/494 [15:22<05:10,  2.08s/it]
[2025-07-31 11:19:56,012: WARNING/ForkPoolWorker-16] 
 80%|#######9  | 395/494 [15:14<03:11,  1.94s/it]
[2025-07-31 11:19:56,175: WARNING/ForkPoolWorker-1] 
 77%|#######6  | 379/494 [14:27<03:45,  1.96s/it]
[2025-07-31 11:19:56,309: WARNING/ForkPoolWorker-2] 
 71%|#######   | 349/494 [14:22<04:40,  1.94s/it]
[2025-07-31 11:19:57,828: WARNING/ForkPoolWorker-15] 
 70%|#######   | 346/494 [15:24<05:17,  2.15s/it]
[2025-07-31 11:19:57,981: WARNING/ForkPoolWorker-16] 
 80%|########  | 396/494 [15:16<03:10,  1.95s/it]
[2025-07-31 11:19:58,096: WARNING/ForkPoolWorker-1] 
 77%|#######6  | 380/494 [14:29<03:42,  1.95s/it]
[2025-07-31 11:19:58,239: WARNING/ForkPoolWorker-2] 
 71%|#######   | 350/494 [14:24<04:38,  1.93s/it]
[2025-07-31 11:19:59,741: WARNING/ForkPoolWorker-16] 
 80%|########  | 397/494 [15:18<03:03,  1.89s/it]
[2025-07-31 11:19:59,978: WARNING/ForkPoolWorker-1] 
 77%|#######7  | 381/494 [14:31<03:37,  1.93s/it]
[2025-07-31 11:20:00,056: WARNING/ForkPoolWorker-15] 
 70%|#######   | 347/494 [15:27<05:19,  2.17s/it]
[2025-07-31 11:20:00,239: WARNING/ForkPoolWorker-2] 
 71%|#######1  | 351/494 [14:26<04:39,  1.95s/it]
[2025-07-31 11:20:01,739: WARNING/ForkPoolWorker-16] 
 81%|########  | 398/494 [15:20<03:04,  1.92s/it]
[2025-07-31 11:20:01,768: WARNING/ForkPoolWorker-2] 
 71%|#######1  | 352/494 [14:27<04:19,  1.83s/it]
[2025-07-31 11:20:02,100: WARNING/ForkPoolWorker-1] 
 77%|#######7  | 382/494 [14:33<03:42,  1.99s/it]
[2025-07-31 11:20:02,286: WARNING/ForkPoolWorker-15] 
 70%|#######   | 348/494 [15:29<05:19,  2.19s/it]
[2025-07-31 11:20:03,810: WARNING/ForkPoolWorker-2] 
 71%|#######1  | 353/494 [14:29<04:26,  1.89s/it]
[2025-07-31 11:20:03,930: WARNING/ForkPoolWorker-16] 
 81%|########  | 399/494 [15:22<03:10,  2.00s/it]
[2025-07-31 11:20:04,035: WARNING/ForkPoolWorker-1] 
 78%|#######7  | 383/494 [14:35<03:38,  1.97s/it]
[2025-07-31 11:20:04,362: WARNING/ForkPoolWorker-15] 
 71%|#######   | 349/494 [15:31<05:12,  2.16s/it]
[2025-07-31 11:20:05,807: WARNING/ForkPoolWorker-2] 
 72%|#######1  | 354/494 [14:31<04:29,  1.92s/it]
[2025-07-31 11:20:05,910: WARNING/ForkPoolWorker-16] 
 81%|########  | 400/494 [15:24<03:07,  2.00s/it]
[2025-07-31 11:20:05,913: WARNING/ForkPoolWorker-1] 
 78%|#######7  | 384/494 [14:37<03:33,  1.94s/it]
[2025-07-31 11:20:06,567: WARNING/ForkPoolWorker-15] 
 71%|#######   | 350/494 [15:33<05:12,  2.17s/it]
[2025-07-31 11:20:07,726: WARNING/ForkPoolWorker-2] 
 72%|#######1  | 355/494 [14:33<04:27,  1.92s/it]
[2025-07-31 11:20:07,961: WARNING/ForkPoolWorker-16] 
 81%|########1 | 401/494 [15:26<03:07,  2.01s/it]
[2025-07-31 11:20:07,988: WARNING/ForkPoolWorker-1] 
 78%|#######7  | 385/494 [14:39<03:36,  1.98s/it]
[2025-07-31 11:20:08,603: WARNING/ForkPoolWorker-15] 
 71%|#######1  | 351/494 [15:35<05:04,  2.13s/it]
[2025-07-31 11:20:09,591: WARNING/ForkPoolWorker-2] 
 72%|#######2  | 356/494 [14:35<04:22,  1.90s/it]
[2025-07-31 11:20:09,826: WARNING/ForkPoolWorker-16] 
 81%|########1 | 402/494 [15:28<03:01,  1.97s/it]
[2025-07-31 11:20:10,041: WARNING/ForkPoolWorker-1] 
 78%|#######8  | 386/494 [14:41<03:36,  2.00s/it]
[2025-07-31 11:20:10,620: WARNING/ForkPoolWorker-15] 
 71%|#######1  | 352/494 [15:37<04:57,  2.10s/it]
[2025-07-31 11:20:11,419: WARNING/ForkPoolWorker-2] 
 72%|#######2  | 357/494 [14:37<04:17,  1.88s/it]
[2025-07-31 11:20:11,770: WARNING/ForkPoolWorker-16] 
 82%|########1 | 403/494 [15:30<02:58,  1.96s/it]
[2025-07-31 11:20:12,011: WARNING/ForkPoolWorker-1] 
 78%|#######8  | 387/494 [14:43<03:33,  1.99s/it]
[2025-07-31 11:20:12,837: WARNING/ForkPoolWorker-15] 
 71%|#######1  | 353/494 [15:39<05:00,  2.13s/it]
[2025-07-31 11:20:13,319: WARNING/ForkPoolWorker-2] 
 72%|#######2  | 358/494 [14:39<04:16,  1.89s/it]
[2025-07-31 11:20:13,609: WARNING/ForkPoolWorker-16] 
 82%|########1 | 404/494 [15:32<02:53,  1.93s/it]
[2025-07-31 11:20:13,876: WARNING/ForkPoolWorker-1] 
 79%|#######8  | 388/494 [14:45<03:27,  1.96s/it]
[2025-07-31 11:20:14,964: WARNING/ForkPoolWorker-15] 
 72%|#######1  | 354/494 [15:42<04:58,  2.13s/it]
[2025-07-31 11:20:15,332: WARNING/ForkPoolWorker-2] 
 73%|#######2  | 359/494 [14:41<04:19,  1.92s/it]
[2025-07-31 11:20:15,736: WARNING/ForkPoolWorker-16] 
 82%|########1 | 405/494 [15:34<02:56,  1.99s/it]
[2025-07-31 11:20:16,026: WARNING/ForkPoolWorker-1] 
 79%|#######8  | 389/494 [14:47<03:31,  2.01s/it]
[2025-07-31 11:20:17,116: WARNING/ForkPoolWorker-15] 
 72%|#######1  | 355/494 [15:44<04:57,  2.14s/it]
[2025-07-31 11:20:17,410: WARNING/ForkPoolWorker-2] 
 73%|#######2  | 360/494 [14:43<04:24,  1.97s/it]
[2025-07-31 11:20:17,871: WARNING/ForkPoolWorker-16] 
 82%|########2 | 406/494 [15:36<02:58,  2.03s/it]
[2025-07-31 11:20:18,144: WARNING/ForkPoolWorker-1] 
 79%|#######8  | 390/494 [14:49<03:32,  2.04s/it]
[2025-07-31 11:20:19,334: WARNING/ForkPoolWorker-15] 
 72%|#######2  | 356/494 [15:46<04:58,  2.16s/it]
[2025-07-31 11:20:19,453: WARNING/ForkPoolWorker-2] 
 73%|#######3  | 361/494 [14:45<04:25,  1.99s/it]
[2025-07-31 11:20:19,972: WARNING/ForkPoolWorker-16] 
 82%|########2 | 407/494 [15:38<02:58,  2.05s/it]
[2025-07-31 11:20:20,055: WARNING/ForkPoolWorker-1] 
 79%|#######9  | 391/494 [14:51<03:26,  2.00s/it]
[2025-07-31 11:20:20,965: WARNING/ForkPoolWorker-15] 
 72%|#######2  | 357/494 [15:48<04:34,  2.00s/it]
[2025-07-31 11:20:21,479: WARNING/ForkPoolWorker-2] 
 73%|#######3  | 362/494 [14:47<04:24,  2.00s/it]
[2025-07-31 11:20:21,997: WARNING/ForkPoolWorker-1] 
 79%|#######9  | 392/494 [14:53<03:22,  1.99s/it]
[2025-07-31 11:20:22,050: WARNING/ForkPoolWorker-16] 
 83%|########2 | 408/494 [15:40<02:57,  2.06s/it]
[2025-07-31 11:20:22,989: WARNING/ForkPoolWorker-15] 
 72%|#######2  | 358/494 [15:50<04:33,  2.01s/it]
[2025-07-31 11:20:23,374: WARNING/ForkPoolWorker-2] 
 73%|#######3  | 363/494 [14:49<04:18,  1.97s/it]
[2025-07-31 11:20:23,915: WARNING/ForkPoolWorker-16] 
 83%|########2 | 409/494 [15:42<02:50,  2.00s/it]
[2025-07-31 11:20:24,073: WARNING/ForkPoolWorker-1] 
 80%|#######9  | 393/494 [14:55<03:23,  2.01s/it]
[2025-07-31 11:20:24,980: WARNING/ForkPoolWorker-15] 
 73%|#######2  | 359/494 [15:52<04:30,  2.00s/it]
[2025-07-31 11:20:25,270: WARNING/ForkPoolWorker-2] 
 74%|#######3  | 364/494 [14:51<04:13,  1.95s/it]
[2025-07-31 11:20:25,890: WARNING/ForkPoolWorker-16] 
 83%|########2 | 410/494 [15:44<02:47,  1.99s/it]
[2025-07-31 11:20:26,071: WARNING/ForkPoolWorker-1] 
 80%|#######9  | 394/494 [14:57<03:20,  2.01s/it]
[2025-07-31 11:20:27,009: WARNING/ForkPoolWorker-15] 
 73%|#######2  | 360/494 [15:54<04:29,  2.01s/it]
[2025-07-31 11:20:27,410: WARNING/ForkPoolWorker-2] 
 74%|#######3  | 365/494 [14:53<04:18,  2.01s/it]
[2025-07-31 11:20:27,819: WARNING/ForkPoolWorker-16] 
 83%|########3 | 411/494 [15:46<02:43,  1.97s/it]
[2025-07-31 11:20:28,133: WARNING/ForkPoolWorker-1] 
 80%|#######9  | 395/494 [14:59<03:20,  2.02s/it]
[2025-07-31 11:20:29,051: WARNING/ForkPoolWorker-15] 
 73%|#######3  | 361/494 [15:56<04:28,  2.02s/it]
[2025-07-31 11:20:29,475: WARNING/ForkPoolWorker-2] 
 74%|#######4  | 366/494 [14:55<04:18,  2.02s/it]
[2025-07-31 11:20:29,752: WARNING/ForkPoolWorker-16] 
 83%|########3 | 412/494 [15:48<02:40,  1.96s/it]
[2025-07-31 11:20:30,141: WARNING/ForkPoolWorker-1] 
 80%|########  | 396/494 [15:01<03:17,  2.02s/it]
[2025-07-31 11:20:31,022: WARNING/ForkPoolWorker-15] 
 73%|#######3  | 362/494 [15:58<04:24,  2.01s/it]
[2025-07-31 11:20:31,311: WARNING/ForkPoolWorker-2] 
 74%|#######4  | 367/494 [14:57<04:09,  1.97s/it]
[2025-07-31 11:20:31,783: WARNING/ForkPoolWorker-16] 
 84%|########3 | 413/494 [15:50<02:40,  1.98s/it]
[2025-07-31 11:20:31,928: WARNING/ForkPoolWorker-1] 
 80%|########  | 397/494 [15:03<03:09,  1.95s/it]
[2025-07-31 11:20:33,101: WARNING/ForkPoolWorker-2] 
 74%|#######4  | 368/494 [14:59<04:01,  1.91s/it]
[2025-07-31 11:20:33,365: WARNING/ForkPoolWorker-15] 
 73%|#######3  | 363/494 [16:00<04:36,  2.11s/it]
[2025-07-31 11:20:33,500: WARNING/ForkPoolWorker-16] 
 84%|########3 | 414/494 [15:51<02:32,  1.90s/it]
[2025-07-31 11:20:33,637: WARNING/ForkPoolWorker-1] 
 81%|########  | 398/494 [15:05<03:00,  1.88s/it]
[2025-07-31 11:20:34,675: WARNING/ForkPoolWorker-2] 
 75%|#######4  | 369/494 [15:00<03:46,  1.81s/it]
[2025-07-31 11:20:35,297: WARNING/ForkPoolWorker-16] 
 84%|########4 | 415/494 [15:53<02:27,  1.87s/it]
[2025-07-31 11:20:35,495: WARNING/ForkPoolWorker-1] 
 81%|########  | 399/494 [15:07<02:57,  1.87s/it]
[2025-07-31 11:20:35,686: WARNING/ForkPoolWorker-15] 
 74%|#######3  | 364/494 [16:02<04:42,  2.17s/it]
[2025-07-31 11:20:36,480: WARNING/ForkPoolWorker-2] 
 75%|#######4  | 370/494 [15:02<03:44,  1.81s/it]
[2025-07-31 11:20:37,313: WARNING/ForkPoolWorker-16] 
 84%|########4 | 416/494 [15:55<02:29,  1.91s/it]
[2025-07-31 11:20:37,377: WARNING/ForkPoolWorker-1] 
 81%|########  | 400/494 [15:09<02:56,  1.88s/it]
[2025-07-31 11:20:37,677: WARNING/ForkPoolWorker-15] 
 74%|#######3  | 365/494 [16:04<04:33,  2.12s/it]
[2025-07-31 11:20:38,267: WARNING/ForkPoolWorker-2] 
 75%|#######5  | 371/494 [15:04<03:41,  1.80s/it]
[2025-07-31 11:20:39,255: WARNING/ForkPoolWorker-16] 
 84%|########4 | 417/494 [15:57<02:28,  1.92s/it]
[2025-07-31 11:20:39,295: WARNING/ForkPoolWorker-1] 
 81%|########1 | 401/494 [15:10<02:55,  1.89s/it]
[2025-07-31 11:20:39,793: WARNING/ForkPoolWorker-15] 
 74%|#######4  | 366/494 [16:06<04:30,  2.12s/it]
[2025-07-31 11:20:39,971: WARNING/ForkPoolWorker-2] 
 75%|#######5  | 372/494 [15:05<03:36,  1.77s/it]
[2025-07-31 11:20:40,832: WARNING/ForkPoolWorker-1] 
 81%|########1 | 402/494 [15:12<02:44,  1.78s/it]
[2025-07-31 11:20:41,174: WARNING/ForkPoolWorker-16] 
 85%|########4 | 418/494 [15:59<02:26,  1.92s/it]
[2025-07-31 11:20:41,654: WARNING/ForkPoolWorker-2] 
 76%|#######5  | 373/494 [15:07<03:31,  1.75s/it]
[2025-07-31 11:20:42,021: WARNING/ForkPoolWorker-15] 
 74%|#######4  | 367/494 [16:09<04:33,  2.15s/it]
[2025-07-31 11:20:42,621: WARNING/ForkPoolWorker-1] 
 82%|########1 | 403/494 [15:14<02:42,  1.79s/it]
[2025-07-31 11:20:42,986: WARNING/ForkPoolWorker-16] 
 85%|########4 | 419/494 [16:01<02:21,  1.89s/it]
[2025-07-31 11:20:43,236: WARNING/ForkPoolWorker-2] 
 76%|#######5  | 374/494 [15:09<03:23,  1.70s/it]
[2025-07-31 11:20:44,377: WARNING/ForkPoolWorker-15] 
 74%|#######4  | 368/494 [16:11<04:38,  2.21s/it]
[2025-07-31 11:20:44,549: WARNING/ForkPoolWorker-1] 
 82%|########1 | 404/494 [15:16<02:44,  1.83s/it]
[2025-07-31 11:20:44,842: WARNING/ForkPoolWorker-16] 
 85%|########5 | 420/494 [16:03<02:19,  1.88s/it]
[2025-07-31 11:20:45,248: WARNING/ForkPoolWorker-2] 
 76%|#######5  | 375/494 [15:11<03:33,  1.79s/it]
[2025-07-31 11:20:46,358: WARNING/ForkPoolWorker-1] 
 82%|########1 | 405/494 [15:18<02:42,  1.82s/it]
[2025-07-31 11:20:46,571: WARNING/ForkPoolWorker-16] 
 85%|########5 | 421/494 [16:04<02:13,  1.83s/it]
[2025-07-31 11:20:46,620: WARNING/ForkPoolWorker-15] 
 75%|#######4  | 369/494 [16:13<04:37,  2.22s/it]
[2025-07-31 11:20:46,897: WARNING/ForkPoolWorker-2] 
 76%|#######6  | 376/494 [15:12<03:26,  1.75s/it]
[2025-07-31 11:20:48,315: WARNING/ForkPoolWorker-1] 
 82%|########2 | 406/494 [15:19<02:43,  1.86s/it]
[2025-07-31 11:20:48,399: WARNING/ForkPoolWorker-16] 
 85%|########5 | 422/494 [16:06<02:11,  1.83s/it]
[2025-07-31 11:20:48,634: WARNING/ForkPoolWorker-2] 
 76%|#######6  | 377/494 [15:14<03:24,  1.75s/it]
[2025-07-31 11:20:48,836: WARNING/ForkPoolWorker-15] 
 75%|#######4  | 370/494 [16:15<04:35,  2.22s/it]
[2025-07-31 11:20:49,508: WARNING/ForkPoolWorker-1] 
 82%|########2 | 407/494 [15:21<02:24,  1.66s/it]
[2025-07-31 11:20:50,367: WARNING/ForkPoolWorker-16] 
 86%|########5 | 423/494 [16:08<02:12,  1.87s/it]
[2025-07-31 11:20:50,580: WARNING/ForkPoolWorker-2] 
 77%|#######6  | 378/494 [15:16<03:29,  1.81s/it]
[2025-07-31 11:20:50,781: WARNING/ForkPoolWorker-15] 
 75%|#######5  | 371/494 [16:17<04:22,  2.14s/it]
[2025-07-31 11:20:51,333: WARNING/ForkPoolWorker-1] 
 83%|########2 | 408/494 [15:23<02:27,  1.71s/it]
[2025-07-31 11:20:52,147: WARNING/ForkPoolWorker-16] 
 86%|########5 | 424/494 [16:10<02:09,  1.85s/it]
[2025-07-31 11:20:52,490: WARNING/ForkPoolWorker-2] 
 77%|#######6  | 379/494 [15:18<03:30,  1.83s/it]
[2025-07-31 11:20:53,041: WARNING/ForkPoolWorker-1] 
 83%|########2 | 409/494 [15:24<02:25,  1.71s/it]
[2025-07-31 11:20:53,058: WARNING/ForkPoolWorker-15] 
 75%|#######5  | 372/494 [16:20<04:25,  2.18s/it]
[2025-07-31 11:20:53,753: WARNING/ForkPoolWorker-16] 
 86%|########6 | 425/494 [16:12<02:02,  1.77s/it]
[2025-07-31 11:20:54,301: WARNING/ForkPoolWorker-2] 
 77%|#######6  | 380/494 [15:20<03:28,  1.83s/it]
[2025-07-31 11:20:54,806: WARNING/ForkPoolWorker-1] 
 83%|########2 | 410/494 [15:26<02:25,  1.73s/it]
[2025-07-31 11:20:55,214: WARNING/ForkPoolWorker-15] 
 76%|#######5  | 373/494 [16:22<04:22,  2.17s/it]
[2025-07-31 11:20:55,543: WARNING/ForkPoolWorker-16] 
 86%|########6 | 426/494 [16:13<02:00,  1.78s/it]
[2025-07-31 11:20:56,026: WARNING/ForkPoolWorker-2] 
 77%|#######7  | 381/494 [15:22<03:23,  1.80s/it]
[2025-07-31 11:20:56,728: WARNING/ForkPoolWorker-1] 
 83%|########3 | 411/494 [15:28<02:28,  1.79s/it]
[2025-07-31 11:20:57,431: WARNING/ForkPoolWorker-16] 
 86%|########6 | 427/494 [16:15<02:01,  1.81s/it]
[2025-07-31 11:20:57,447: WARNING/ForkPoolWorker-15] 
 76%|#######5  | 374/494 [16:24<04:22,  2.19s/it]
[2025-07-31 11:20:57,964: WARNING/ForkPoolWorker-2] 
 77%|#######7  | 382/494 [15:23<03:26,  1.84s/it]
[2025-07-31 11:20:58,565: WARNING/ForkPoolWorker-1] 
 83%|########3 | 412/494 [15:30<02:27,  1.80s/it]
[2025-07-31 11:20:59,309: WARNING/ForkPoolWorker-16] 
 87%|########6 | 428/494 [16:17<02:00,  1.83s/it]
[2025-07-31 11:20:59,553: WARNING/ForkPoolWorker-15] 
 76%|#######5  | 375/494 [16:26<04:17,  2.17s/it]
[2025-07-31 11:20:59,689: WARNING/ForkPoolWorker-2] 
 78%|#######7  | 383/494 [15:25<03:20,  1.81s/it]
[2025-07-31 11:21:00,043: WARNING/ForkPoolWorker-1] 
 84%|########3 | 413/494 [15:31<02:18,  1.70s/it]
[2025-07-31 11:21:01,304: WARNING/ForkPoolWorker-16] 
 87%|########6 | 429/494 [16:19<02:02,  1.88s/it]
[2025-07-31 11:21:01,446: WARNING/ForkPoolWorker-2] 
 78%|#######7  | 384/494 [15:27<03:17,  1.79s/it]
[2025-07-31 11:21:01,720: WARNING/ForkPoolWorker-15] 
 76%|#######6  | 376/494 [16:28<04:15,  2.17s/it]
[2025-07-31 11:21:01,837: WARNING/ForkPoolWorker-1] 
 84%|########3 | 414/494 [15:33<02:18,  1.73s/it]
[2025-07-31 11:21:03,132: WARNING/ForkPoolWorker-16] 
 87%|########7 | 430/494 [16:21<01:59,  1.86s/it]
[2025-07-31 11:21:03,404: WARNING/ForkPoolWorker-2] 
 78%|#######7  | 385/494 [15:29<03:20,  1.84s/it]
[2025-07-31 11:21:03,671: WARNING/ForkPoolWorker-1] 
 84%|########4 | 415/494 [15:35<02:19,  1.76s/it]
[2025-07-31 11:21:04,031: WARNING/ForkPoolWorker-15] 
 76%|#######6  | 377/494 [16:31<04:18,  2.21s/it]
[2025-07-31 11:21:04,242: WARNING/ForkPoolWorker-16] 
 87%|########7 | 431/494 [16:22<01:43,  1.64s/it]
[2025-07-31 11:21:05,055: WARNING/ForkPoolWorker-2] 
 78%|#######8  | 386/494 [15:31<03:12,  1.78s/it]
[2025-07-31 11:21:05,664: WARNING/ForkPoolWorker-1] 
 84%|########4 | 416/494 [15:37<02:22,  1.83s/it]
[2025-07-31 11:21:06,109: WARNING/ForkPoolWorker-16] 
 87%|########7 | 432/494 [16:24<01:45,  1.71s/it]
[2025-07-31 11:21:06,218: WARNING/ForkPoolWorker-15] 
 77%|#######6  | 378/494 [16:33<04:15,  2.20s/it]
[2025-07-31 11:21:06,829: WARNING/ForkPoolWorker-2] 
 78%|#######8  | 387/494 [15:32<03:10,  1.78s/it]
[2025-07-31 11:21:07,533: WARNING/ForkPoolWorker-1] 
 84%|########4 | 417/494 [15:39<02:21,  1.84s/it]
[2025-07-31 11:21:07,992: WARNING/ForkPoolWorker-16] 
 88%|########7 | 433/494 [16:26<01:47,  1.76s/it]
[2025-07-31 11:21:08,290: WARNING/ForkPoolWorker-15] 
 77%|#######6  | 379/494 [16:35<04:08,  2.16s/it]
[2025-07-31 11:21:08,596: WARNING/ForkPoolWorker-2] 
 79%|#######8  | 388/494 [15:34<03:08,  1.78s/it]
[2025-07-31 11:21:09,257: WARNING/ForkPoolWorker-1] 
 85%|########4 | 418/494 [15:40<02:17,  1.81s/it]
[2025-07-31 11:21:09,777: WARNING/ForkPoolWorker-16] 
 88%|########7 | 434/494 [16:28<01:46,  1.77s/it]
[2025-07-31 11:21:10,425: WARNING/ForkPoolWorker-2] 
 79%|#######8  | 389/494 [15:36<03:08,  1.79s/it]
[2025-07-31 11:21:10,623: WARNING/ForkPoolWorker-15] 
 77%|#######6  | 380/494 [16:37<04:12,  2.21s/it]
[2025-07-31 11:21:11,138: WARNING/ForkPoolWorker-1] 
 85%|########4 | 419/494 [15:42<02:17,  1.83s/it]
[2025-07-31 11:21:11,392: WARNING/ForkPoolWorker-16] 
 88%|########8 | 435/494 [16:29<01:41,  1.72s/it]
[2025-07-31 11:21:12,368: WARNING/ForkPoolWorker-2] 
 79%|#######8  | 390/494 [15:38<03:11,  1.84s/it]
[2025-07-31 11:21:12,652: WARNING/ForkPoolWorker-15] 
 77%|#######7  | 381/494 [16:39<04:03,  2.16s/it]
[2025-07-31 11:21:13,107: WARNING/ForkPoolWorker-1] 
 85%|########5 | 420/494 [15:44<02:18,  1.87s/it]
[2025-07-31 11:21:13,258: WARNING/ForkPoolWorker-16] 
 88%|########8 | 436/494 [16:31<01:42,  1.77s/it]
[2025-07-31 11:21:14,234: WARNING/ForkPoolWorker-2] 
 79%|#######9  | 391/494 [15:40<03:10,  1.85s/it]
[2025-07-31 11:21:14,760: WARNING/ForkPoolWorker-1] 
 85%|########5 | 421/494 [15:46<02:11,  1.81s/it]
[2025-07-31 11:21:14,803: WARNING/ForkPoolWorker-15] 
 77%|#######7  | 382/494 [16:41<04:01,  2.16s/it]
[2025-07-31 11:21:15,017: WARNING/ForkPoolWorker-16] 
 88%|########8 | 437/494 [16:33<01:40,  1.76s/it]
[2025-07-31 11:21:15,924: WARNING/ForkPoolWorker-2] 
 79%|#######9  | 392/494 [15:41<03:03,  1.80s/it]
[2025-07-31 11:21:16,468: WARNING/ForkPoolWorker-1] 
 85%|########5 | 422/494 [15:48<02:07,  1.78s/it]
[2025-07-31 11:21:16,882: WARNING/ForkPoolWorker-16] 
 89%|########8 | 438/494 [16:35<01:40,  1.79s/it]
[2025-07-31 11:21:16,980: WARNING/ForkPoolWorker-15] 
 78%|#######7  | 383/494 [16:44<04:00,  2.16s/it]
[2025-07-31 11:21:17,760: WARNING/ForkPoolWorker-2] 
 80%|#######9  | 393/494 [15:43<03:02,  1.81s/it]
[2025-07-31 11:21:18,333: WARNING/ForkPoolWorker-1] 
 86%|########5 | 423/494 [15:50<02:07,  1.80s/it]
[2025-07-31 11:21:18,760: WARNING/ForkPoolWorker-16] 
 89%|########8 | 439/494 [16:37<01:40,  1.82s/it]
[2025-07-31 11:21:18,994: WARNING/ForkPoolWorker-15] 
 78%|#######7  | 384/494 [16:46<03:52,  2.12s/it]
[2025-07-31 11:21:19,858: WARNING/ForkPoolWorker-2] 
 80%|#######9  | 394/494 [15:45<03:09,  1.90s/it]
[2025-07-31 11:21:20,396: WARNING/ForkPoolWorker-1] 
 86%|########5 | 424/494 [15:52<02:11,  1.88s/it]
[2025-07-31 11:21:20,602: WARNING/ForkPoolWorker-16] 
 89%|########9 | 440/494 [16:39<01:38,  1.83s/it]
[2025-07-31 11:21:21,010: WARNING/ForkPoolWorker-15] 
 78%|#######7  | 385/494 [16:48<03:47,  2.09s/it]
[2025-07-31 11:21:21,749: WARNING/ForkPoolWorker-2] 
 80%|#######9  | 395/494 [15:47<03:07,  1.89s/it]
[2025-07-31 11:21:22,221: WARNING/ForkPoolWorker-1] 
 86%|########6 | 425/494 [15:53<02:08,  1.86s/it]
[2025-07-31 11:21:22,613: WARNING/ForkPoolWorker-16] 
 89%|########9 | 441/494 [16:41<01:39,  1.88s/it]
[2025-07-31 11:21:23,164: WARNING/ForkPoolWorker-15] 
 78%|#######8  | 386/494 [16:50<03:47,  2.11s/it]
[2025-07-31 11:21:23,648: WARNING/ForkPoolWorker-2] 
 80%|########  | 396/494 [15:49<03:05,  1.90s/it]
[2025-07-31 11:21:24,332: WARNING/ForkPoolWorker-1] 
 86%|########6 | 426/494 [15:56<02:11,  1.94s/it]
[2025-07-31 11:21:24,697: WARNING/ForkPoolWorker-16] 
 89%|########9 | 442/494 [16:43<01:40,  1.94s/it]
[2025-07-31 11:21:25,232: WARNING/ForkPoolWorker-15] 
 78%|#######8  | 387/494 [16:52<03:44,  2.10s/it]
[2025-07-31 11:21:25,477: WARNING/ForkPoolWorker-2] 
 80%|########  | 397/494 [15:51<03:01,  1.88s/it]
[2025-07-31 11:21:26,233: WARNING/ForkPoolWorker-1] 
 86%|########6 | 427/494 [15:57<02:09,  1.93s/it]
[2025-07-31 11:21:26,784: WARNING/ForkPoolWorker-16] 
 90%|########9 | 443/494 [16:45<01:41,  1.99s/it]
[2025-07-31 11:21:27,347: WARNING/ForkPoolWorker-2] 
 81%|########  | 398/494 [15:53<02:59,  1.87s/it]
[2025-07-31 11:21:27,350: WARNING/ForkPoolWorker-15] 
 79%|#######8  | 388/494 [16:54<03:42,  2.10s/it]
[2025-07-31 11:21:28,240: WARNING/ForkPoolWorker-1] 
 87%|########6 | 428/494 [15:59<02:08,  1.95s/it]
[2025-07-31 11:21:28,849: WARNING/ForkPoolWorker-16] 
 90%|########9 | 444/494 [16:47<01:40,  2.01s/it]
[2025-07-31 11:21:29,292: WARNING/ForkPoolWorker-2] 
 81%|########  | 399/494 [15:55<03:00,  1.90s/it]
[2025-07-31 11:21:29,530: WARNING/ForkPoolWorker-15] 
 79%|#######8  | 389/494 [16:56<03:43,  2.13s/it]
[2025-07-31 11:21:30,050: WARNING/ForkPoolWorker-1] 
 87%|########6 | 429/494 [16:01<02:04,  1.91s/it]
[2025-07-31 11:21:30,690: WARNING/ForkPoolWorker-16] 
 90%|######### | 445/494 [16:49<01:35,  1.96s/it]
[2025-07-31 11:21:31,182: WARNING/ForkPoolWorker-2] 
 81%|########  | 400/494 [15:57<02:58,  1.89s/it]
[2025-07-31 11:21:31,570: WARNING/ForkPoolWorker-15] 
 79%|#######8  | 390/494 [16:58<03:38,  2.10s/it]
[2025-07-31 11:21:31,933: WARNING/ForkPoolWorker-1] 
 87%|########7 | 430/494 [16:03<02:01,  1.90s/it]
[2025-07-31 11:21:32,420: WARNING/ForkPoolWorker-16] 
 90%|######### | 446/494 [16:50<01:30,  1.89s/it]
[2025-07-31 11:21:33,261: WARNING/ForkPoolWorker-2] 
 81%|########1 | 401/494 [15:59<03:01,  1.95s/it]
[2025-07-31 11:21:33,732: WARNING/ForkPoolWorker-1] 
 87%|########7 | 431/494 [16:05<01:57,  1.87s/it]
[2025-07-31 11:21:33,851: WARNING/ForkPoolWorker-15] 
 79%|#######9  | 391/494 [17:01<03:41,  2.15s/it]
[2025-07-31 11:21:34,075: WARNING/ForkPoolWorker-16] 
 90%|######### | 447/494 [16:52<01:25,  1.82s/it]
[2025-07-31 11:21:35,177: WARNING/ForkPoolWorker-2] 
 81%|########1 | 402/494 [16:01<02:58,  1.94s/it]
[2025-07-31 11:21:35,543: WARNING/ForkPoolWorker-1] 
 87%|########7 | 432/494 [16:07<01:54,  1.85s/it]
[2025-07-31 11:21:35,872: WARNING/ForkPoolWorker-16] 
 91%|######### | 448/494 [16:54<01:23,  1.81s/it]
[2025-07-31 11:21:36,039: WARNING/ForkPoolWorker-15] 
 79%|#######9  | 392/494 [17:03<03:40,  2.16s/it]
[2025-07-31 11:21:36,928: WARNING/ForkPoolWorker-2] 
 82%|########1 | 403/494 [16:02<02:51,  1.88s/it]
[2025-07-31 11:21:37,235: WARNING/ForkPoolWorker-1] 
 88%|########7 | 433/494 [16:08<01:50,  1.80s/it]
[2025-07-31 11:21:37,688: WARNING/ForkPoolWorker-16] 
 91%|######### | 449/494 [16:56<01:21,  1.81s/it]
[2025-07-31 11:21:38,159: WARNING/ForkPoolWorker-15] 
 80%|#######9  | 393/494 [17:05<03:37,  2.15s/it]
[2025-07-31 11:21:38,992: WARNING/ForkPoolWorker-2] 
 82%|########1 | 404/494 [16:05<02:54,  1.94s/it]
[2025-07-31 11:21:39,393: WARNING/ForkPoolWorker-1] 
 88%|########7 | 434/494 [16:11<01:54,  1.91s/it]
[2025-07-31 11:21:39,523: WARNING/ForkPoolWorker-16] 
 91%|#########1| 450/494 [16:57<01:20,  1.82s/it]
[2025-07-31 11:21:40,173: WARNING/ForkPoolWorker-15] 
 80%|#######9  | 394/494 [17:07<03:30,  2.11s/it]
[2025-07-31 11:21:40,879: WARNING/ForkPoolWorker-2] 
 82%|########1 | 405/494 [16:06<02:51,  1.92s/it]
[2025-07-31 11:21:41,230: WARNING/ForkPoolWorker-1] 
 88%|########8 | 435/494 [16:12<01:51,  1.89s/it]
[2025-07-31 11:21:41,335: WARNING/ForkPoolWorker-16] 
 91%|#########1| 451/494 [16:59<01:18,  1.82s/it]
[2025-07-31 11:21:42,239: WARNING/ForkPoolWorker-15] 
 80%|#######9  | 395/494 [17:09<03:27,  2.10s/it]
[2025-07-31 11:21:43,026: WARNING/ForkPoolWorker-2] 
 82%|########2 | 406/494 [16:09<02:55,  1.99s/it]
[2025-07-31 11:21:43,285: WARNING/ForkPoolWorker-1] 
 88%|########8 | 436/494 [16:14<01:52,  1.94s/it]
[2025-07-31 11:21:43,437: WARNING/ForkPoolWorker-16] 
 91%|#########1| 452/494 [17:01<01:19,  1.90s/it]
[2025-07-31 11:21:44,382: WARNING/ForkPoolWorker-15] 
 80%|########  | 396/494 [17:11<03:26,  2.11s/it]
[2025-07-31 11:21:45,042: WARNING/ForkPoolWorker-2] 
 82%|########2 | 407/494 [16:11<02:53,  2.00s/it]
[2025-07-31 11:21:45,348: WARNING/ForkPoolWorker-16] 
 92%|#########1| 453/494 [17:03<01:18,  1.91s/it]
[2025-07-31 11:21:45,388: WARNING/ForkPoolWorker-1] 
 88%|########8 | 437/494 [16:17<01:53,  1.99s/it]
[2025-07-31 11:21:46,498: WARNING/ForkPoolWorker-15] 
 80%|########  | 397/494 [17:13<03:24,  2.11s/it]
[2025-07-31 11:21:46,959: WARNING/ForkPoolWorker-2] 
 83%|########2 | 408/494 [16:12<02:49,  1.97s/it]
[2025-07-31 11:21:47,158: WARNING/ForkPoolWorker-1] 
 89%|########8 | 438/494 [16:18<01:47,  1.92s/it]
[2025-07-31 11:21:47,161: WARNING/ForkPoolWorker-16] 
 92%|#########1| 454/494 [17:05<01:14,  1.87s/it]
[2025-07-31 11:21:48,616: WARNING/ForkPoolWorker-15] 
 81%|########  | 398/494 [17:15<03:22,  2.11s/it]
[2025-07-31 11:21:48,940: WARNING/ForkPoolWorker-16] 
 92%|#########2| 455/494 [17:07<01:11,  1.85s/it]
[2025-07-31 11:21:48,981: WARNING/ForkPoolWorker-1] 
 89%|########8 | 439/494 [16:20<01:44,  1.89s/it]
[2025-07-31 11:21:49,023: WARNING/ForkPoolWorker-2] 
 83%|########2 | 409/494 [16:15<02:50,  2.00s/it]
[2025-07-31 11:21:50,721: WARNING/ForkPoolWorker-15] 
 81%|########  | 399/494 [17:17<03:20,  2.11s/it]
[2025-07-31 11:21:51,029: WARNING/ForkPoolWorker-2] 
 83%|########2 | 410/494 [16:17<02:48,  2.00s/it]
[2025-07-31 11:21:51,162: WARNING/ForkPoolWorker-16] 
 92%|#########2| 456/494 [17:09<01:14,  1.96s/it]
[2025-07-31 11:21:51,263: WARNING/ForkPoolWorker-1] 
 89%|########9 | 440/494 [16:22<01:48,  2.01s/it]
[2025-07-31 11:21:52,745: WARNING/ForkPoolWorker-15] 
 81%|########  | 400/494 [17:19<03:16,  2.09s/it]
[2025-07-31 11:21:52,977: WARNING/ForkPoolWorker-2] 
 83%|########3 | 411/494 [16:18<02:44,  1.99s/it]
[2025-07-31 11:21:53,050: WARNING/ForkPoolWorker-1] 
 89%|########9 | 441/494 [16:24<01:43,  1.94s/it]
[2025-07-31 11:21:53,123: WARNING/ForkPoolWorker-16] 
 93%|#########2| 457/494 [17:11<01:12,  1.96s/it]
[2025-07-31 11:21:54,711: WARNING/ForkPoolWorker-15] 
 81%|########1 | 401/494 [17:21<03:10,  2.05s/it]
[2025-07-31 11:21:55,070: WARNING/ForkPoolWorker-2] 
 83%|########3 | 412/494 [16:21<02:45,  2.02s/it]
[2025-07-31 11:21:55,093: WARNING/ForkPoolWorker-1] 
 89%|########9 | 442/494 [16:26<01:42,  1.97s/it]
[2025-07-31 11:21:55,345: WARNING/ForkPoolWorker-16] 
 93%|#########2| 458/494 [17:13<01:13,  2.04s/it]
[2025-07-31 11:21:56,730: WARNING/ForkPoolWorker-15] 
 81%|########1 | 402/494 [17:23<03:07,  2.04s/it]
[2025-07-31 11:21:56,957: WARNING/ForkPoolWorker-1] 
 90%|########9 | 443/494 [16:28<01:38,  1.94s/it]
[2025-07-31 11:21:57,080: WARNING/ForkPoolWorker-2] 
 84%|########3 | 413/494 [16:23<02:43,  2.02s/it]
[2025-07-31 11:21:57,305: WARNING/ForkPoolWorker-16] 
 93%|#########2| 459/494 [17:15<01:10,  2.01s/it]
[2025-07-31 11:21:58,651: WARNING/ForkPoolWorker-15] 
 82%|########1 | 403/494 [17:25<03:02,  2.00s/it]
[2025-07-31 11:21:58,893: WARNING/ForkPoolWorker-1] 
 90%|########9 | 444/494 [16:30<01:36,  1.94s/it]
[2025-07-31 11:21:59,079: WARNING/ForkPoolWorker-2] 
 84%|########3 | 414/494 [16:25<02:40,  2.01s/it]
[2025-07-31 11:21:59,288: WARNING/ForkPoolWorker-16] 
 93%|#########3| 460/494 [17:17<01:08,  2.01s/it]
[2025-07-31 11:22:00,717: WARNING/ForkPoolWorker-15] 
 82%|########1 | 404/494 [17:27<03:02,  2.02s/it]
[2025-07-31 11:22:00,963: WARNING/ForkPoolWorker-1] 
 90%|######### | 445/494 [16:32<01:36,  1.98s/it]
[2025-07-31 11:22:01,146: WARNING/ForkPoolWorker-2] 
 84%|########4 | 415/494 [16:27<02:40,  2.03s/it]
[2025-07-31 11:22:01,477: WARNING/ForkPoolWorker-16] 
 93%|#########3| 461/494 [17:19<01:07,  2.06s/it]
[2025-07-31 11:22:02,625: WARNING/ForkPoolWorker-15] 
 82%|########1 | 405/494 [17:29<02:56,  1.99s/it]
[2025-07-31 11:22:03,045: WARNING/ForkPoolWorker-1] 
 90%|######### | 446/494 [16:34<01:36,  2.01s/it]
[2025-07-31 11:22:03,351: WARNING/ForkPoolWorker-2] 
 84%|########4 | 416/494 [16:29<02:42,  2.08s/it]
[2025-07-31 11:22:03,549: WARNING/ForkPoolWorker-16] 
 94%|#########3| 462/494 [17:21<01:06,  2.06s/it]
[2025-07-31 11:22:04,535: WARNING/ForkPoolWorker-15] 
 82%|########2 | 406/494 [17:31<02:52,  1.97s/it]
[2025-07-31 11:22:05,036: WARNING/ForkPoolWorker-1] 
 90%|######### | 447/494 [16:36<01:34,  2.00s/it]
[2025-07-31 11:22:05,395: WARNING/ForkPoolWorker-2] 
 84%|########4 | 417/494 [16:31<02:39,  2.07s/it]
[2025-07-31 11:22:05,608: WARNING/ForkPoolWorker-16] 
 94%|#########3| 463/494 [17:24<01:03,  2.06s/it]
[2025-07-31 11:22:06,511: WARNING/ForkPoolWorker-15] 
 82%|########2 | 407/494 [17:33<02:51,  1.97s/it]
[2025-07-31 11:22:07,133: WARNING/ForkPoolWorker-1] 
 91%|######### | 448/494 [16:38<01:33,  2.03s/it]
[2025-07-31 11:22:07,497: WARNING/ForkPoolWorker-2] 
 85%|########4 | 418/494 [16:33<02:38,  2.08s/it]
[2025-07-31 11:22:07,545: WARNING/ForkPoolWorker-16] 
 94%|#########3| 464/494 [17:25<01:00,  2.03s/it]
[2025-07-31 11:22:08,682: WARNING/ForkPoolWorker-15] 
 83%|########2 | 408/494 [17:35<02:54,  2.03s/it]
[2025-07-31 11:22:09,281: WARNING/ForkPoolWorker-1] 
 91%|######### | 449/494 [16:40<01:33,  2.07s/it]
[2025-07-31 11:22:09,492: WARNING/ForkPoolWorker-2] 
 85%|########4 | 419/494 [16:35<02:34,  2.05s/it]
[2025-07-31 11:22:09,536: WARNING/ForkPoolWorker-16] 
 94%|#########4| 465/494 [17:27<00:58,  2.02s/it]
[2025-07-31 11:22:10,595: WARNING/ForkPoolWorker-15] 
 83%|########2 | 409/494 [17:37<02:49,  1.99s/it]
[2025-07-31 11:22:11,182: WARNING/ForkPoolWorker-1] 
 91%|#########1| 450/494 [16:42<01:28,  2.02s/it]
[2025-07-31 11:22:11,541: WARNING/ForkPoolWorker-16] 
 94%|#########4| 466/494 [17:29<00:56,  2.01s/it]
[2025-07-31 11:22:11,715: WARNING/ForkPoolWorker-2] 
 85%|########5 | 420/494 [16:37<02:35,  2.11s/it]
[2025-07-31 11:22:12,600: WARNING/ForkPoolWorker-15] 
 83%|########2 | 410/494 [17:39<02:47,  2.00s/it]
[2025-07-31 11:22:13,021: WARNING/ForkPoolWorker-1] 
 91%|#########1| 451/494 [16:44<01:24,  1.96s/it]
[2025-07-31 11:22:13,548: WARNING/ForkPoolWorker-16] 
 95%|#########4| 467/494 [17:31<00:54,  2.01s/it]
[2025-07-31 11:22:13,932: WARNING/ForkPoolWorker-2] 
 85%|########5 | 421/494 [16:39<02:36,  2.14s/it]
[2025-07-31 11:22:14,530: WARNING/ForkPoolWorker-15] 
 83%|########3 | 411/494 [17:41<02:44,  1.98s/it]
[2025-07-31 11:22:15,000: WARNING/ForkPoolWorker-1] 
 91%|#########1| 452/494 [16:46<01:22,  1.97s/it]
[2025-07-31 11:22:15,637: WARNING/ForkPoolWorker-16] 
 95%|#########4| 468/494 [17:34<00:52,  2.03s/it]
[2025-07-31 11:22:16,089: WARNING/ForkPoolWorker-2] 
 85%|########5 | 422/494 [16:42<02:34,  2.14s/it]
[2025-07-31 11:22:16,529: WARNING/ForkPoolWorker-15] 
 83%|########3 | 412/494 [17:43<02:42,  1.98s/it]
[2025-07-31 11:22:16,958: WARNING/ForkPoolWorker-1] 
 92%|#########1| 453/494 [16:48<01:20,  1.97s/it]
[2025-07-31 11:22:17,665: WARNING/ForkPoolWorker-16] 
 95%|#########4| 469/494 [17:36<00:50,  2.03s/it]
[2025-07-31 11:22:17,896: WARNING/ForkPoolWorker-2] 
 86%|########5 | 423/494 [16:43<02:25,  2.04s/it]
[2025-07-31 11:22:18,668: WARNING/ForkPoolWorker-15] 
 84%|########3 | 413/494 [17:45<02:44,  2.03s/it]
[2025-07-31 11:22:18,982: WARNING/ForkPoolWorker-1] 
 92%|#########1| 454/494 [16:50<01:19,  1.98s/it]
[2025-07-31 11:22:19,475: WARNING/ForkPoolWorker-16] 
 95%|#########5| 470/494 [17:37<00:47,  1.97s/it]
[2025-07-31 11:22:19,944: WARNING/ForkPoolWorker-2] 
 86%|########5 | 424/494 [16:45<02:23,  2.04s/it]
[2025-07-31 11:22:20,884: WARNING/ForkPoolWorker-1] 
 92%|#########2| 455/494 [16:52<01:16,  1.96s/it]
[2025-07-31 11:22:20,889: WARNING/ForkPoolWorker-15] 
 84%|########3 | 414/494 [17:48<02:46,  2.09s/it]
[2025-07-31 11:22:21,311: WARNING/ForkPoolWorker-16] 
 95%|#########5| 471/494 [17:39<00:44,  1.93s/it]
[2025-07-31 11:22:21,547: WARNING/ForkPoolWorker-2] 
 86%|########6 | 425/494 [16:47<02:11,  1.91s/it]
[2025-07-31 11:22:22,720: WARNING/ForkPoolWorker-1] 
 92%|#########2| 456/494 [16:54<01:13,  1.92s/it]
[2025-07-31 11:22:23,034: WARNING/ForkPoolWorker-15] 
 84%|########4 | 415/494 [17:50<02:46,  2.10s/it]
[2025-07-31 11:22:23,269: WARNING/ForkPoolWorker-16] 
 96%|#########5| 472/494 [17:41<00:42,  1.94s/it]
[2025-07-31 11:22:23,552: WARNING/ForkPoolWorker-2] 
 86%|########6 | 426/494 [16:49<02:11,  1.94s/it]
[2025-07-31 11:22:24,769: WARNING/ForkPoolWorker-1] 
 93%|#########2| 457/494 [16:56<01:12,  1.96s/it]
[2025-07-31 11:22:25,101: WARNING/ForkPoolWorker-15] 
 84%|########4 | 416/494 [17:52<02:43,  2.09s/it]
[2025-07-31 11:22:25,131: WARNING/ForkPoolWorker-16] 
 96%|#########5| 473/494 [17:43<00:40,  1.91s/it]
[2025-07-31 11:22:25,526: WARNING/ForkPoolWorker-2] 
 86%|########6 | 427/494 [16:51<02:10,  1.95s/it]
[2025-07-31 11:22:26,799: WARNING/ForkPoolWorker-1] 
 93%|#########2| 458/494 [16:58<01:11,  1.98s/it]
[2025-07-31 11:22:27,141: WARNING/ForkPoolWorker-15] 
 84%|########4 | 417/494 [17:54<02:39,  2.08s/it]
[2025-07-31 11:22:27,151: WARNING/ForkPoolWorker-16] 
 96%|#########5| 474/494 [17:45<00:38,  1.94s/it]
[2025-07-31 11:22:27,390: WARNING/ForkPoolWorker-2] 
 87%|########6 | 428/494 [16:53<02:07,  1.92s/it]
[2025-07-31 11:22:28,564: WARNING/ForkPoolWorker-1] 
 93%|#########2| 459/494 [17:00<01:07,  1.92s/it]
[2025-07-31 11:22:29,028: WARNING/ForkPoolWorker-16] 
 96%|#########6| 475/494 [17:47<00:36,  1.93s/it]
[2025-07-31 11:22:29,326: WARNING/ForkPoolWorker-15] 
 85%|########4 | 418/494 [17:56<02:40,  2.11s/it]
[2025-07-31 11:22:29,415: WARNING/ForkPoolWorker-2] 
 87%|########6 | 429/494 [16:55<02:07,  1.95s/it]
[2025-07-31 11:22:30,480: WARNING/ForkPoolWorker-1] 
 93%|#########3| 460/494 [17:02<01:05,  1.91s/it]
[2025-07-31 11:22:30,841: WARNING/ForkPoolWorker-16] 
 96%|#########6| 476/494 [17:49<00:34,  1.89s/it]
[2025-07-31 11:22:31,406: WARNING/ForkPoolWorker-15] 
 85%|########4 | 419/494 [17:58<02:37,  2.10s/it]
[2025-07-31 11:22:31,421: WARNING/ForkPoolWorker-2] 
 87%|########7 | 430/494 [16:57<02:06,  1.97s/it]
[2025-07-31 11:22:32,426: WARNING/ForkPoolWorker-1] 
 93%|#########3| 461/494 [17:04<01:03,  1.93s/it]
[2025-07-31 11:22:32,824: WARNING/ForkPoolWorker-16] 
 97%|#########6| 477/494 [17:51<00:32,  1.92s/it]
[2025-07-31 11:22:33,497: WARNING/ForkPoolWorker-15] 
 85%|########5 | 420/494 [18:00<02:35,  2.10s/it]
[2025-07-31 11:22:33,498: WARNING/ForkPoolWorker-2] 
 87%|########7 | 431/494 [16:59<02:06,  2.00s/it]
[2025-07-31 11:22:34,319: WARNING/ForkPoolWorker-1] 
 94%|#########3| 462/494 [17:05<01:01,  1.92s/it]
[2025-07-31 11:22:34,832: WARNING/ForkPoolWorker-16] 
 97%|#########6| 478/494 [17:53<00:31,  1.95s/it]
[2025-07-31 11:22:35,253: WARNING/ForkPoolWorker-2] 
 87%|########7 | 432/494 [17:01<01:59,  1.93s/it]
[2025-07-31 11:22:35,723: WARNING/ForkPoolWorker-15] 
 85%|########5 | 421/494 [18:02<02:35,  2.14s/it]
[2025-07-31 11:22:36,143: WARNING/ForkPoolWorker-1] 
 94%|#########3| 463/494 [17:07<00:58,  1.89s/it]
[2025-07-31 11:22:36,614: WARNING/ForkPoolWorker-16] 
 97%|#########6| 479/494 [17:55<00:28,  1.90s/it]
[2025-07-31 11:22:37,398: WARNING/ForkPoolWorker-2] 
 88%|########7 | 433/494 [17:03<02:01,  1.99s/it]
[2025-07-31 11:22:37,874: WARNING/ForkPoolWorker-15] 
 85%|########5 | 422/494 [18:05<02:34,  2.14s/it]
[2025-07-31 11:22:38,005: WARNING/ForkPoolWorker-1] 
 94%|#########3| 464/494 [17:09<00:56,  1.88s/it]
[2025-07-31 11:22:38,330: WARNING/ForkPoolWorker-16] 
 97%|#########7| 480/494 [17:56<00:25,  1.84s/it]
[2025-07-31 11:22:39,067: WARNING/ForkPoolWorker-2] 
 88%|########7 | 434/494 [17:05<01:53,  1.90s/it]
[2025-07-31 11:22:39,707: WARNING/ForkPoolWorker-1] 
 94%|#########4| 465/494 [17:11<00:52,  1.83s/it]
[2025-07-31 11:22:40,098: WARNING/ForkPoolWorker-16] 
 97%|#########7| 481/494 [17:58<00:23,  1.82s/it]
[2025-07-31 11:22:40,210: WARNING/ForkPoolWorker-15] 
 86%|########5 | 423/494 [18:07<02:36,  2.20s/it]
[2025-07-31 11:22:40,905: WARNING/ForkPoolWorker-2] 
 88%|########8 | 435/494 [17:06<01:50,  1.88s/it]
[2025-07-31 11:22:41,735: WARNING/ForkPoolWorker-1] 
 94%|#########4| 466/494 [17:13<00:52,  1.89s/it]
[2025-07-31 11:22:42,097: WARNING/ForkPoolWorker-16] 
 98%|#########7| 482/494 [18:00<00:22,  1.87s/it]
[2025-07-31 11:22:42,201: WARNING/ForkPoolWorker-15] 
 86%|########5 | 424/494 [18:09<02:29,  2.14s/it]
[2025-07-31 11:22:42,854: WARNING/ForkPoolWorker-2] 
 88%|########8 | 436/494 [17:08<01:50,  1.90s/it]
[2025-07-31 11:22:43,739: WARNING/ForkPoolWorker-1] 
 95%|#########4| 467/494 [17:15<00:51,  1.92s/it]
[2025-07-31 11:22:44,001: WARNING/ForkPoolWorker-16] 
 98%|#########7| 483/494 [18:02<00:20,  1.88s/it]
[2025-07-31 11:22:44,233: WARNING/ForkPoolWorker-15] 
 86%|########6 | 425/494 [18:11<02:25,  2.11s/it]
[2025-07-31 11:22:44,509: WARNING/ForkPoolWorker-2] 
 88%|########8 | 437/494 [17:10<01:44,  1.83s/it]
[2025-07-31 11:22:45,561: WARNING/ForkPoolWorker-1] 
 95%|#########4| 468/494 [17:17<00:49,  1.89s/it]
[2025-07-31 11:22:45,680: WARNING/ForkPoolWorker-16] 
 98%|#########7| 484/494 [18:04<00:18,  1.82s/it]
[2025-07-31 11:22:46,210: WARNING/ForkPoolWorker-15] 
 86%|########6 | 426/494 [18:13<02:20,  2.07s/it]
[2025-07-31 11:22:46,574: WARNING/ForkPoolWorker-2] 
 89%|########8 | 438/494 [17:12<01:46,  1.90s/it]
[2025-07-31 11:22:47,391: WARNING/ForkPoolWorker-1] 
 95%|#########4| 469/494 [17:19<00:46,  1.87s/it]
[2025-07-31 11:22:47,554: WARNING/ForkPoolWorker-16] 
 98%|#########8| 485/494 [18:05<00:16,  1.84s/it]
[2025-07-31 11:22:48,305: WARNING/ForkPoolWorker-2] 
 89%|########8 | 439/494 [17:14<01:41,  1.85s/it]
[2025-07-31 11:22:48,452: WARNING/ForkPoolWorker-15] 
 86%|########6 | 427/494 [18:15<02:22,  2.12s/it]
[2025-07-31 11:22:49,154: WARNING/ForkPoolWorker-1] 
 95%|#########5| 470/494 [17:20<00:44,  1.84s/it]
[2025-07-31 11:22:49,309: WARNING/ForkPoolWorker-16] 
 98%|#########8| 486/494 [18:07<00:14,  1.81s/it]
[2025-07-31 11:22:50,083: WARNING/ForkPoolWorker-2] 
 89%|########9 | 440/494 [17:16<01:38,  1.83s/it]
[2025-07-31 11:22:50,696: WARNING/ForkPoolWorker-15] 
 87%|########6 | 428/494 [18:17<02:22,  2.16s/it]
[2025-07-31 11:22:50,836: WARNING/ForkPoolWorker-1] 
 95%|#########5| 471/494 [17:22<00:41,  1.79s/it]
[2025-07-31 11:22:51,055: WARNING/ForkPoolWorker-16] 
 99%|#########8| 487/494 [18:09<00:12,  1.79s/it]
[2025-07-31 11:22:52,166: WARNING/ForkPoolWorker-2] 
 89%|########9 | 441/494 [17:18<01:40,  1.90s/it]
[2025-07-31 11:22:52,633: WARNING/ForkPoolWorker-1] 
 96%|#########5| 472/494 [17:24<00:39,  1.79s/it]
[2025-07-31 11:22:52,722: WARNING/ForkPoolWorker-16] 
 99%|#########8| 488/494 [18:11<00:10,  1.75s/it]
[2025-07-31 11:22:52,852: WARNING/ForkPoolWorker-15] 
 87%|########6 | 429/494 [18:20<02:20,  2.16s/it]
[2025-07-31 11:22:54,042: WARNING/ForkPoolWorker-2] 
 89%|########9 | 442/494 [17:20<01:38,  1.90s/it]
[2025-07-31 11:22:54,509: WARNING/ForkPoolWorker-1] 
 96%|#########5| 473/494 [17:26<00:38,  1.82s/it]
[2025-07-31 11:22:54,599: WARNING/ForkPoolWorker-16] 
 99%|#########8| 489/494 [18:13<00:08,  1.79s/it]
[2025-07-31 11:22:54,916: WARNING/ForkPoolWorker-15] 
 87%|########7 | 430/494 [18:22<02:16,  2.13s/it]
[2025-07-31 11:22:56,002: WARNING/ForkPoolWorker-2] 
 90%|########9 | 443/494 [17:22<01:37,  1.91s/it]
[2025-07-31 11:22:56,442: WARNING/ForkPoolWorker-16] 
 99%|#########9| 490/494 [18:14<00:07,  1.81s/it]
[2025-07-31 11:22:56,448: WARNING/ForkPoolWorker-1] 
 96%|#########5| 474/494 [17:28<00:37,  1.85s/it]
[2025-07-31 11:22:56,932: WARNING/ForkPoolWorker-15] 
 87%|########7 | 431/494 [18:24<02:11,  2.09s/it]
[2025-07-31 11:22:57,922: WARNING/ForkPoolWorker-2] 
 90%|########9 | 444/494 [17:23<01:35,  1.92s/it]
[2025-07-31 11:22:58,311: WARNING/ForkPoolWorker-1] 
 96%|#########6| 475/494 [17:29<00:35,  1.86s/it]
[2025-07-31 11:22:58,397: WARNING/ForkPoolWorker-16] 
 99%|#########9| 491/494 [18:16<00:05,  1.85s/it]
[2025-07-31 11:22:59,113: WARNING/ForkPoolWorker-15] 
 87%|########7 | 432/494 [18:26<02:11,  2.12s/it]
[2025-07-31 11:22:59,911: WARNING/ForkPoolWorker-2] 
 90%|######### | 445/494 [17:25<01:34,  1.94s/it]
[2025-07-31 11:23:00,305: WARNING/ForkPoolWorker-1] 
 96%|#########6| 476/494 [17:31<00:34,  1.90s/it]
[2025-07-31 11:23:00,370: WARNING/ForkPoolWorker-16] 
100%|#########9| 492/494 [18:18<00:03,  1.89s/it]
[2025-07-31 11:23:01,197: WARNING/ForkPoolWorker-15] 
 88%|########7 | 433/494 [18:28<02:08,  2.11s/it]
[2025-07-31 11:23:01,855: WARNING/ForkPoolWorker-2] 
 90%|######### | 446/494 [17:27<01:33,  1.94s/it]
[2025-07-31 11:23:02,344: WARNING/ForkPoolWorker-1] 
 97%|#########6| 477/494 [17:34<00:32,  1.94s/it]
[2025-07-31 11:23:02,401: WARNING/ForkPoolWorker-16] 
100%|#########9| 493/494 [18:20<00:01,  1.93s/it]
[2025-07-31 11:23:03,285: WARNING/ForkPoolWorker-15] 
 88%|########7 | 434/494 [18:30<02:06,  2.10s/it]
[2025-07-31 11:23:03,441: WARNING/ForkPoolWorker-16] 
100%|##########| 494/494 [18:21<00:00,  1.67s/it]
[2025-07-31 11:23:03,443: WARNING/ForkPoolWorker-16] 
100%|##########| 494/494 [18:21<00:00,  2.23s/it]
ffmpeg version 7.0.2-static https://johnvansickle.com/ffmpeg/  Copyright (c) 2000-2024 the FFmpeg developers
  built with gcc 8 (Debian 8.3.0-6)
  configuration: --enable-gpl --enable-version3 --enable-static --disable-debug --disable-ffplay --disable-indev=sndio --disable-outdev=sndio --cc=gcc --enable-fontconfig --enable-frei0r --enable-gnutls --enable-gmp --enable-libgme --enable-gray --enable-libaom --enable-libfribidi --enable-libass --enable-libvmaf --enable-libfreetype --enable-libmp3lame --enable-libopencore-amrnb --enable-libopencore-amrwb --enable-libopenjpeg --enable-librubberband --enable-libsoxr --enable-libspeex --enable-libsrt --enable-libvorbis --enable-libopus --enable-libtheora --enable-libvidstab --enable-libvo-amrwbenc --enable-libvpx --enable-libwebp --enable-libx264 --enable-libx265 --enable-libxml2 --enable-libdav1d --enable-libxvid --enable-libzvbi --enable-libzimg
  libavutil      59.  8.100 / 59.  8.100
  libavcodec     61.  3.100 / 61.  3.100
  libavformat    61.  1.100 / 61.  1.100
  libavdevice    61.  1.100 / 61.  1.100
  libavfilter    10.  1.100 / 10.  1.100
  libswscale      8.  1.100 /  8.  1.100
  libswresample   5.  1.100 /  5.  1.100
  libpostproc    58.  1.100 / 58.  1.100
Input #0, mov,mp4,m4a,3gp,3g2,mj2, from '/tmp/lstmsync_gpu0_4czs9q8j/temp.mp4':
  Metadata:
    major_brand     : isom
    minor_version   : 512
    compatible_brands: isomiso2mp41
    encoder         : Lavf59.27.100
  Duration: 00:01:18.96, start: 0.000000, bitrate: 1734 kb/s
  Stream #0:0[0x1](und): Video: mpeg4 (Simple Profile) (mp4v / 0x7634706D), yuv420p, 1280x1280 [SAR 1:1 DAR 1:1], 1733 kb/s, 25 fps, 25 tbr, 12800 tbn (default)
      Metadata:
        handler_name    : VideoHandler
        vendor_id       : [0][0][0][0]
[aist#1:0/pcm_s16le @ 0x1ed4d940] Guessed Channel Layout: mono
Input #1, wav, from '/tmp/lstmsync_gpu0_4czs9q8j/temp.wav':
  Metadata:
    encoder         : Lavf61.1.100
  Duration: 00:01:19.12, bitrate: 256 kb/s
  Stream #1:0: Audio: pcm_s16le ([1][0][0][0] / 0x0001), 16000 Hz, mono, s16, 256 kb/s
Stream mapping:
  Stream #0:0 -> #0:0 (mpeg4 (native) -> h264 (libx264))
  Stream #1:0 -> #0:1 (pcm_s16le (native) -> aac (native))
Press [q] to stop, [?] for help
[2025-07-31 11:23:03,699: WARNING/ForkPoolWorker-2] 
 90%|######### | 447/494 [17:29<01:29,  1.91s/it]
[aac @ 0x1eda3a00] Too many bits 12288.000000 > 6144 per frame requested, clamping to max
[libx264 @ 0x1ed5ba00] using SAR=1/1
[libx264 @ 0x1ed5ba00] using cpu capabilities: MMX2 SSE2Fast SSSE3 SSE4.2 AVX FMA3 BMI2 AVX2 AVX512
[libx264 @ 0x1ed5ba00] profile High, level 4.0, 4:2:0, 8-bit
[libx264 @ 0x1ed5ba00] 264 - core 164 r3191 4613ac3 - H.264/MPEG-4 AVC codec - Copyleft 2003-2024 - http://www.videolan.org/x264.html - options: cabac=1 ref=2 deblock=1:0:0 analyse=0x3:0x113 me=hex subme=6 psy=1 psy_rd=1.00:0.00 mixed_ref=1 me_range=16 chroma_me=1 trellis=1 8x8dct=1 cqm=0 deadzone=21,11 fast_pskip=1 chroma_qp_offset=-2 threads=40 lookahead_threads=6 sliced_threads=0 nr=0 decimate=1 interlaced=0 bluray_compat=0 constrained_intra=0 bframes=3 b_pyramid=2 b_adapt=1 b_bias=0 direct=1 weightb=1 open_gop=0 weightp=1 keyint=250 keyint_min=25 scenecut=40 intra_refresh=0 rc_lookahead=30 rc=crf mbtree=1 crf=23.0 qcomp=0.60 qpmin=0 qpmax=69 qpstep=4 ip_ratio=1.40 aq=1:1.00
Output #0, mp4, to '/tmp/lstmsync_gpu0_4czs9q8j/result.mp4':
  Metadata:
    major_brand     : isom
    minor_version   : 512
    compatible_brands: isomiso2mp41
    encoder         : Lavf61.1.100
  Stream #0:0(und): Video: h264 (avc1 / 0x31637661), yuv420p(progressive), 1280x1280 [SAR 1:1 DAR 1:1], q=2-31, 25 fps, 12800 tbn (default)
      Metadata:
        handler_name    : VideoHandler
        vendor_id       : [0][0][0][0]
        encoder         : Lavc61.3.100 libx264
      Side data:
        cpb: bitrate max/min/avg: 0/0/0 buffer size: 0 vbv_delay: N/A
  Stream #0:1: Audio: aac (LC) (mp4a / 0x6134706D), 16000 Hz, mono, fltp, 96 kb/s
      Metadata:
        encoder         : Lavc61.3.100 aac
[2025-07-31 11:23:04,125: WARNING/ForkPoolWorker-1] 
 97%|#########6| 478/494 [17:35<00:30,  1.89s/it]
frame=    0 fps=0.0 q=0.0 size=       0KiB time=N/A bitrate=N/A speed=N/A    
frame=    0 fps=0.0 q=0.0 size=       0KiB time=N/A bitrate=N/A speed=N/A    
frame=   10 fps=6.7 q=28.0 size=       0KiB time=00:00:00.32 bitrate=   1.2kbits/s speed=0.213x    
[2025-07-31 11:23:05,271: WARNING/ForkPoolWorker-2] 
 91%|######### | 448/494 [17:31<01:23,  1.81s/it]
[2025-07-31 11:23:05,291: WARNING/ForkPoolWorker-15] 
 88%|########8 | 435/494 [18:32<02:02,  2.07s/it]
frame=   39 fps= 19 q=28.0 size=       0KiB time=00:00:01.48 bitrate=   0.3kbits/s speed=0.74x    
[2025-07-31 11:23:05,836: WARNING/ForkPoolWorker-1] 
 97%|#########6| 479/494 [17:37<00:27,  1.84s/it]
frame=   75 fps= 30 q=28.0 size=     256KiB time=00:00:02.92 bitrate= 718.3kbits/s speed=1.17x    
frame=  117 fps= 39 q=28.0 size=     512KiB time=00:00:04.60 bitrate= 911.9kbits/s speed=1.53x    
[2025-07-31 11:23:06,984: WARNING/ForkPoolWorker-2] 
 91%|######### | 449/494 [17:33<01:20,  1.78s/it]
frame=  158 fps= 45 q=28.0 size=     768KiB time=00:00:05.76 bitrate=1092.3kbits/s speed=1.64x    
[2025-07-31 11:23:07,317: WARNING/ForkPoolWorker-15] 
 88%|########8 | 436/494 [18:34<01:59,  2.06s/it]
[2025-07-31 11:23:07,484: WARNING/ForkPoolWorker-1] 
 97%|#########7| 480/494 [17:39<00:24,  1.78s/it]
frame=  193 fps= 48 q=28.0 size=    1024KiB time=00:00:07.64 bitrate=1098.0kbits/s speed=1.91x    
frame=  227 fps= 50 q=28.0 size=    1280KiB time=00:00:09.00 bitrate=1165.1kbits/s speed=   2x    
frame=  264 fps= 53 q=28.0 size=    1280KiB time=00:00:10.52 bitrate= 996.8kbits/s speed= 2.1x    
[2025-07-31 11:23:08,734: WARNING/ForkPoolWorker-2] 
 91%|#########1| 450/494 [17:34<01:17,  1.77s/it]
frame=  308 fps= 56 q=28.0 size=    1536KiB time=00:00:12.09 bitrate=1040.3kbits/s speed= 2.2x    
[2025-07-31 11:23:09,281: WARNING/ForkPoolWorker-1] 
 97%|#########7| 481/494 [17:40<00:23,  1.79s/it]
[2025-07-31 11:23:09,348: WARNING/ForkPoolWorker-15] 
 88%|########8 | 437/494 [18:36<01:56,  2.05s/it]
frame=  347 fps= 58 q=28.0 size=    1792KiB time=00:00:13.80 bitrate=1063.8kbits/s speed= 2.3x    
frame=  387 fps= 60 q=28.0 size=    2048KiB time=00:00:15.40 bitrate=1089.5kbits/s speed=2.37x    
[2025-07-31 11:23:10,586: WARNING/ForkPoolWorker-2] 
 91%|#########1| 451/494 [17:36<01:17,  1.80s/it]
frame=  421 fps= 60 q=28.0 size=    2304KiB time=00:00:16.70 bitrate=1130.0kbits/s speed=2.38x    
[2025-07-31 11:23:11,085: WARNING/ForkPoolWorker-1] 
 98%|#########7| 482/494 [17:42<00:21,  1.79s/it]
frame=  460 fps= 61 q=28.0 size=    2560KiB time=00:00:18.32 bitrate=1144.8kbits/s speed=2.44x    
[2025-07-31 11:23:11,315: WARNING/ForkPoolWorker-15] 
 89%|########8 | 438/494 [18:38<01:53,  2.03s/it]
frame=  487 fps= 61 q=28.0 size=    2560KiB time=00:00:19.40 bitrate=1081.0kbits/s speed=2.42x    
frame=  531 fps= 62 q=28.0 size=    2816KiB time=00:00:20.99 bitrate=1098.9kbits/s speed=2.47x    
[2025-07-31 11:23:12,398: WARNING/ForkPoolWorker-2] 
 91%|#########1| 452/494 [17:38<01:15,  1.79s/it]
frame=  568 fps= 63 q=28.0 size=    3072KiB time=00:00:22.64 bitrate=1111.6kbits/s speed=2.51x    
[2025-07-31 11:23:12,863: WARNING/ForkPoolWorker-1] 
 98%|#########7| 483/494 [17:44<00:19,  1.79s/it]
frame=  603 fps= 63 q=28.0 size=    3328KiB time=00:00:24.04 bitrate=1134.1kbits/s speed=2.53x    
[2025-07-31 11:23:13,218: WARNING/ForkPoolWorker-15] 
 89%|########8 | 439/494 [18:40<01:49,  1.99s/it]
frame=  651 fps= 65 q=28.0 size=    3584KiB time=00:00:25.66 bitrate=1144.0kbits/s speed=2.57x    
[2025-07-31 11:23:14,101: WARNING/ForkPoolWorker-2] 
 92%|#########1| 453/494 [17:40<01:12,  1.77s/it]
frame=  692 fps= 66 q=28.0 size=    3840KiB time=00:00:27.60 bitrate=1139.8kbits/s speed=2.63x    
[2025-07-31 11:23:14,414: WARNING/ForkPoolWorker-1] 
 98%|#########7| 484/494 [17:46<00:17,  1.72s/it]
frame=  737 fps= 67 q=28.0 size=    4096KiB time=00:00:29.40 bitrate=1141.3kbits/s speed=2.67x    
frame=  785 fps= 68 q=28.0 size=    4352KiB time=00:00:31.32 bitrate=1138.3kbits/s speed=2.72x    
[2025-07-31 11:23:15,447: WARNING/ForkPoolWorker-15] 
 89%|########9 | 440/494 [18:42<01:51,  2.06s/it]
frame=  832 fps= 69 q=28.0 size=    4608KiB time=00:00:33.20 bitrate=1137.0kbits/s speed=2.76x    
[2025-07-31 11:23:15,855: WARNING/ForkPoolWorker-2] 
 92%|#########1| 454/494 [17:41<01:10,  1.77s/it]
[2025-07-31 11:23:16,132: WARNING/ForkPoolWorker-1] 
 98%|#########8| 485/494 [17:47<00:15,  1.71s/it]
frame=  868 fps= 69 q=28.0 size=    4864KiB time=00:00:34.64 bitrate=1150.3kbits/s speed=2.77x    
frame=  905 fps= 70 q=28.0 size=    4864KiB time=00:00:36.12 bitrate=1103.2kbits/s speed=2.78x    
frame=  949 fps= 70 q=28.0 size=    5376KiB time=00:00:37.88 bitrate=1162.6kbits/s speed= 2.8x    
[2025-07-31 11:23:17,346: WARNING/ForkPoolWorker-15] 
 89%|########9 | 441/494 [18:44<01:46,  2.01s/it]
[2025-07-31 11:23:17,627: WARNING/ForkPoolWorker-2] 
 92%|#########2| 455/494 [17:43<01:09,  1.77s/it]
frame=  986 fps= 70 q=28.0 size=    5376KiB time=00:00:39.36 bitrate=1118.9kbits/s speed=2.81x    
[2025-07-31 11:23:17,989: WARNING/ForkPoolWorker-1] 
 98%|#########8| 486/494 [17:49<00:14,  1.76s/it]
frame= 1023 fps= 70 q=28.0 size=    5632KiB time=00:00:40.84 bitrate=1129.7kbits/s speed=2.81x    
frame= 1065 fps= 71 q=28.0 size=    5888KiB time=00:00:42.52 bitrate=1134.4kbits/s speed=2.83x    
frame= 1109 fps= 71 q=28.0 size=    6144KiB time=00:00:44.28 bitrate=1136.7kbits/s speed=2.85x    
[2025-07-31 11:23:19,299: WARNING/ForkPoolWorker-15] 
 89%|########9 | 442/494 [18:46<01:43,  1.99s/it]
[2025-07-31 11:23:19,568: WARNING/ForkPoolWorker-2] 
 92%|#########2| 456/494 [17:45<01:09,  1.82s/it]
frame= 1151 fps= 72 q=28.0 size=    6400KiB time=00:00:45.96 bitrate=1140.8kbits/s speed=2.87x    
[2025-07-31 11:23:19,956: WARNING/ForkPoolWorker-1] 
 99%|#########8| 487/494 [17:51<00:12,  1.82s/it]
frame= 1197 fps= 72 q=28.0 size=    6656KiB time=00:00:47.80 bitrate=1140.7kbits/s speed=2.89x    
frame= 1237 fps= 73 q=28.0 size=    6912KiB time=00:00:49.40 bitrate=1146.2kbits/s speed= 2.9x    
[2025-07-31 11:23:21,098: WARNING/ForkPoolWorker-15] 
 90%|########9 | 443/494 [18:48<01:38,  1.94s/it]
frame= 1287 fps= 73 q=28.0 size=    7168KiB time=00:00:51.40 bitrate=1142.4kbits/s speed=2.94x    
[2025-07-31 11:23:21,589: WARNING/ForkPoolWorker-2] 
 93%|#########2| 457/494 [17:47<01:09,  1.88s/it]
frame= 1325 fps= 74 q=28.0 size=    7424KiB time=00:00:52.96 bitrate=1148.4kbits/s speed=2.94x    
[2025-07-31 11:23:21,682: WARNING/ForkPoolWorker-1] 
 99%|#########8| 488/494 [17:53<00:10,  1.79s/it]
frame= 1363 fps= 74 q=28.0 size=    7680KiB time=00:00:54.44 bitrate=1155.7kbits/s speed=2.94x    
frame= 1399 fps= 74 q=28.0 size=    7680KiB time=00:00:55.92 bitrate=1125.1kbits/s speed=2.94x    
[2025-07-31 11:23:23,010: WARNING/ForkPoolWorker-15] 
 90%|########9 | 444/494 [18:50<01:36,  1.93s/it]
frame= 1437 fps= 74 q=28.0 size=    7936KiB time=00:00:57.40 bitrate=1132.6kbits/s speed=2.94x    
[2025-07-31 11:23:23,438: WARNING/ForkPoolWorker-2] 
 93%|#########2| 458/494 [17:49<01:07,  1.87s/it]
[2025-07-31 11:23:23,483: WARNING/ForkPoolWorker-1] 
 99%|#########8| 489/494 [17:55<00:08,  1.80s/it]
frame= 1475 fps= 74 q=28.0 size=    8192KiB time=00:00:58.43 bitrate=1148.5kbits/s speed=2.92x    
frame= 1501 fps= 73 q=28.0 size=    8448KiB time=00:00:59.96 bitrate=1154.2kbits/s speed=2.92x    
frame= 1544 fps= 73 q=28.0 size=    8704KiB time=00:01:01.68 bitrate=1156.0kbits/s speed=2.94x    
[2025-07-31 11:23:24,921: WARNING/ForkPoolWorker-15] 
 90%|######### | 445/494 [18:52<01:34,  1.92s/it]
frame= 1582 fps= 74 q=28.0 size=    8960KiB time=00:01:03.20 bitrate=1161.4kbits/s speed=2.94x    
[2025-07-31 11:23:25,349: WARNING/ForkPoolWorker-2] 
 93%|#########2| 459/494 [17:51<01:05,  1.88s/it]
[2025-07-31 11:23:25,491: WARNING/ForkPoolWorker-1] 
 99%|#########9| 490/494 [17:57<00:07,  1.86s/it]
frame= 1615 fps= 73 q=28.0 size=    9216KiB time=00:01:04.52 bitrate=1170.1kbits/s speed=2.93x    
frame= 1663 fps= 74 q=28.0 size=    9472KiB time=00:01:06.44 bitrate=1167.9kbits/s speed=2.95x    
frame= 1711 fps= 74 q=28.0 size=    9728KiB time=00:01:08.36 bitrate=1165.8kbits/s speed=2.97x    
[2025-07-31 11:23:26,754: WARNING/ForkPoolWorker-15] 
 90%|######### | 446/494 [18:53<01:31,  1.90s/it]
frame= 1755 fps= 75 q=28.0 size=    9984KiB time=00:01:10.12 bitrate=1166.4kbits/s speed=2.98x    
[2025-07-31 11:23:27,323: WARNING/ForkPoolWorker-2] 
 93%|#########3| 460/494 [17:53<01:04,  1.91s/it]
[2025-07-31 11:23:27,615: WARNING/ForkPoolWorker-1] 
 99%|#########9| 491/494 [17:59<00:05,  1.94s/it]
frame= 1789 fps= 74 q=28.0 size=    9984KiB time=00:01:11.52 bitrate=1143.6kbits/s speed=2.98x    
frame= 1835 fps= 75 q=28.0 size=   10240KiB time=00:01:13.32 bitrate=1144.1kbits/s speed=2.99x    
frame= 1873 fps= 75 q=28.0 size=   10496KiB time=00:01:14.68 bitrate=1151.2kbits/s speed=2.99x    
[2025-07-31 11:23:28,704: WARNING/ForkPoolWorker-15] 
 90%|######### | 447/494 [18:55<01:29,  1.91s/it]
[2025-07-31 11:23:29,114: WARNING/ForkPoolWorker-2] 
 93%|#########3| 461/494 [17:55<01:01,  1.87s/it]
frame= 1903 fps= 75 q=28.0 size=   10752KiB time=N/A bitrate=N/A speed=N/A    
[2025-07-31 11:23:29,433: WARNING/ForkPoolWorker-1] 
100%|#########9| 492/494 [18:01<00:03,  1.90s/it]
frame= 1970 fps= 76 q=-1.0 size=   11008KiB time=N/A bitrate=N/A speed=N/A    
[out#0/mp4 @ 0x1ed5a840] video:10569KiB audio:671KiB subtitle:0KiB other streams:0KiB global headers:0KiB muxing overhead: 0.437059%
frame= 1974 fps= 76 q=-1.0 Lsize=   11289KiB time=00:01:18.88 bitrate=1172.4kbits/s speed=3.03x    
[libx264 @ 0x1ed5ba00] frame I:8     Avg QP:12.63  size: 31913
[libx264 @ 0x1ed5ba00] frame P:821   Avg QP:18.06  size:  9076
[libx264 @ 0x1ed5ba00] frame B:1145  Avg QP:19.72  size:  2721
[libx264 @ 0x1ed5ba00] consecutive B-frames: 12.6% 27.7%  7.4% 52.3%
[libx264 @ 0x1ed5ba00] mb I  I16..4: 46.2% 49.6%  4.1%
[libx264 @ 0x1ed5ba00] mb P  I16..4:  2.5%  7.8%  0.1%  P16..4: 20.4%  4.4%  1.5%  0.0%  0.0%    skip:63.3%
[libx264 @ 0x1ed5ba00] mb B  I16..4:  0.6%  1.1%  0.0%  B16..8: 14.6%  1.0%  0.0%  direct: 4.0%  skip:78.6%  L0:51.4% L1:46.4% BI: 2.2%
[libx264 @ 0x1ed5ba00] 8x8 transform intra:71.4% inter:82.2%
[libx264 @ 0x1ed5ba00] coded y,uvDC,uvAC intra: 25.4% 44.2% 5.1% inter: 2.6% 7.1% 0.1%
[libx264 @ 0x1ed5ba00] i16 v,h,dc,p: 44% 33% 16%  7%
[libx264 @ 0x1ed5ba00] i8 v,h,dc,ddl,ddr,vr,hd,vl,hu: 24% 24% 47%  2%  1%  1%  1%  1%  1%
[libx264 @ 0x1ed5ba00] i4 v,h,dc,ddl,ddr,vr,hd,vl,hu: 42% 27% 15%  2%  3%  4%  3%  2%  1%
[libx264 @ 0x1ed5ba00] i8c dc,h,v,p: 51% 23% 23%  3%
[libx264 @ 0x1ed5ba00] Weighted P-Frames: Y:0.0% UV:0.0%
[libx264 @ 0x1ed5ba00] ref P L0: 75.6% 24.4%
[libx264 @ 0x1ed5ba00] ref B L0: 82.1% 17.9%
[libx264 @ 0x1ed5ba00] ref B L1: 95.6%  4.4%
[libx264 @ 0x1ed5ba00] kb/s:1096.44
[aac @ 0x1eda3a00] Qavg: 65335.871
[2025-07-31 11:23:29,716: WARNING/ForkPoolWorker-16] 正在上传文件: /tmp/lstmsync_gpu0_4czs9q8j/result.mp4
[2025-07-31 11:23:29,716: WARNING/ForkPoolWorker-16] 业务路径: video
[2025-07-31 11:23:29,716: WARNING/ForkPoolWorker-16] [PROXY] 域名 *************3 在直连列表中，跳过代理
[2025-07-31 11:23:29,716: WARNING/ForkPoolWorker-16] [UPLOAD] 使用代理: {'http': None, 'https': None}
[2025-07-31 11:23:30,364: WARNING/ForkPoolWorker-15] 
 91%|######### | 448/494 [18:57<01:24,  1.84s/it]
[2025-07-31 11:23:30,483: WARNING/ForkPoolWorker-16] 上传成功! URL: https://hsobs.cdutcm.edu.cn/bucket-zbkt-orgin/video/5678cc86111c887fba2ea9218b8b25a1.mp4?AWSAccessKeyId=S1EHPJGC6CTCM9UGD6CA&Expires=2700012210&Signature=FEdKpKq%2BL5Rb2tIuYk6Z7yf7d3Y%3D
[2025-07-31 11:23:30,490: WARNING/ForkPoolWorker-16] [CLEANUP] 临时目录已清理: /tmp/lstmsync_gpu0_4czs9q8j
[2025-07-31 11:23:30,490: WARNING/ForkPoolWorker-16] [CLEANUP] 开始清理任务 0cf463e9-8483-4eb7-b304-fd012d964351 的GPU 0 资源
[2025-07-31 11:23:30,490: WARNING/ForkPoolWorker-16] [MODEL_MANAGER] GPU 0 模型使用计数: 0
[2025-07-31 11:23:30,498: WARNING/ForkPoolWorker-16] [DEBUG] 释放GPU 0资源，当前任务数: 4
[2025-07-31 11:23:30,498: WARNING/ForkPoolWorker-16] [GPU_CLEANUP] PID 585987: 当前GPU设备: 0, 目标清理GPU: 0
[2025-07-31 11:23:30,498: WARNING/ForkPoolWorker-16] [GPU_CLEANUP] PID 585987: 成功设置GPU 0
[2025-07-31 11:23:30,574: WARNING/ForkPoolWorker-2] 
 94%|#########3| 462/494 [17:56<00:56,  1.75s/it]
[2025-07-31 11:23:30,659: WARNING/ForkPoolWorker-16] [GPU_CLEANUP] PID 585987: GPU 0缓存已清理
[2025-07-31 11:23:30,830: WARNING/ForkPoolWorker-1] 
100%|#########9| 493/494 [18:02<00:01,  1.75s/it]
[2025-07-31 11:23:31,120: WARNING/ForkPoolWorker-16] [GPU_CLEANUP] PID 585987: GPU 0 内存清理完成
[2025-07-31 11:23:31,298: WARNING/ForkPoolWorker-16] [CLEANUP] 任务 0cf463e9-8483-4eb7-b304-fd012d964351 的GPU 0 资源清理完成
[2025-07-31 11:23:31,300: INFO/ForkPoolWorker-16] Task process_video_gpu0[7c126676-1020-4fad-a58b-bdb6c8a4f624] succeeded in 1145.865093196997s: {'status': 'completed', 'result_url': 'https://hsobs.cdutcm.edu.cn/bucket-zbkt-orgin/video/5678cc86111c887fba2ea9218b8b25a1.mp4?AWSAccessKeyId=S1EHPJGC6CTCM9UGD6CA&Expires=2700012210&Signature=FEdKpKq%2BL5Rb2tIuYk6Z7yf7d3Y%3D', 'message': '任务在GPU 0上处理完成', 'gpu_id': 0, 'processing_time': 1145.0422265529633, 'updated_at': 1753932210.4833179}
[2025-07-31 11:23:31,518: WARNING/ForkPoolWorker-1] 
100%|##########| 494/494 [18:03<00:00,  1.43s/it]
[2025-07-31 11:23:31,528: WARNING/ForkPoolWorker-1] 
100%|##########| 494/494 [18:03<00:00,  2.19s/it]
ffmpeg version 7.0.2-static https://johnvansickle.com/ffmpeg/  Copyright (c) 2000-2024 the FFmpeg developers
  built with gcc 8 (Debian 8.3.0-6)
  configuration: --enable-gpl --enable-version3 --enable-static --disable-debug --disable-ffplay --disable-indev=sndio --disable-outdev=sndio --cc=gcc --enable-fontconfig --enable-frei0r --enable-gnutls --enable-gmp --enable-libgme --enable-gray --enable-libaom --enable-libfribidi --enable-libass --enable-libvmaf --enable-libfreetype --enable-libmp3lame --enable-libopencore-amrnb --enable-libopencore-amrwb --enable-libopenjpeg --enable-librubberband --enable-libsoxr --enable-libspeex --enable-libsrt --enable-libvorbis --enable-libopus --enable-libtheora --enable-libvidstab --enable-libvo-amrwbenc --enable-libvpx --enable-libwebp --enable-libx264 --enable-libx265 --enable-libxml2 --enable-libdav1d --enable-libxvid --enable-libzvbi --enable-libzimg
  libavutil      59.  8.100 / 59.  8.100
  libavcodec     61.  3.100 / 61.  3.100
  libavformat    61.  1.100 / 61.  1.100
  libavdevice    61.  1.100 / 61.  1.100
  libavfilter    10.  1.100 / 10.  1.100
  libswscale      8.  1.100 /  8.  1.100
  libswresample   5.  1.100 /  5.  1.100
  libpostproc    58.  1.100 / 58.  1.100
Input #0, mov,mp4,m4a,3gp,3g2,mj2, from '/tmp/lstmsync_gpu0_d4eghldb/temp.mp4':
  Metadata:
    major_brand     : isom
    minor_version   : 512
    compatible_brands: isomiso2mp41
    encoder         : Lavf59.27.100
  Duration: 00:01:18.96, start: 0.000000, bitrate: 1734 kb/s
  Stream #0:0[0x1](und): Video: mpeg4 (Simple Profile) (mp4v / 0x7634706D), yuv420p, 1280x1280 [SAR 1:1 DAR 1:1], 1733 kb/s, 25 fps, 25 tbr, 12800 tbn (default)
      Metadata:
        handler_name    : VideoHandler
        vendor_id       : [0][0][0][0]
[aist#1:0/pcm_s16le @ 0x32cdc940] Guessed Channel Layout: mono
Input #1, wav, from '/tmp/lstmsync_gpu0_d4eghldb/temp.wav':
  Metadata:
    encoder         : Lavf61.1.100
  Duration: 00:01:19.12, bitrate: 256 kb/s
  Stream #1:0: Audio: pcm_s16le ([1][0][0][0] / 0x0001), 16000 Hz, mono, s16, 256 kb/s
Stream mapping:
  Stream #0:0 -> #0:0 (mpeg4 (native) -> h264 (libx264))
  Stream #1:0 -> #0:1 (pcm_s16le (native) -> aac (native))
Press [q] to stop, [?] for help
[aac @ 0x32d32a00] Too many bits 12288.000000 > 6144 per frame requested, clamping to max
[libx264 @ 0x32ceaa00] using SAR=1/1
[libx264 @ 0x32ceaa00] using cpu capabilities: MMX2 SSE2Fast SSSE3 SSE4.2 AVX FMA3 BMI2 AVX2 AVX512
[libx264 @ 0x32ceaa00] profile High, level 4.0, 4:2:0, 8-bit
[libx264 @ 0x32ceaa00] 264 - core 164 r3191 4613ac3 - H.264/MPEG-4 AVC codec - Copyleft 2003-2024 - http://www.videolan.org/x264.html - options: cabac=1 ref=2 deblock=1:0:0 analyse=0x3:0x113 me=hex subme=6 psy=1 psy_rd=1.00:0.00 mixed_ref=1 me_range=16 chroma_me=1 trellis=1 8x8dct=1 cqm=0 deadzone=21,11 fast_pskip=1 chroma_qp_offset=-2 threads=40 lookahead_threads=6 sliced_threads=0 nr=0 decimate=1 interlaced=0 bluray_compat=0 constrained_intra=0 bframes=3 b_pyramid=2 b_adapt=1 b_bias=0 direct=1 weightb=1 open_gop=0 weightp=1 keyint=250 keyint_min=25 scenecut=40 intra_refresh=0 rc_lookahead=30 rc=crf mbtree=1 crf=23.0 qcomp=0.60 qpmin=0 qpmax=69 qpstep=4 ip_ratio=1.40 aq=1:1.00
Output #0, mp4, to '/tmp/lstmsync_gpu0_d4eghldb/result.mp4':
  Metadata:
    major_brand     : isom
    minor_version   : 512
    compatible_brands: isomiso2mp41
    encoder         : Lavf61.1.100
  Stream #0:0(und): Video: h264 (avc1 / 0x31637661), yuv420p(progressive), 1280x1280 [SAR 1:1 DAR 1:1], q=2-31, 25 fps, 12800 tbn (default)
      Metadata:
        handler_name    : VideoHandler
        vendor_id       : [0][0][0][0]
        encoder         : Lavc61.3.100 libx264
      Side data:
        cpb: bitrate max/min/avg: 0/0/0 buffer size: 0 vbv_delay: N/A
  Stream #0:1: Audio: aac (LC) (mp4a / 0x6134706D), 16000 Hz, mono, fltp, 96 kb/s
      Metadata:
        encoder         : Lavc61.3.100 aac
[2025-07-31 11:23:31,926: WARNING/ForkPoolWorker-2] 
 94%|#########3| 463/494 [17:57<00:50,  1.63s/it]
[2025-07-31 11:23:31,957: WARNING/ForkPoolWorker-15] 
 91%|######### | 449/494 [18:59<01:19,  1.76s/it]
frame=    0 fps=0.0 q=0.0 size=       0KiB time=N/A bitrate=N/A speed=N/A    
frame=    0 fps=0.0 q=0.0 size=       0KiB time=N/A bitrate=N/A speed=N/A    
frame=   25 fps= 17 q=28.0 size=       0KiB time=00:00:00.92 bitrate=   0.4kbits/s speed=0.613x    
[2025-07-31 11:23:33,294: WARNING/ForkPoolWorker-2] 
 94%|#########3| 464/494 [17:59<00:46,  1.55s/it]
[2025-07-31 11:23:33,308: WARNING/ForkPoolWorker-15] 
 91%|#########1| 450/494 [19:00<01:12,  1.64s/it]
frame=   69 fps= 34 q=28.0 size=     256KiB time=00:00:02.36 bitrate= 885.8kbits/s speed=1.18x    
frame=  113 fps= 45 q=28.0 size=     512KiB time=00:00:04.44 bitrate= 944.7kbits/s speed=1.78x    
frame=  165 fps= 55 q=28.0 size=     768KiB time=00:00:06.52 bitrate= 965.0kbits/s speed=2.17x    
[2025-07-31 11:23:34,794: WARNING/ForkPoolWorker-2] 
 94%|#########4| 465/494 [18:00<00:44,  1.54s/it]
[2025-07-31 11:23:34,862: WARNING/ForkPoolWorker-15] 
 91%|#########1| 451/494 [19:02<01:09,  1.61s/it]
frame=  205 fps= 59 q=28.0 size=    1024KiB time=00:00:07.74 bitrate=1083.3kbits/s speed=2.21x    
frame=  241 fps= 60 q=28.0 size=    1280KiB time=00:00:09.56 bitrate=1096.9kbits/s speed=2.39x    
frame=  285 fps= 63 q=28.0 size=    1536KiB time=00:00:11.32 bitrate=1111.6kbits/s speed=2.51x    
[2025-07-31 11:23:36,215: WARNING/ForkPoolWorker-2] 
 94%|#########4| 466/494 [18:02<00:42,  1.50s/it]
[2025-07-31 11:23:36,375: WARNING/ForkPoolWorker-15] 
 91%|#########1| 452/494 [19:03<01:06,  1.58s/it]
frame=  329 fps= 66 q=28.0 size=    1792KiB time=00:00:13.08 bitrate=1122.4kbits/s speed=2.62x    
frame=  373 fps= 68 q=28.0 size=    2048KiB time=00:00:14.84 bitrate=1130.6kbits/s speed= 2.7x    
[2025-07-31 11:23:37,500: WARNING/ForkPoolWorker-2] 
 95%|#########4| 467/494 [18:03<00:38,  1.44s/it]
frame=  419 fps= 70 q=28.0 size=    2304KiB time=00:00:16.68 bitrate=1131.6kbits/s speed=2.78x    
[2025-07-31 11:23:37,988: WARNING/ForkPoolWorker-15] 
 92%|#########1| 453/494 [19:05<01:05,  1.59s/it]
frame=  462 fps= 71 q=25.0 size=    2560KiB time=00:00:18.40 bitrate=1139.8kbits/s speed=2.83x    
[2025-07-31 11:23:38,671: WARNING/ForkPoolWorker-2] 
 95%|#########4| 468/494 [18:04<00:35,  1.36s/it]
frame=  503 fps= 72 q=28.0 size=    2816KiB time=00:00:20.04 bitrate=1151.2kbits/s speed=2.86x    
frame=  543 fps= 72 q=28.0 size=    3072KiB time=00:00:21.64 bitrate=1162.9kbits/s speed=2.88x    
[2025-07-31 11:23:39,545: WARNING/ForkPoolWorker-15] 
 92%|#########1| 454/494 [19:06<01:03,  1.58s/it]
frame=  591 fps= 74 q=28.0 size=    3328KiB time=00:00:23.56 bitrate=1157.2kbits/s speed=2.94x    
[2025-07-31 11:23:39,983: WARNING/ForkPoolWorker-2] 
 95%|#########4| 469/494 [18:05<00:33,  1.34s/it]
frame=  632 fps= 74 q=28.0 size=    3584KiB time=00:00:25.20 bitrate=1165.1kbits/s speed=2.96x    
frame=  680 fps= 76 q=28.0 size=    3840KiB time=00:00:27.12 bitrate=1159.9kbits/s speed=3.01x    
[2025-07-31 11:23:41,103: WARNING/ForkPoolWorker-15] 
 92%|#########2| 455/494 [19:08<01:01,  1.58s/it]
frame=  731 fps= 77 q=28.0 size=    4096KiB time=00:00:29.16 bitrate=1150.7kbits/s speed=3.07x    
[2025-07-31 11:23:41,276: WARNING/ForkPoolWorker-2] 
 95%|#########5| 470/494 [18:07<00:31,  1.33s/it]
frame=  779 fps= 78 q=28.0 size=    4352KiB time=00:00:31.16 bitrate=1144.2kbits/s speed=3.12x    
frame=  823 fps= 78 q=28.0 size=    4608KiB time=00:00:32.84 bitrate=1149.5kbits/s speed=3.13x    
[2025-07-31 11:23:42,610: WARNING/ForkPoolWorker-15] 
 92%|#########2| 456/494 [19:09<00:59,  1.55s/it]
frame=  868 fps= 79 q=28.0 size=    4864KiB time=00:00:34.64 bitrate=1150.3kbits/s speed=3.15x    
[2025-07-31 11:23:42,733: WARNING/ForkPoolWorker-2] 
 95%|#########5| 471/494 [18:08<00:31,  1.37s/it]
frame=  903 fps= 78 q=28.0 size=    4864KiB time=00:00:36.04 bitrate=1105.6kbits/s speed=3.13x    
frame=  936 fps= 78 q=28.0 size=    5120KiB time=00:00:37.36 bitrate=1122.7kbits/s speed=3.11x    
[2025-07-31 11:23:44,125: WARNING/ForkPoolWorker-15] 
 93%|#########2| 457/494 [19:11<00:57,  1.54s/it]
frame=  981 fps= 78 q=28.0 size=    5376KiB time=00:00:39.16 bitrate=1124.6kbits/s speed=3.13x    
[2025-07-31 11:23:44,259: WARNING/ForkPoolWorker-2] 
 96%|#########5| 472/494 [18:10<00:31,  1.41s/it]
frame= 1021 fps= 79 q=28.0 size=    5632KiB time=00:00:40.76 bitrate=1131.9kbits/s speed=3.13x    
frame= 1063 fps= 79 q=28.0 size=    5888KiB time=00:00:42.44 bitrate=1136.5kbits/s speed=3.14x    
[2025-07-31 11:23:45,607: WARNING/ForkPoolWorker-15] 
 93%|#########2| 458/494 [19:12<00:54,  1.52s/it]
frame= 1115 fps= 80 q=28.0 size=    6400KiB time=00:00:44.52 bitrate=1177.7kbits/s speed=3.18x    
[2025-07-31 11:23:45,755: WARNING/ForkPoolWorker-2] 
 96%|#########5| 473/494 [18:11<00:30,  1.44s/it]
frame= 1161 fps= 80 q=28.0 size=    6656KiB time=00:00:46.36 bitrate=1176.2kbits/s speed= 3.2x    
frame= 1209 fps= 81 q=28.0 size=    6912KiB time=00:00:48.28 bitrate=1172.8kbits/s speed=3.22x    
[2025-07-31 11:23:47,184: WARNING/ForkPoolWorker-15] 
 93%|#########2| 459/494 [19:14<00:53,  1.54s/it]
frame= 1264 fps= 82 q=28.0 size=    7168KiB time=00:00:50.48 bitrate=1163.2kbits/s speed=3.26x    
[2025-07-31 11:23:47,206: WARNING/ForkPoolWorker-2] 
 96%|#########5| 474/494 [18:13<00:28,  1.44s/it]
frame= 1310 fps= 82 q=28.0 size=    7424KiB time=00:00:52.32 bitrate=1162.4kbits/s speed=3.27x    
frame= 1356 fps= 82 q=28.0 size=    7680KiB time=00:00:54.14 bitrate=1162.0kbits/s speed=3.28x    
[2025-07-31 11:23:48,688: WARNING/ForkPoolWorker-2] 
 96%|#########6| 475/494 [18:14<00:27,  1.45s/it]
[2025-07-31 11:23:48,693: WARNING/ForkPoolWorker-15] 
 93%|#########3| 460/494 [19:15<00:52,  1.53s/it]
frame= 1399 fps= 82 q=28.0 size=    7680KiB time=00:00:55.88 bitrate=1125.9kbits/s speed=3.29x    
frame= 1438 fps= 82 q=28.0 size=    7936KiB time=00:00:57.44 bitrate=1131.8kbits/s speed=3.28x    
frame= 1478 fps= 82 q=28.0 size=    8192KiB time=00:00:59.04 bitrate=1136.7kbits/s speed=3.28x    
[2025-07-31 11:23:50,179: WARNING/ForkPoolWorker-15] 
 93%|#########3| 461/494 [19:17<00:50,  1.52s/it]
frame= 1520 fps= 82 q=28.0 size=    8448KiB time=00:01:00.72 bitrate=1139.8kbits/s speed=3.28x    
[2025-07-31 11:23:50,233: WARNING/ForkPoolWorker-2] 
 96%|#########6| 476/494 [18:16<00:26,  1.48s/it]
frame= 1559 fps= 82 q=28.0 size=    8704KiB time=00:01:02.28 bitrate=1144.9kbits/s speed=3.28x    
frame= 1607 fps= 82 q=28.0 size=    9216KiB time=00:01:04.20 bitrate=1176.0kbits/s speed=3.29x    
[2025-07-31 11:23:51,633: WARNING/ForkPoolWorker-2] 
 97%|#########6| 477/494 [18:17<00:24,  1.46s/it]
[2025-07-31 11:23:51,664: WARNING/ForkPoolWorker-15] 
 94%|#########3| 462/494 [19:18<00:48,  1.51s/it]
frame= 1650 fps= 82 q=28.0 size=    9472KiB time=00:01:05.92 bitrate=1177.1kbits/s speed=3.29x    
frame= 1702 fps= 83 q=28.0 size=    9728KiB time=00:01:08.04 bitrate=1171.3kbits/s speed=3.32x    
frame= 1753 fps= 83 q=28.0 size=    9984KiB time=00:01:10.04 bitrate=1167.8kbits/s speed=3.33x    
[2025-07-31 11:23:52,953: WARNING/ForkPoolWorker-2] 
 97%|#########6| 478/494 [18:18<00:22,  1.42s/it]
[2025-07-31 11:23:53,164: WARNING/ForkPoolWorker-15] 
 94%|#########3| 463/494 [19:20<00:46,  1.51s/it]
frame= 1801 fps= 84 q=28.0 size=   10240KiB time=00:01:11.96 bitrate=1165.7kbits/s speed=3.35x    
frame= 1847 fps= 84 q=28.0 size=   10240KiB time=00:01:13.80 bitrate=1136.7kbits/s speed=3.35x    
[2025-07-31 11:23:54,101: WARNING/ForkPoolWorker-2] 
 97%|#########6| 479/494 [18:20<00:20,  1.34s/it]
[2025-07-31 11:23:54,162: WARNING/ForkPoolWorker-15] 
 94%|#########3| 464/494 [19:21<00:40,  1.35s/it]
frame= 1885 fps= 84 q=28.0 size=   10496KiB time=00:01:15.32 bitrate=1141.6kbits/s speed=3.35x    
frame= 1957 fps= 85 q=-1.0 size=   11008KiB time=N/A bitrate=N/A speed=N/A    
[2025-07-31 11:23:54,705: WARNING/ForkPoolWorker-15] 
 94%|#########4| 465/494 [19:21<00:32,  1.11s/it]
[out#0/mp4 @ 0x32ce9840] video:10569KiB audio:671KiB subtitle:0KiB other streams:0KiB global headers:0KiB muxing overhead: 0.437059%
frame= 1974 fps= 86 q=-1.0 Lsize=   11289KiB time=00:01:18.88 bitrate=1172.4kbits/s speed=3.42x    
[libx264 @ 0x32ceaa00] frame I:8     Avg QP:12.63  size: 31913
[libx264 @ 0x32ceaa00] frame P:821   Avg QP:18.06  size:  9076
[libx264 @ 0x32ceaa00] frame B:1145  Avg QP:19.72  size:  2721
[libx264 @ 0x32ceaa00] consecutive B-frames: 12.6% 27.7%  7.4% 52.3%
[libx264 @ 0x32ceaa00] mb I  I16..4: 46.2% 49.6%  4.1%
[libx264 @ 0x32ceaa00] mb P  I16..4:  2.5%  7.8%  0.1%  P16..4: 20.4%  4.4%  1.5%  0.0%  0.0%    skip:63.3%
[libx264 @ 0x32ceaa00] mb B  I16..4:  0.6%  1.1%  0.0%  B16..8: 14.6%  1.0%  0.0%  direct: 4.0%  skip:78.6%  L0:51.4% L1:46.4% BI: 2.2%
[libx264 @ 0x32ceaa00] 8x8 transform intra:71.4% inter:82.2%
[libx264 @ 0x32ceaa00] coded y,uvDC,uvAC intra: 25.4% 44.2% 5.1% inter: 2.6% 7.1% 0.1%
[libx264 @ 0x32ceaa00] i16 v,h,dc,p: 44% 33% 16%  7%
[libx264 @ 0x32ceaa00] i8 v,h,dc,ddl,ddr,vr,hd,vl,hu: 24% 24% 47%  2%  1%  1%  1%  1%  1%
[libx264 @ 0x32ceaa00] i4 v,h,dc,ddl,ddr,vr,hd,vl,hu: 42% 27% 15%  2%  3%  4%  3%  2%  1%
[libx264 @ 0x32ceaa00] i8c dc,h,v,p: 51% 23% 23%  3%
[libx264 @ 0x32ceaa00] Weighted P-Frames: Y:0.0% UV:0.0%
[libx264 @ 0x32ceaa00] ref P L0: 75.6% 24.4%
[libx264 @ 0x32ceaa00] ref B L0: 82.1% 17.9%
[libx264 @ 0x32ceaa00] ref B L1: 95.6%  4.4%
[libx264 @ 0x32ceaa00] kb/s:1096.44
[aac @ 0x32d32a00] Qavg: 65335.871
[2025-07-31 11:23:54,804: WARNING/ForkPoolWorker-1] 正在上传文件: /tmp/lstmsync_gpu0_d4eghldb/result.mp4
[2025-07-31 11:23:54,804: WARNING/ForkPoolWorker-1] 业务路径: video
[2025-07-31 11:23:54,804: WARNING/ForkPoolWorker-1] [PROXY] 域名 *************3 在直连列表中，跳过代理
[2025-07-31 11:23:54,804: WARNING/ForkPoolWorker-1] [UPLOAD] 使用代理: {'http': None, 'https': None}
[2025-07-31 11:23:55,244: WARNING/ForkPoolWorker-2] 
 97%|#########7| 480/494 [18:21<00:17,  1.28s/it]
[2025-07-31 11:23:55,491: WARNING/ForkPoolWorker-1] 上传成功! URL: https://hsobs.cdutcm.edu.cn/bucket-zbkt-orgin/video/0536a542b89582a90882a76a3c1cfbe7.mp4?AWSAccessKeyId=S1EHPJGC6CTCM9UGD6CA&Expires=2700012235&Signature=QwJF%2B1Pq3MeER%2FoXGZBXfUZD%2BwE%3D
[2025-07-31 11:23:55,498: WARNING/ForkPoolWorker-1] [CLEANUP] 临时目录已清理: /tmp/lstmsync_gpu0_d4eghldb
[2025-07-31 11:23:55,498: WARNING/ForkPoolWorker-1] [CLEANUP] 开始清理任务 5a830ec3-6a9f-474d-bac2-3d2596841936 的GPU 0 资源
[2025-07-31 11:23:55,498: WARNING/ForkPoolWorker-1] [MODEL_MANAGER] GPU 0 模型使用计数: 0
[2025-07-31 11:23:55,499: WARNING/ForkPoolWorker-1] [DEBUG] 释放GPU 0资源，当前任务数: 3
[2025-07-31 11:23:55,500: WARNING/ForkPoolWorker-1] [GPU_CLEANUP] PID 585896: 当前GPU设备: 0, 目标清理GPU: 0
[2025-07-31 11:23:55,500: WARNING/ForkPoolWorker-1] [GPU_CLEANUP] PID 585896: 成功设置GPU 0
[2025-07-31 11:23:55,572: WARNING/ForkPoolWorker-1] [GPU_CLEANUP] PID 585896: GPU 0缓存已清理
[2025-07-31 11:23:55,895: WARNING/ForkPoolWorker-1] [GPU_CLEANUP] PID 585896: GPU 0 内存清理完成
[2025-07-31 11:23:56,009: WARNING/ForkPoolWorker-15] 
 94%|#########4| 466/494 [19:23<00:32,  1.17s/it]
[2025-07-31 11:23:56,107: WARNING/ForkPoolWorker-1] [CLEANUP] 任务 5a830ec3-6a9f-474d-bac2-3d2596841936 的GPU 0 资源清理完成
[2025-07-31 11:23:56,109: INFO/ForkPoolWorker-1] Task process_video_gpu0[78c97625-afa0-4cc0-a558-159313402881] succeeded in 1142.087723896002s: {'status': 'completed', 'result_url': 'https://hsobs.cdutcm.edu.cn/bucket-zbkt-orgin/video/0536a542b89582a90882a76a3c1cfbe7.mp4?AWSAccessKeyId=S1EHPJGC6CTCM9UGD6CA&Expires=2700012235&Signature=QwJF%2B1Pq3MeER%2FoXGZBXfUZD%2BwE%3D', 'message': '任务在GPU 0上处理完成', 'gpu_id': 0, 'processing_time': 1141.4682412147522, 'updated_at': 1753932235.4917703}
[2025-07-31 11:23:56,166: WARNING/ForkPoolWorker-2] 
 97%|#########7| 481/494 [18:22<00:15,  1.17s/it]
[2025-07-31 11:23:57,208: WARNING/ForkPoolWorker-2] 
 98%|#########7| 482/494 [18:23<00:13,  1.13s/it]
[2025-07-31 11:23:57,360: WARNING/ForkPoolWorker-15] 
 95%|#########4| 467/494 [19:24<00:33,  1.22s/it]
[2025-07-31 11:23:58,296: WARNING/ForkPoolWorker-2] 
 98%|#########7| 483/494 [18:24<00:12,  1.12s/it]
[2025-07-31 11:23:58,701: WARNING/ForkPoolWorker-15] 
 95%|#########4| 468/494 [19:25<00:32,  1.26s/it]
[2025-07-31 11:23:58,943: WARNING/ForkPoolWorker-2] 
 98%|#########7| 484/494 [18:24<00:09,  1.02it/s]
[2025-07-31 11:24:00,027: WARNING/ForkPoolWorker-2] 
 98%|#########8| 485/494 [18:26<00:09,  1.01s/it]
[2025-07-31 11:24:00,031: WARNING/ForkPoolWorker-15] 
 95%|#########4| 469/494 [19:27<00:32,  1.28s/it]
[2025-07-31 11:24:01,201: WARNING/ForkPoolWorker-2] 
 98%|#########8| 486/494 [18:27<00:08,  1.06s/it]
[2025-07-31 11:24:01,396: WARNING/ForkPoolWorker-15] 
 95%|#########5| 470/494 [19:28<00:31,  1.31s/it]
[2025-07-31 11:24:02,333: WARNING/ForkPoolWorker-2] 
 99%|#########8| 487/494 [18:28<00:07,  1.08s/it]
[2025-07-31 11:24:02,752: WARNING/ForkPoolWorker-15] 
 95%|#########5| 471/494 [19:29<00:30,  1.32s/it]
[2025-07-31 11:24:03,069: WARNING/ForkPoolWorker-2] 
 99%|#########8| 488/494 [18:29<00:05,  1.02it/s]
[2025-07-31 11:24:03,575: WARNING/ForkPoolWorker-15] 
 96%|#########5| 472/494 [19:30<00:25,  1.17s/it]
[2025-07-31 11:24:04,294: WARNING/ForkPoolWorker-2] 
 99%|#########8| 489/494 [18:30<00:05,  1.05s/it]
[2025-07-31 11:24:04,847: WARNING/ForkPoolWorker-15] 
 96%|#########5| 473/494 [19:32<00:25,  1.20s/it]
[2025-07-31 11:24:05,432: WARNING/ForkPoolWorker-2] 
 99%|#########9| 490/494 [18:31<00:04,  1.08s/it]
[2025-07-31 11:24:05,782: WARNING/ForkPoolWorker-15] 
 96%|#########5| 474/494 [19:32<00:22,  1.12s/it]
[2025-07-31 11:24:05,868: WARNING/ForkPoolWorker-2] 
 99%|#########9| 491/494 [18:31<00:02,  1.13it/s]
[2025-07-31 11:24:06,934: WARNING/ForkPoolWorker-2] 
100%|#########9| 492/494 [18:32<00:01,  1.06it/s]
[2025-07-31 11:24:07,153: WARNING/ForkPoolWorker-15] 
 96%|#########6| 475/494 [19:34<00:22,  1.20s/it]
[2025-07-31 11:24:07,991: WARNING/ForkPoolWorker-2] 
100%|#########9| 493/494 [18:34<00:00,  1.03it/s]
[2025-07-31 11:24:08,321: WARNING/ForkPoolWorker-2] 
100%|##########| 494/494 [18:34<00:00,  1.28it/s]
[2025-07-31 11:24:08,323: WARNING/ForkPoolWorker-2] 
100%|##########| 494/494 [18:34<00:00,  2.26s/it]
[2025-07-31 11:24:08,378: WARNING/ForkPoolWorker-15] 
 96%|#########6| 476/494 [19:35<00:21,  1.21s/it]
ffmpeg version 7.0.2-static https://johnvansickle.com/ffmpeg/  Copyright (c) 2000-2024 the FFmpeg developers
  built with gcc 8 (Debian 8.3.0-6)
  configuration: --enable-gpl --enable-version3 --enable-static --disable-debug --disable-ffplay --disable-indev=sndio --disable-outdev=sndio --cc=gcc --enable-fontconfig --enable-frei0r --enable-gnutls --enable-gmp --enable-libgme --enable-gray --enable-libaom --enable-libfribidi --enable-libass --enable-libvmaf --enable-libfreetype --enable-libmp3lame --enable-libopencore-amrnb --enable-libopencore-amrwb --enable-libopenjpeg --enable-librubberband --enable-libsoxr --enable-libspeex --enable-libsrt --enable-libvorbis --enable-libopus --enable-libtheora --enable-libvidstab --enable-libvo-amrwbenc --enable-libvpx --enable-libwebp --enable-libx264 --enable-libx265 --enable-libxml2 --enable-libdav1d --enable-libxvid --enable-libzvbi --enable-libzimg
  libavutil      59.  8.100 / 59.  8.100
  libavcodec     61.  3.100 / 61.  3.100
  libavformat    61.  1.100 / 61.  1.100
  libavdevice    61.  1.100 / 61.  1.100
  libavfilter    10.  1.100 / 10.  1.100
  libswscale      8.  1.100 /  8.  1.100
  libswresample   5.  1.100 /  5.  1.100
  libpostproc    58.  1.100 / 58.  1.100
Input #0, mov,mp4,m4a,3gp,3g2,mj2, from '/tmp/lstmsync_gpu0_hct5x_rq/temp.mp4':
  Metadata:
    major_brand     : isom
    minor_version   : 512
    compatible_brands: isomiso2mp41
    encoder         : Lavf59.27.100
  Duration: 00:01:18.96, start: 0.000000, bitrate: 1734 kb/s
  Stream #0:0[0x1](und): Video: mpeg4 (Simple Profile) (mp4v / 0x7634706D), yuv420p, 1280x1280 [SAR 1:1 DAR 1:1], 1733 kb/s, 25 fps, 25 tbr, 12800 tbn (default)
      Metadata:
        handler_name    : VideoHandler
        vendor_id       : [0][0][0][0]
[aist#1:0/pcm_s16le @ 0x33f48940] Guessed Channel Layout: mono
Input #1, wav, from '/tmp/lstmsync_gpu0_hct5x_rq/temp.wav':
  Metadata:
    encoder         : Lavf61.1.100
  Duration: 00:01:19.12, bitrate: 256 kb/s
  Stream #1:0: Audio: pcm_s16le ([1][0][0][0] / 0x0001), 16000 Hz, mono, s16, 256 kb/s
Stream mapping:
  Stream #0:0 -> #0:0 (mpeg4 (native) -> h264 (libx264))
  Stream #1:0 -> #0:1 (pcm_s16le (native) -> aac (native))
Press [q] to stop, [?] for help
[aac @ 0x33f9ea00] Too many bits 12288.000000 > 6144 per frame requested, clamping to max
[libx264 @ 0x33f56a00] using SAR=1/1
[libx264 @ 0x33f56a00] using cpu capabilities: MMX2 SSE2Fast SSSE3 SSE4.2 AVX FMA3 BMI2 AVX2 AVX512
[libx264 @ 0x33f56a00] profile High, level 4.0, 4:2:0, 8-bit
[libx264 @ 0x33f56a00] 264 - core 164 r3191 4613ac3 - H.264/MPEG-4 AVC codec - Copyleft 2003-2024 - http://www.videolan.org/x264.html - options: cabac=1 ref=2 deblock=1:0:0 analyse=0x3:0x113 me=hex subme=6 psy=1 psy_rd=1.00:0.00 mixed_ref=1 me_range=16 chroma_me=1 trellis=1 8x8dct=1 cqm=0 deadzone=21,11 fast_pskip=1 chroma_qp_offset=-2 threads=40 lookahead_threads=6 sliced_threads=0 nr=0 decimate=1 interlaced=0 bluray_compat=0 constrained_intra=0 bframes=3 b_pyramid=2 b_adapt=1 b_bias=0 direct=1 weightb=1 open_gop=0 weightp=1 keyint=250 keyint_min=25 scenecut=40 intra_refresh=0 rc_lookahead=30 rc=crf mbtree=1 crf=23.0 qcomp=0.60 qpmin=0 qpmax=69 qpstep=4 ip_ratio=1.40 aq=1:1.00
Output #0, mp4, to '/tmp/lstmsync_gpu0_hct5x_rq/result.mp4':
  Metadata:
    major_brand     : isom
    minor_version   : 512
    compatible_brands: isomiso2mp41
    encoder         : Lavf61.1.100
  Stream #0:0(und): Video: h264 (avc1 / 0x31637661), yuv420p(progressive), 1280x1280 [SAR 1:1 DAR 1:1], q=2-31, 25 fps, 12800 tbn (default)
      Metadata:
        handler_name    : VideoHandler
        vendor_id       : [0][0][0][0]
        encoder         : Lavc61.3.100 libx264
      Side data:
        cpb: bitrate max/min/avg: 0/0/0 buffer size: 0 vbv_delay: N/A
  Stream #0:1: Audio: aac (LC) (mp4a / 0x6134706D), 16000 Hz, mono, fltp, 96 kb/s
      Metadata:
        encoder         : Lavc61.3.100 aac
[2025-07-31 11:24:08,789: WARNING/ForkPoolWorker-15] 
 97%|#########6| 477/494 [19:35<00:16,  1.03it/s]
frame=    0 fps=0.0 q=0.0 size=       0KiB time=N/A bitrate=N/A speed=N/A    
[2025-07-31 11:24:09,098: WARNING/ForkPoolWorker-15] 
 97%|#########6| 478/494 [19:36<00:12,  1.30it/s]
frame=   10 fps= 10 q=28.0 size=       0KiB time=00:00:00.32 bitrate=   1.2kbits/s speed=0.319x    
frame=   63 fps= 42 q=28.0 size=     256KiB time=00:00:02.36 bitrate= 885.8kbits/s speed=1.57x    
[2025-07-31 11:24:10,214: WARNING/ForkPoolWorker-15] 
 97%|#########6| 479/494 [19:37<00:13,  1.14it/s]
frame=  111 fps= 55 q=28.0 size=     512KiB time=00:00:04.36 bitrate= 962.1kbits/s speed=2.18x    
frame=  164 fps= 65 q=28.0 size=     768KiB time=00:00:06.52 bitrate= 965.0kbits/s speed= 2.6x    
[2025-07-31 11:24:11,278: WARNING/ForkPoolWorker-15] 
 97%|#########7| 480/494 [19:38<00:13,  1.07it/s]
frame=  233 fps= 78 q=28.0 size=    1280KiB time=00:00:09.24 bitrate=1134.9kbits/s speed=3.08x    
frame=  285 fps= 81 q=28.0 size=    1536KiB time=00:00:11.32 bitrate=1111.6kbits/s speed=3.23x    
[2025-07-31 11:24:12,213: WARNING/ForkPoolWorker-15] 
 97%|#########7| 481/494 [19:39<00:12,  1.07it/s]
frame=  353 fps= 88 q=28.0 size=    1792KiB time=00:00:14.04 bitrate=1045.6kbits/s speed= 3.5x    
[2025-07-31 11:24:12,656: WARNING/ForkPoolWorker-15] 
 98%|#########7| 482/494 [19:39<00:09,  1.27it/s]
frame=  401 fps= 89 q=28.0 size=    2048KiB time=00:00:15.96 bitrate=1051.2kbits/s speed=3.54x    
frame=  457 fps= 91 q=28.0 size=    2560KiB time=00:00:18.20 bitrate=1152.3kbits/s speed=3.63x    
[2025-07-31 11:24:13,465: WARNING/ForkPoolWorker-15] 
 98%|#########7| 483/494 [19:40<00:08,  1.26it/s]
frame=  513 fps= 93 q=28.0 size=    2816KiB time=00:00:20.44 bitrate=1128.6kbits/s speed=3.71x    
[2025-07-31 11:24:13,970: WARNING/ForkPoolWorker-15] 
 98%|#########7| 484/494 [19:41<00:07,  1.42it/s]
[2025-07-31 11:24:14,263: WARNING/ForkPoolWorker-15] 
 98%|#########8| 485/494 [19:41<00:05,  1.72it/s]
frame=  601 fps=100 q=28.0 size=    3328KiB time=00:00:23.96 bitrate=1137.9kbits/s speed=3.99x    
frame=  666 fps=102 q=28.0 size=    3840KiB time=00:00:26.56 bitrate=1184.4kbits/s speed=4.08x    
[2025-07-31 11:24:15,080: WARNING/ForkPoolWorker-15] 
 98%|#########8| 486/494 [19:42<00:05,  1.53it/s]
frame=  757 fps=108 q=28.0 size=    4352KiB time=00:00:30.20 bitrate=1180.5kbits/s speed=4.31x    
[2025-07-31 11:24:15,476: WARNING/ForkPoolWorker-15] 
 99%|#########8| 487/494 [19:42<00:04,  1.74it/s]
[2025-07-31 11:24:15,890: WARNING/ForkPoolWorker-15] 
 99%|#########8| 488/494 [19:43<00:03,  1.90it/s]
frame=  842 fps=112 q=28.0 size=    4608KiB time=00:00:33.60 bitrate=1123.5kbits/s speed=4.48x    
[2025-07-31 11:24:16,306: WARNING/ForkPoolWorker-15] 
 99%|#########8| 489/494 [19:43<00:02,  2.03it/s]
frame=  912 fps=114 q=28.0 size=    5120KiB time=00:00:36.40 bitrate=1152.3kbits/s speed=4.55x    
[2025-07-31 11:24:16,871: WARNING/ForkPoolWorker-15] 
 99%|#########9| 490/494 [19:44<00:02,  1.94it/s]
frame=  973 fps=114 q=28.0 size=    5376KiB time=00:00:38.84 bitrate=1133.9kbits/s speed=4.56x    
[2025-07-31 11:24:17,330: WARNING/ForkPoolWorker-15] 
 99%|#########9| 491/494 [19:44<00:01,  2.01it/s]
frame= 1034 fps=115 q=28.0 size=    5888KiB time=00:00:41.28 bitrate=1168.5kbits/s speed=4.58x    
frame= 1095 fps=115 q=28.0 size=    6144KiB time=00:00:43.72 bitrate=1151.2kbits/s speed= 4.6x    
[2025-07-31 11:24:17,985: WARNING/ForkPoolWorker-15] 
100%|#########9| 492/494 [19:45<00:01,  1.83it/s]
frame= 1198 fps=120 q=28.0 size=    6656KiB time=00:00:47.84 bitrate=1139.8kbits/s speed=4.78x    
[2025-07-31 11:24:18,469: WARNING/ForkPoolWorker-15] 
100%|#########9| 493/494 [19:45<00:00,  1.90it/s]
[2025-07-31 11:24:18,847: WARNING/ForkPoolWorker-15] 
100%|##########| 494/494 [19:46<00:00,  2.07it/s]
[2025-07-31 11:24:18,850: WARNING/ForkPoolWorker-15] 
100%|##########| 494/494 [19:46<00:00,  2.40s/it]
frame= 1283 fps=122 q=28.0 size=    7168KiB time=00:00:51.24 bitrate=1146.0kbits/s speed=4.88x    
ffmpeg version 7.0.2-static https://johnvansickle.com/ffmpeg/  Copyright (c) 2000-2024 the FFmpeg developers
  built with gcc 8 (Debian 8.3.0-6)
  configuration: --enable-gpl --enable-version3 --enable-static --disable-debug --disable-ffplay --disable-indev=sndio --disable-outdev=sndio --cc=gcc --enable-fontconfig --enable-frei0r --enable-gnutls --enable-gmp --enable-libgme --enable-gray --enable-libaom --enable-libfribidi --enable-libass --enable-libvmaf --enable-libfreetype --enable-libmp3lame --enable-libopencore-amrnb --enable-libopencore-amrwb --enable-libopenjpeg --enable-librubberband --enable-libsoxr --enable-libspeex --enable-libsrt --enable-libvorbis --enable-libopus --enable-libtheora --enable-libvidstab --enable-libvo-amrwbenc --enable-libvpx --enable-libwebp --enable-libx264 --enable-libx265 --enable-libxml2 --enable-libdav1d --enable-libxvid --enable-libzvbi --enable-libzimg
  libavutil      59.  8.100 / 59.  8.100
  libavcodec     61.  3.100 / 61.  3.100
  libavformat    61.  1.100 / 61.  1.100
  libavdevice    61.  1.100 / 61.  1.100
  libavfilter    10.  1.100 / 10.  1.100
  libswscale      8.  1.100 /  8.  1.100
  libswresample   5.  1.100 /  5.  1.100
  libpostproc    58.  1.100 / 58.  1.100
Input #0, mov,mp4,m4a,3gp,3g2,mj2, from '/tmp/lstmsync_gpu0_h4993rkw/temp.mp4':
  Metadata:
    major_brand     : isom
    minor_version   : 512
    compatible_brands: isomiso2mp41
    encoder         : Lavf59.27.100
  Duration: 00:01:18.96, start: 0.000000, bitrate: 1734 kb/s
  Stream #0:0[0x1](und): Video: mpeg4 (Simple Profile) (mp4v / 0x7634706D), yuv420p, 1280x1280 [SAR 1:1 DAR 1:1], 1733 kb/s, 25 fps, 25 tbr, 12800 tbn (default)
      Metadata:
        handler_name    : VideoHandler
        vendor_id       : [0][0][0][0]
[aist#1:0/pcm_s16le @ 0x20c19940] Guessed Channel Layout: mono
Input #1, wav, from '/tmp/lstmsync_gpu0_h4993rkw/temp.wav':
  Metadata:
    encoder         : Lavf61.1.100
  Duration: 00:01:19.12, bitrate: 256 kb/s
  Stream #1:0: Audio: pcm_s16le ([1][0][0][0] / 0x0001), 16000 Hz, mono, s16, 256 kb/s
Stream mapping:
  Stream #0:0 -> #0:0 (mpeg4 (native) -> h264 (libx264))
  Stream #1:0 -> #0:1 (pcm_s16le (native) -> aac (native))
Press [q] to stop, [?] for help
[aac @ 0x20c6fa00] Too many bits 12288.000000 > 6144 per frame requested, clamping to max
[libx264 @ 0x20c27a00] using SAR=1/1
[libx264 @ 0x20c27a00] using cpu capabilities: MMX2 SSE2Fast SSSE3 SSE4.2 AVX FMA3 BMI2 AVX2 AVX512
[libx264 @ 0x20c27a00] profile High, level 4.0, 4:2:0, 8-bit
[libx264 @ 0x20c27a00] 264 - core 164 r3191 4613ac3 - H.264/MPEG-4 AVC codec - Copyleft 2003-2024 - http://www.videolan.org/x264.html - options: cabac=1 ref=2 deblock=1:0:0 analyse=0x3:0x113 me=hex subme=6 psy=1 psy_rd=1.00:0.00 mixed_ref=1 me_range=16 chroma_me=1 trellis=1 8x8dct=1 cqm=0 deadzone=21,11 fast_pskip=1 chroma_qp_offset=-2 threads=40 lookahead_threads=6 sliced_threads=0 nr=0 decimate=1 interlaced=0 bluray_compat=0 constrained_intra=0 bframes=3 b_pyramid=2 b_adapt=1 b_bias=0 direct=1 weightb=1 open_gop=0 weightp=1 keyint=250 keyint_min=25 scenecut=40 intra_refresh=0 rc_lookahead=30 rc=crf mbtree=1 crf=23.0 qcomp=0.60 qpmin=0 qpmax=69 qpstep=4 ip_ratio=1.40 aq=1:1.00
Output #0, mp4, to '/tmp/lstmsync_gpu0_h4993rkw/result.mp4':
  Metadata:
    major_brand     : isom
    minor_version   : 512
    compatible_brands: isomiso2mp41
    encoder         : Lavf61.1.100
  Stream #0:0(und): Video: h264 (avc1 / 0x31637661), yuv420p(progressive), 1280x1280 [SAR 1:1 DAR 1:1], q=2-31, 25 fps, 12800 tbn (default)
      Metadata:
        handler_name    : VideoHandler
        vendor_id       : [0][0][0][0]
        encoder         : Lavc61.3.100 libx264
      Side data:
        cpb: bitrate max/min/avg: 0/0/0 buffer size: 0 vbv_delay: N/A
  Stream #0:1: Audio: aac (LC) (mp4a / 0x6134706D), 16000 Hz, mono, fltp, 96 kb/s
      Metadata:
        encoder         : Lavc61.3.100 aac
frame= 1380 fps=125 q=28.0 size=    7680KiB time=00:00:55.12 bitrate=1141.4kbits/s speed=5.01x    
frame=    0 fps=0.0 q=0.0 size=       0KiB time=N/A bitrate=N/A speed=N/A    
frame= 1468 fps=128 q=28.0 size=    8192KiB time=00:00:58.64 bitrate=1144.4kbits/s speed= 5.1x    
frame=   69 fps= 69 q=28.0 size=     256KiB time=00:00:02.68 bitrate= 782.7kbits/s speed=2.68x    
frame= 1542 fps=128 q=28.0 size=    8704KiB time=00:01:01.60 bitrate=1157.5kbits/s speed=5.13x    
frame=  149 fps= 99 q=28.0 size=     768KiB time=00:00:05.88 bitrate=1070.0kbits/s speed=3.92x    
frame= 1626 fps=130 q=28.0 size=    9216KiB time=00:01:04.96 bitrate=1162.2kbits/s speed=5.19x    
frame=  227 fps=113 q=28.0 size=    1280KiB time=00:00:09.00 bitrate=1165.1kbits/s speed= 4.5x    
frame= 1712 fps=132 q=25.0 size=    9728KiB time=00:01:08.40 bitrate=1165.1kbits/s speed=5.26x    
frame=  313 fps=125 q=28.0 size=    1536KiB time=00:00:12.44 bitrate=1011.5kbits/s speed=4.97x    
frame= 1809 fps=134 q=28.0 size=   10240KiB time=00:01:12.28 bitrate=1160.6kbits/s speed=5.35x    
frame=  400 fps=133 q=28.0 size=    2048KiB time=00:00:15.92 bitrate=1053.9kbits/s speed=5.31x    
frame= 1932 fps=138 q=28.0 size=   10752KiB time=N/A bitrate=N/A speed=N/A    
frame=  498 fps=142 q=28.0 size=    2816KiB time=00:00:19.84 bitrate=1162.8kbits/s speed=5.67x    
[out#0/mp4 @ 0x33f55840] video:10569KiB audio:671KiB subtitle:0KiB other streams:0KiB global headers:0KiB muxing overhead: 0.437059%
frame= 1974 fps=140 q=-1.0 Lsize=   11289KiB time=00:01:18.88 bitrate=1172.4kbits/s speed=5.58x    
[libx264 @ 0x33f56a00] frame I:8     Avg QP:12.63  size: 31913
[libx264 @ 0x33f56a00] frame P:821   Avg QP:18.06  size:  9076
[libx264 @ 0x33f56a00] frame B:1145  Avg QP:19.72  size:  2721
[libx264 @ 0x33f56a00] consecutive B-frames: 12.6% 27.7%  7.4% 52.3%
[libx264 @ 0x33f56a00] mb I  I16..4: 46.2% 49.6%  4.1%
[libx264 @ 0x33f56a00] mb P  I16..4:  2.5%  7.8%  0.1%  P16..4: 20.4%  4.4%  1.5%  0.0%  0.0%    skip:63.3%
[libx264 @ 0x33f56a00] mb B  I16..4:  0.6%  1.1%  0.0%  B16..8: 14.6%  1.0%  0.0%  direct: 4.0%  skip:78.6%  L0:51.4% L1:46.4% BI: 2.2%
[libx264 @ 0x33f56a00] 8x8 transform intra:71.4% inter:82.2%
[libx264 @ 0x33f56a00] coded y,uvDC,uvAC intra: 25.4% 44.2% 5.1% inter: 2.6% 7.1% 0.1%
[libx264 @ 0x33f56a00] i16 v,h,dc,p: 44% 33% 16%  7%
[libx264 @ 0x33f56a00] i8 v,h,dc,ddl,ddr,vr,hd,vl,hu: 24% 24% 47%  2%  1%  1%  1%  1%  1%
[libx264 @ 0x33f56a00] i4 v,h,dc,ddl,ddr,vr,hd,vl,hu: 42% 27% 15%  2%  3%  4%  3%  2%  1%
[libx264 @ 0x33f56a00] i8c dc,h,v,p: 51% 23% 23%  3%
[libx264 @ 0x33f56a00] Weighted P-Frames: Y:0.0% UV:0.0%
[libx264 @ 0x33f56a00] ref P L0: 75.6% 24.4%
[libx264 @ 0x33f56a00] ref B L0: 82.1% 17.9%
[libx264 @ 0x33f56a00] ref B L1: 95.6%  4.4%
[libx264 @ 0x33f56a00] kb/s:1096.44
[aac @ 0x33f9ea00] Qavg: 65335.871
[2025-07-31 11:24:22,610: WARNING/ForkPoolWorker-2] 正在上传文件: /tmp/lstmsync_gpu0_hct5x_rq/result.mp4
[2025-07-31 11:24:22,610: WARNING/ForkPoolWorker-2] 业务路径: video
[2025-07-31 11:24:22,611: WARNING/ForkPoolWorker-2] [PROXY] 域名 *************3 在直连列表中，跳过代理
[2025-07-31 11:24:22,611: WARNING/ForkPoolWorker-2] [UPLOAD] 使用代理: {'http': None, 'https': None}
frame=  605 fps=151 q=28.0 size=    3328KiB time=00:00:24.12 bitrate=1130.3kbits/s speed=6.03x    
[2025-07-31 11:24:23,358: WARNING/ForkPoolWorker-2] 上传成功! URL: https://hsobs.cdutcm.edu.cn/bucket-zbkt-orgin/video/78ebf34120369afc668b77df8e48d2fa.mp4?AWSAccessKeyId=S1EHPJGC6CTCM9UGD6CA&Expires=2700012263&Signature=WMZBCt%2F6puQVQtuUFnad2SGYl90%3D
[2025-07-31 11:24:23,367: WARNING/ForkPoolWorker-2] [CLEANUP] 临时目录已清理: /tmp/lstmsync_gpu0_hct5x_rq
[2025-07-31 11:24:23,367: WARNING/ForkPoolWorker-2] [CLEANUP] 开始清理任务 0aa24898-d728-4fc9-b565-4acda0461c61 的GPU 0 资源
[2025-07-31 11:24:23,367: WARNING/ForkPoolWorker-2] [MODEL_MANAGER] GPU 0 模型使用计数: 0
[2025-07-31 11:24:23,368: WARNING/ForkPoolWorker-2] [DEBUG] 释放GPU 0资源，当前任务数: 2
[2025-07-31 11:24:23,369: WARNING/ForkPoolWorker-2] [GPU_CLEANUP] PID 585897: 当前GPU设备: 0, 目标清理GPU: 0
[2025-07-31 11:24:23,369: WARNING/ForkPoolWorker-2] [GPU_CLEANUP] PID 585897: 成功设置GPU 0
[2025-07-31 11:24:23,409: WARNING/ForkPoolWorker-2] [GPU_CLEANUP] PID 585897: GPU 0缓存已清理
frame=  731 fps=162 q=28.0 size=    4096KiB time=00:00:29.16 bitrate=1150.7kbits/s speed=6.48x    
[2025-07-31 11:24:23,751: WARNING/ForkPoolWorker-2] [GPU_CLEANUP] PID 585897: GPU 0 内存清理完成
[2025-07-31 11:24:23,989: WARNING/ForkPoolWorker-2] [CLEANUP] 任务 0aa24898-d728-4fc9-b565-4acda0461c61 的GPU 0 资源清理完成
frame=  845 fps=169 q=28.0 size=    4608KiB time=00:00:33.72 bitrate=1119.5kbits/s speed=6.74x    
[2025-07-31 11:24:23,994: INFO/ForkPoolWorker-2] Task process_video_gpu0[b3f105cc-170d-4774-85b0-2b5f7b8d47e8] succeeded in 1167.6591150170061s: {'status': 'completed', 'result_url': 'https://hsobs.cdutcm.edu.cn/bucket-zbkt-orgin/video/78ebf34120369afc668b77df8e48d2fa.mp4?AWSAccessKeyId=S1EHPJGC6CTCM9UGD6CA&Expires=2700012263&Signature=WMZBCt%2F6puQVQtuUFnad2SGYl90%3D', 'message': '任务在GPU 0上处理完成', 'gpu_id': 0, 'processing_time': 1167.0218560695648, 'updated_at': 1753932263.3591702}
frame=  964 fps=175 q=28.0 size=    5376KiB time=00:00:38.48 bitrate=1144.5kbits/s speed=6.99x    
frame= 1085 fps=181 q=28.0 size=    6144KiB time=00:00:43.32 bitrate=1161.9kbits/s speed=7.22x    
frame= 1238 fps=190 q=28.0 size=    6912KiB time=00:00:49.44 bitrate=1145.3kbits/s speed= 7.6x    
frame= 1383 fps=198 q=28.0 size=    7680KiB time=00:00:55.24 bitrate=1138.9kbits/s speed=7.89x    
frame= 1504 fps=200 q=28.0 size=    8448KiB time=00:01:00.08 bitrate=1151.9kbits/s speed=8.01x    
frame= 1635 fps=204 q=28.0 size=    9216KiB time=00:01:05.32 bitrate=1155.8kbits/s speed=8.16x    
frame= 1771 fps=208 q=28.0 size=    9984KiB time=00:01:10.76 bitrate=1155.9kbits/s speed=8.32x    
frame= 1900 fps=211 q=28.0 size=   10752KiB time=00:01:17.69 bitrate=1133.7kbits/s speed=8.63x    
[out#0/mp4 @ 0x20c26840] video:10569KiB audio:671KiB subtitle:0KiB other streams:0KiB global headers:0KiB muxing overhead: 0.437059%
frame= 1974 fps=215 q=-1.0 Lsize=   11289KiB time=00:01:18.88 bitrate=1172.4kbits/s speed=8.59x    
[libx264 @ 0x20c27a00] frame I:8     Avg QP:12.63  size: 31913
[libx264 @ 0x20c27a00] frame P:821   Avg QP:18.06  size:  9076
[libx264 @ 0x20c27a00] frame B:1145  Avg QP:19.72  size:  2721
[libx264 @ 0x20c27a00] consecutive B-frames: 12.6% 27.7%  7.4% 52.3%
[libx264 @ 0x20c27a00] mb I  I16..4: 46.2% 49.6%  4.1%
[libx264 @ 0x20c27a00] mb P  I16..4:  2.5%  7.8%  0.1%  P16..4: 20.4%  4.4%  1.5%  0.0%  0.0%    skip:63.3%
[libx264 @ 0x20c27a00] mb B  I16..4:  0.6%  1.1%  0.0%  B16..8: 14.6%  1.0%  0.0%  direct: 4.0%  skip:78.6%  L0:51.4% L1:46.4% BI: 2.2%
[libx264 @ 0x20c27a00] 8x8 transform intra:71.4% inter:82.2%
[libx264 @ 0x20c27a00] coded y,uvDC,uvAC intra: 25.4% 44.2% 5.1% inter: 2.6% 7.1% 0.1%
[libx264 @ 0x20c27a00] i16 v,h,dc,p: 44% 33% 16%  7%
[libx264 @ 0x20c27a00] i8 v,h,dc,ddl,ddr,vr,hd,vl,hu: 24% 24% 47%  2%  1%  1%  1%  1%  1%
[libx264 @ 0x20c27a00] i4 v,h,dc,ddl,ddr,vr,hd,vl,hu: 42% 27% 15%  2%  3%  4%  3%  2%  1%
[libx264 @ 0x20c27a00] i8c dc,h,v,p: 51% 23% 23%  3%
[libx264 @ 0x20c27a00] Weighted P-Frames: Y:0.0% UV:0.0%
[libx264 @ 0x20c27a00] ref P L0: 75.6% 24.4%
[libx264 @ 0x20c27a00] ref B L0: 82.1% 17.9%
[libx264 @ 0x20c27a00] ref B L1: 95.6%  4.4%
[libx264 @ 0x20c27a00] kb/s:1096.44
[aac @ 0x20c6fa00] Qavg: 65335.871
[2025-07-31 11:24:28,204: WARNING/ForkPoolWorker-15] 正在上传文件: /tmp/lstmsync_gpu0_h4993rkw/result.mp4
[2025-07-31 11:24:28,204: WARNING/ForkPoolWorker-15] 业务路径: video
[2025-07-31 11:24:28,204: WARNING/ForkPoolWorker-15] [PROXY] 域名 *************3 在直连列表中，跳过代理
[2025-07-31 11:24:28,204: WARNING/ForkPoolWorker-15] [UPLOAD] 使用代理: {'http': None, 'https': None}
[2025-07-31 11:24:28,869: WARNING/ForkPoolWorker-15] 上传成功! URL: https://hsobs.cdutcm.edu.cn/bucket-zbkt-orgin/video/e371aa74ed809959f3ebfb32219e7830.mp4?AWSAccessKeyId=S1EHPJGC6CTCM9UGD6CA&Expires=2700012268&Signature=eGSzkxvl2cYZ1xTsYYDa0FrAoPI%3D
[2025-07-31 11:24:28,878: WARNING/ForkPoolWorker-15] [CLEANUP] 临时目录已清理: /tmp/lstmsync_gpu0_h4993rkw
[2025-07-31 11:24:28,878: WARNING/ForkPoolWorker-15] [CLEANUP] 开始清理任务 aa86ab04-5ae1-4705-8832-f8f7d59e17c5 的GPU 0 资源
[2025-07-31 11:24:28,879: WARNING/ForkPoolWorker-15] [MODEL_MANAGER] GPU 0 模型使用计数: 0
[2025-07-31 11:24:28,879: WARNING/ForkPoolWorker-15] [DEBUG] 释放GPU 0资源，当前任务数: 1
[2025-07-31 11:24:28,880: WARNING/ForkPoolWorker-15] [GPU_CLEANUP] PID 585985: 当前GPU设备: 0, 目标清理GPU: 0
[2025-07-31 11:24:28,880: WARNING/ForkPoolWorker-15] [GPU_CLEANUP] PID 585985: 成功设置GPU 0
[2025-07-31 11:24:28,921: WARNING/ForkPoolWorker-15] [GPU_CLEANUP] PID 585985: GPU 0缓存已清理
[2025-07-31 11:24:29,156: WARNING/ForkPoolWorker-15] [GPU_CLEANUP] PID 585985: GPU 0 内存清理完成
[2025-07-31 11:24:29,341: WARNING/ForkPoolWorker-15] [CLEANUP] 任务 aa86ab04-5ae1-4705-8832-f8f7d59e17c5 的GPU 0 资源清理完成
[2025-07-31 11:24:29,343: INFO/ForkPoolWorker-15] Task process_video_gpu0[cdd90d12-1577-4d2f-92ca-9eee1e8c3044] succeeded in 1207.0344811490068s: {'status': 'completed', 'result_url': 'https://hsobs.cdutcm.edu.cn/bucket-zbkt-orgin/video/e371aa74ed809959f3ebfb32219e7830.mp4?AWSAccessKeyId=S1EHPJGC6CTCM9UGD6CA&Expires=2700012268&Signature=eGSzkxvl2cYZ1xTsYYDa0FrAoPI%3D', 'message': '任务在GPU 0上处理完成', 'gpu_id': 0, 'processing_time': 1206.5580434799194, 'updated_at': 1753932268.870053}
